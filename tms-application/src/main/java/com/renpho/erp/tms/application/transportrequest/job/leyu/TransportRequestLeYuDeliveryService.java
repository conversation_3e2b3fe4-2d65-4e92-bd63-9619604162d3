package com.renpho.erp.tms.application.transportrequest.job.leyu;

import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.apiproxy.lingxing.LingxingResponse;
import com.renpho.erp.apiproxy.lingxing.SystemAccount;
import com.renpho.erp.apiproxy.lingxing.wms.api.LxwmsInboundOrderApi;
import com.renpho.erp.apiproxy.lingxing.wms.model.inboundOrder.GetInboundOrderDetailData;
import com.renpho.erp.apiproxy.lingxing.wms.model.inboundOrder.GetInboundOrderDetailRequest;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.application.transportrequest.TransportRequestQueryService;
import com.renpho.erp.tms.application.transportrequest.job.common.TransportRequestCommonService;
import com.renpho.erp.tms.domain.inbound.InboundBusinessType;
import com.renpho.erp.tms.domain.inbound.InboundRequestHistory;
import com.renpho.erp.tms.domain.inbound.InboundStatus;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transportorder.TransportOrderStatusEnum;
import com.renpho.erp.tms.domain.transportorder.dto.TransportRequestOrderData;
import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderItemRepository;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderRepository;

import com.renpho.erp.tms.infrastructure.util.DateUtils;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

import java.util.List;
import java.util.Map;

/**
 * TR-目的仓为乐鱼-入库单签收任务.
 *
 * <AUTHOR>
 * @since 2025/7/19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportRequestLeYuDeliveryService {

    private final TransportRequestQueryService transportRequestQueryService;
    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final TransportRequestCommonService transportRequestCommonService;
    private final PushTaskService pushTaskService;

    private final PushTaskRepository pushTaskRepository;
    private final TransportRequestOrderItemRepository transportRequestOrderItemRepository;
    private final TransportRequestOrderRepository transportRequestOrderRepository;
    private final PushTaskLookup pushTaskLookup;

    private final LxwmsInboundOrderApi lxwmsInboundOrderApi;

    // 仓库类型
    private final WarehouseProviderType warehouseType = WarehouseProviderType.LEYU_XLWMS;

    /**
     * TR-目的仓为乐鱼-入库单签收任务生成.
     */
    @Lock4j(name = "transport:request:leyu:inbound:delivery:basic")
    @Transactional(rollbackFor = Exception.class)
    public void createInboundLeYu() {
        try {
            log.info("TR-目的仓为乐鱼的定时器任务开始");

            // 状态是部分签收、已派送
            List<TransportOrderStatusEnum> shipStatusList = List.of(TransportOrderStatusEnum.DELIVERED_PART, TransportOrderStatusEnum.OUT_FOR_DELIVERY);
            List<TransportRequestOrderData> trDataList = transportRequestQueryService.selectTrListByShipStatusList(shipStatusList, warehouseType.name());

            // 生成入库单推送任务
            if (trDataList != null && !trDataList.isEmpty()) {
                // 获取tr上下文信息
                Map<Integer, TransportRequest> trMap = transportRequestQueryService.getTrMapByIds(trDataList.stream().map(TransportRequestOrderData::getId).toList());

                for (TransportRequestOrderData trData : trDataList) {
                    try {
                        TransportRequest tr = trMap.get(trData.getId());
                        if (tr == null) {
                            log.error("TR-目的仓为乐鱼-入库单签收任务生成异常, tr上下文找不到, trId={}", trData.getId());
                            continue;
                        }
                        // 目的仓限制
                        if (warehouseType != tr.findWarehouseProviderType()) {
                            continue;
                        }

                        // 生成推送任务
                        transportRequestCommonService.createInboundTask(trData, warehouseType, PushTaskType.INBOUND);
                    } catch (Exception e) {
                        log.error("TR-目的仓为乐鱼-入库单签收任务异常, 入库单推送任务生成失败, trId={}", trData.getId(), e);
                    }
                }
            }

        } catch (Exception e) {
            log.error("TR-目的仓为乐鱼-入库单签收任务", e);
        }
    }


    /**
     * 执行: TR-目的仓为乐鱼-入库单签收任务生成.
     * @param trNoList tr单号集合，用于异常重试的参数触发机制
     */
    @Lock4j(name = "transport:request:leyu:inbound:delivery:do")
    @Transactional(rollbackFor = Exception.class)
    public void doingInboundLeYu(List<String> trNoList) {
        try {
            log.info("TR-目的仓为乐鱼的签收任务开始");

            // 1. load出入库单推送任务
            List<PushTask> trPushTaskList = pushTaskLookup.findByTaskType(InboundBusinessType.TR, warehouseType, PushTaskType.INBOUND, PushTaskStatus.PENDING);

            // 补充外部任务参数任务数据
            trPushTaskList = transportRequestCommonService.buildOutPushTash(trPushTaskList, trNoList, PushTaskType.INBOUND);

            // 2. 调用JD查询签收信息
            if (trPushTaskList != null && !trPushTaskList.isEmpty()) {
                Map<Integer, TransportRequest> trMap = transportRequestQueryService.getTrMap(trPushTaskList);
                for (PushTask trPushTask : trPushTaskList) {
                    TransportRequest tr = trMap.get(trPushTask.getBizId());
                    if (tr == null) {
                        log.error("TR-目的仓为乐鱼-签收任务-异常, 入库预约单不存在, trId={}", trPushTask.getBizId());
                        continue;
                    }
                    // 目的仓限制
                    if (warehouseType != tr.findWarehouseProviderType()) {
                        continue;
                    }

                    // 执行签收任务
                    pushTaskService.execute(() -> preHandlerDelivery(tr, trPushTask), trPushTask, "TR-目的仓为乐鱼-签收任务执行-异常");
                }
            }
        } catch (Exception e) {
            log.error("TR-目的仓为乐鱼-入库单签收任务执行-异常", e);
        }
    }

    @EventListener(ApplicationReadyEvent.class)
    @Transactional(rollbackFor = Exception.class)
    public void test() {
//        testDelivery();
    }

    private void testDelivery() {
        GetInboundOrderDetailRequest queryDto = new GetInboundOrderDetailRequest();
        queryDto.setInboundOrderNoList(List.of("IB004250211RT"));

        String account = "A0166RENPHO";
        SystemAccount systemAccount = new SystemAccount();
        systemAccount.setAccount(account);

        R<LingxingResponse<List<GetInboundOrderDetailData>>> result = lxwmsInboundOrderApi.getInboundOrderDetail(systemAccount, queryDto);
        if (result != null) {
            LingxingResponse<List<GetInboundOrderDetailData>> response = result.getData();
            log.info("TR-目的仓为乐鱼-签收任务执行-成功, 入库单详情={}", JSON.toJSONString(response));
        }
    }

    /**
     * 处理签收-前置
     */
    private Boolean preHandlerDelivery(TransportRequest tr, PushTask trPushTask){
        GetInboundOrderDetailRequest queryDto = new GetInboundOrderDetailRequest();
        queryDto.setInboundOrderNoList(List.of(tr.getShipmentId()));

        String account = transportRequestCommonService.getConsumerCode(tr.getDestWarehouse().getId().id());
        if (account == null) {
            log.error("TR-目的仓为乐鱼-执行入库单任务异常, 仓库编码找不到, trId={}", tr.getTrNo());
            return null;
        }
        SystemAccount systemAccount = new SystemAccount();
        systemAccount.setAccount(account);

        R<LingxingResponse<List<GetInboundOrderDetailData>>> result = lxwmsInboundOrderApi.getInboundOrderDetail(systemAccount, queryDto);
        if (result != null) {
            // 记录请求历史
            InboundRequestHistory hisRecord = inboundRequestHistoryService.add(tr, WarehouseProviderType.LEYU_XLWMS, null, queryDto, result.getData().getCode(), null, result.getData());

            if (result.isSuccess()) {
                LingxingResponse<List<GetInboundOrderDetailData>> listInstock = result.getData();
                // 签收处理
                handlerDelivery(listInstock, tr, trPushTask, hisRecord);
            }
        }
        return true;
    }

    /**
     * 处理签收
     * <br/> 目前签收策略： 只认同一个shipmentId就表示是期望的sku，不再校验返回的sku跟tr的sku是不是一个东西
     * <br/> 目前的入库单细节：一个TR只会绑定一个sku，不会绑定多个，所以返回的数组只有一个元素
     */
    private void handlerDelivery(LingxingResponse<List<GetInboundOrderDetailData>> listInstock, TransportRequest tr, PushTask trPushTask, InboundRequestHistory hisRecord) {
        if(listInstock == null || listInstock.getData().isEmpty()){
            return;
        }

        TransportRequestItem itemTr = tr.getItem();
        GetInboundOrderDetailData vo = listInstock.getData().get(0);
        GetInboundOrderDetailData.InboundSkuVO detail = vo.getInboundSkuVOList().get(0);
        if(listInstock.getData().size()>1){
            throw new RuntimeException("TR-目的仓为乐鱼-签收任务执行-异常, 一个TR只能绑定一个sku,trId = {}" + tr.getTrNo());
        }
        if(vo.getInboundSkuVOList().size()>1){
            throw new RuntimeException("TR-目的仓为乐鱼-签收任务执行-异常, 一个TR只能绑定一个sku,trId = {}" + tr.getTrNo());
        }

        // 签收数据 -> 2-收货中=部分签收，3-已收货、4-已上架=全部签收， 乐鱼不存在部分签收状态
        switch (vo.getStatus()) {
            case 2:
                // 收货中目前不处理
                break;
            case 3:
                // 已收货(签收完成)
                partSignIn(itemTr, detail, tr, vo, hisRecord, InboundStatus.WORKING);
                transportRequestOrderItemRepository.update(tr);
                break;
            case 4:
                // 已上架(签收完成)
                partSignIn(itemTr, detail, tr, vo, hisRecord, InboundStatus.FINISH);

                // 计算差异数量 = 签收数量 - 计划发货数量
                Integer diffQuantity = detail.getReceivedQuantity() - itemTr.getQuantity();
                itemTr.setReceiptDiscrepancy(diffQuantity);
                transportRequestOrderItemRepository.update(tr);

                // 同步TR单状态，有差异数量已签收，没有差异数量已完成
                transportRequestCommonService.syncTransportRequestStatus(diffQuantity, tr);

                // 维护 tr上架的生命周期
                pushTaskService.putaway(tr, PushTaskStatus.SUCCESS, 0, null);

                // 更新任务状态
                trPushTask.setStatus(PushTaskStatus.SUCCESS);
                pushTaskRepository.update(trPushTask);
                break;
            default:
                break;
        }
    }

    /**
     * 部分签收
     */
    private void partSignIn(TransportRequestItem itemTr, GetInboundOrderDetailData.InboundSkuVO detail, TransportRequest tr, GetInboundOrderDetailData vo, InboundRequestHistory hisRecord, InboundStatus inboundStatus){
        itemTr.setReceivedQuantity(detail.getReceivedQuantity());
        itemTr.setShelvedQuantity(detail.getReceivedQuantity());
        if(StringUtils.isNotBlank(vo.getReceivedEndTime())){
            // 因为没有部分签收，所以开始时间跟结束时间一致
            LocalDateTime receivedTime = DateUtils.parse(vo.getReceivedEndTime());
            tr.setReceivedTime(receivedTime);
            tr.setReceivedEndTime(receivedTime);

            // 入库单全部签收的，则TR单出运状态更新“签收完成”
            //tr.setShipStatus(TransportOrderStatusEnum.COMPLETED);
            transportRequestOrderRepository.updateById(tr);
        }

        // 记录record
        Integer receiveQty = detail.getReceivedQuantity();
        LocalDateTime signTime = LocalDateTime.now();
        LocalDateTime putWayTime = LocalDateTime.now();
        if(InboundStatus.WORKING == inboundStatus){
            signTime = tr.getReceivedTime();
            putWayTime = tr.getReceivedEndTime();
        } else if(InboundStatus.FINISH == inboundStatus){
            putWayTime = DateUtils.parse(vo.getShelfEndTime());
        }
        transportRequestCommonService.doRecord(receiveQty, receiveQty, signTime, putWayTime, tr, hisRecord, inboundStatus);
    }

}
