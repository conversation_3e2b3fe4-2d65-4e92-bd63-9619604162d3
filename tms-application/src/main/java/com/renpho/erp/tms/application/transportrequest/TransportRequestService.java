package com.renpho.erp.tms.application.transportrequest;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.log.repository.annotation.LogRecord;
import com.renpho.erp.log.repository.support.LogRecordContextHolder;
import com.renpho.erp.security.util.SecurityUtils;
import com.renpho.erp.tms.application.transportorder.TransportOrderFreightQuoteApprovalRuleService;
import com.renpho.erp.tms.application.transportorder.TransportOrderQueryService;
import com.renpho.erp.tms.application.transportorder.logger.TransportOrderLogger;
import com.renpho.erp.tms.domain.exception.BusinessException;
import com.renpho.erp.tms.domain.inbound.InboundBusinessType;
import com.renpho.erp.tms.domain.inbound.InboundRecordRepository;
import com.renpho.erp.tms.domain.orderfile.BusinessTypeEnum;
import com.renpho.erp.tms.domain.orderfile.FileTypeEnum;
import com.renpho.erp.tms.domain.orderfile.OrderFile;
import com.renpho.erp.tms.domain.orderfile.OrderFileLookup;
import com.renpho.erp.tms.domain.transportorder.*;
import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.domain.transportrequest.dto.*;
import com.renpho.erp.tms.infrastructure.common.oplog.LogModule;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.po.converter.TransportRequestConverter;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderItemRepository;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * TR单操作 service
 *
 * <AUTHOR>
 * @since 2025/5/22
 */
@Service
@Validated
@RequiredArgsConstructor
public class TransportRequestService {

    private final TransportRequestOrderRepository transportRequestOrderRepository;
    private final InboundRecordRepository inboundRecordRepository;
    private final TransportRequestQueryService transportRequestQueryService;
    private final TransportRequestOrderItemRepository transportRequestOrderItemRepository;
    private final TransportRequestOrderLookup transportRequestOrderLookup;
    private final TransportOrderFreightQuoteApprovalRuleService transportOrderFreightQuoteApprovalRuleService;
    private final TransportOrderRepository transportOrderRepository;
    private final TransportOrderStatusHistoryRepository transportOrderStatusHistoryRepository;
    private final TransportOrderLogger transportOrderLogger;
    private final OrderFileLookup orderFileLookup;
    private final TransportRequestConverter transportRequestConverter;
    private final TransportOrderQueryService transportOrderQueryService;

    @Transactional(rollbackFor = Exception.class)
    public TransportRequestId add(TransportRequest transportRequest) {
        return transportRequestOrderRepository.add(transportRequest);
    }

    @Transactional(rollbackFor = Exception.class)
    public TransportRequestId updateByPd(TransportRequest command) {
        return transportRequestOrderRepository.updateByPd(command);
    }

    @Transactional(rollbackFor = Exception.class)
    public TransportRequestId updateQcResult(TransportRequest domain) {
        Optional<TransportRequest> optional = Optional.ofNullable(domain);
        if (optional
                .map(TransportRequest::getId)
                .map(transportRequestOrderLookup::findById)
                .map(TransportRequest::getStatus)
                .isPresent()) {
            return transportRequestOrderRepository.updateQcResult(domain);
        } else {
            throw new BusinessException("error.tr.status-not-allow-update", "pdNo=" + optional.map(TransportRequest::getPdNo).orElse(""));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public TransportRequestId cancelByPd(TransportRequestId trId) {
        return transportRequestOrderRepository.cancelByPd(trId);
//        throw new BusinessException("error.tr.status-not-allow-cancel", "trId=" + trId);
    }

    @Transactional(rollbackFor = Exception.class)
    public TransportRequestId updateInboundOrder(TransportRequest command) {
        TransportRequest exist = transportRequestOrderLookup.findById(command.getId());
        if (exist.getStatus() != TransportRequestStatus.PENDING) {
            throw new BusinessException("error.tr.status-not-allow-update");
        }
        return transportRequestOrderRepository.updateInboundOrder(command);
    }

    @Transactional(rollbackFor = Exception.class)
    public void consolidation(Collection<TransportRequestId> trIds) {
        List<TransportRequest> trList = transportRequestOrderLookup.findByIds(trIds);
        transportRequestQueryService.findAssociations(trList);
        //校验TR是否可以拼柜
        checkTrListIsValid(trList, false);

        //生成TO单
        TransportOrder to = new TransportOrder(null);
        to.build(trList);
        TransportOrder newTO = SpringUtil.getBean(this.getClass()).createTO(to);

        Map<TransportRequestId, TransportRequest> oldData =
                transportRequestQueryService.findByIds(trIds).stream().collect(Collectors.toMap(TransportRequest::getId, Function.identity()));

        List<TransportRequestId> idList = transportRequestOrderRepository.consolidation(trIds, newTO.getToNo(), newTO.getId().id(), newTO.getStatus());

        Map<TransportRequestId, TransportRequest> newData =
                transportRequestQueryService.findByIds(trIds).stream().collect(Collectors.toMap(TransportRequest::getId, Function.identity()));


        transportOrderLogger.logConsolidationTR(oldData, newData, idList);
        transportOrderLogger.logCreateTO(String.valueOf(newTO.getId().id()), null, newTO);
    }

    /**
     * 校验TR是否可以拼柜
     *
     * @param trList TR单
     * @param filter 是否过滤已拼柜的数据（因为编辑的时候，tr存在已拼柜的数据，要过滤掉），true:过滤 false:不过滤
     */
    public void checkTrListIsValid(List<TransportRequest> trList, boolean filter) {
        if (trList.stream()
                .map(TransportRequest::getStatus)
                .filter(s -> filter ? !TransportRequestStatus.CONSOLIDATION.equals(s) : true)
                .anyMatch(Predicate.not(TransportRequestStatus::isConsolidatable))) {
            throw new BusinessException("error.tr.status-not-allow-consolidation");
        }

        // 初始化集合用于收集不同字段的唯一值
        Set<String> businessTypes = new HashSet<>();
        Set<String> destCountryCodes = new HashSet<>();
        Set<Integer> firstLegModeIds = new HashSet<>();
        Set<String> shippingPorts = new HashSet<>();
        Set<String> carrierTypes = new HashSet<>();

        for (TransportRequest tr : trList) {
            businessTypes.add(tr.getBusinessType());
            destCountryCodes.add(tr.getDestCountry().getCode());
            firstLegModeIds.add(tr.getFirstLegMode().getId().id());
            shippingPorts.add(tr.getShippingPort());
            carrierTypes.add(tr.getCarrierType());
        }

        // TR业务类型包含(VC或者B2B-客户)和其他类型时，不能拼柜成功
        if ((businessTypes.contains(TransportRequestType.VC.name()) || businessTypes.contains(TransportRequestType.B2B_ORDER.name())) && businessTypes.size() > 1) {
            throw new BusinessException("error.tr.business-type-not-allow-consolidation");
        }

        if (destCountryCodes.size() > 1) {
            throw new BusinessException("error.tr.dest-country-not-allow-consolidation");
        }

        if (firstLegModeIds.size() > 1) {
            throw new BusinessException("error.tr.first-leg-mode-not-allow-consolidation");
        }

        if (shippingPorts.size() > 1) {
            throw new BusinessException("error.tr.shipping-port-not-allow-consolidation");
        }

        if (carrierTypes.size() > 1) {
            throw new BusinessException("error.tr.carrier-type-not-allow-consolidation");
        }
    }

    /**
     * 创建TO
     *
     * @param to to参数
     * @return to
     */
    @Transactional(rollbackFor = Exception.class)
    public TransportOrder createTO(TransportOrder to) {
        to.initTO();
        // 判断TR是否在白名单配置中
        if (transportOrderFreightQuoteApprovalRuleService.isInWhiteList(to)) {
            to.setStatus(TransportOrderStatusEnum.BOOKING_IN_PROGRESS);
            to.setApprovalStatus(TransportOrderApprovalStatusEnum.NO_APPROVAL_NEEDED);
        } else {
            to.setStatus(TransportOrderStatusEnum.QUOTING);
        }
        TransportOrder data = transportOrderRepository.save(to);
        transportOrderStatusHistoryRepository.addHistoryStatus(data.getId().id(), data.getToNo(), data.getStatus().getValue());
        return data;
    }

    /**
     * （TO单操作费-->取消拼柜）清空TO单关联信息
     *
     * @param trIds
     */
    @Transactional(rollbackFor = Exception.class)
    public List<Integer> clearTOValue(List<TransportRequestId> trIds) {
        //清空TO单关联信息
        transportRequestOrderRepository.clearTOValue(trIds);
        List<Integer> ids = trIds.stream().map(TransportRequestId::id).toList();
        return ids;
    }

    /**
     * 更新TR数据
     *
     * @param transportRequestList TR单
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdate(List<TransportRequest> transportRequestList) {
        transportRequestOrderRepository.batchUpdate(transportRequestList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateItem(List<TransportRequest> transportRequestList) {
        transportRequestOrderItemRepository.batchUpdate(transportRequestList);
    }

    /**
     * 标记离港，并校验TO单是否都已离港，是-返回true,否-返回false
     *
     * @param transportRequestList trs
     * @param id                   toId
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateAndCheckSameShipStatus(List<TransportRequest> transportRequestList, TransportOrderId id
            , TransportOrderStatusEnum status) {
        transportRequestOrderRepository.batchUpdate(transportRequestList);

        return transportRequestOrderLookup.checkAllSameShipStatus(id, status.getValue());
    }

    public List<Integer> inspection(List<TransportRequest> domains) {
        CollectionUtil.emptyIfNull(domains).forEach(transportRequestOrderRepository::updateInspectionByTrackingNo);

        return List.of();
    }

    /**
     * TR单-签收列表
     *
     * @param request 签收请求
     * @return 签收列表
     */
    public List<TransportRequestSignInResponse> signInList(TransportRequestSignInRequest request) {
        return transportRequestOrderLookup.signInList(request);
    }

    /**
     * TR单-签收-执行
     *
     * @return 受影响条数
     */
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(name = "transport:request:sign:do")  // 手动签收属于低频操作，所以锁的粒度可以粗一些
    @LogRecord(module = LogModule.TRANSPORT_REQUEST, type = LogModule.CommonDesc.EDIT_OPERATOR, desc = LogModule.CommonDesc.EDIT_DESC)
    public Boolean signInDo(TransportRequestSignInDoListRequest request) {
        if (CollectionUtil.isEmpty(request.getSignInList())) {
            return false;
        }

        // 附件必填校验
        TransportOrderId id = new TransportOrderId(request.getSignInList().get(0).getId());
        List<OrderFile> orderFiles = orderFileLookup.findListByOrderId(id, BusinessTypeEnum.TR, FileTypeEnum.OTHER);
        if (CollectionUtil.isEmpty(orderFiles)) {
            throw new BusinessException("error.tr.sign.attachment-required");
        }

        // 查找历史记录
        Integer hisCount = inboundRecordRepository.findCount(request.getSignInList().stream().map(TransportRequestSignInDoRequest::getId).collect(Collectors.toList()), InboundBusinessType.TR);
        if (hisCount > 0) {
            throw new BusinessException("error.tr.inbound-his-count-exist");
        }

        // 1. tr记录表
        List<InboundRecordData> inboundRecordDataList = new ArrayList<>();
        for (TransportRequestSignInDoRequest doRequest : request.getSignInList()) {
            InboundRecordData inboundRecordData = InboundRecordData.from(doRequest);
            inboundRecordDataList.add(inboundRecordData);

            // 2. tr自身需要记录签收时间跟改变状态
            TransportRequest transportRequest = transportRequestOrderLookup.findById(new TransportRequestId(doRequest.getId()));
            TransportRequest oldData = transportRequestConverter.trToCopy(transportRequest);

            transportRequest.setReceivedTime(doRequest.getReceivedTime());
            transportRequest.setReceivedEndTime(doRequest.getReceivedTime());

            TransportRequestItem trItem = transportRequest.getItem();
            Integer disCount = inboundRecordData.getReceivedQuantity() - trItem.getQuantity();
            trItem.setReceivedQuantity(inboundRecordData.getReceivedQuantity());
            trItem.setReceiptDiscrepancy(disCount);
            trItem.setShelvedQuantity(inboundRecordData.getPutawayQuantity());
            trItem.setShelvedDiscrepancy(inboundRecordData.getPutawayQuantity() - trItem.getQuantity());

            // 同步TR单状态，有差异数量已签收，没有差异数量已完成
            transportRequest.setShipStatus(Objects.equals(disCount, 0) ? TransportOrderStatusEnum.COMPLETED : TransportOrderStatusEnum.DELIVERED);
            transportRequestOrderRepository.updateById(transportRequest);

            transportRequestOrderItemRepository.update(transportRequest);
            TransportOrder to = transportOrderQueryService.findById(transportRequest.getTransportOrderId()).orElseThrow(() -> new BusinessException("DATA_DOES_NOT_EXIST"));
            if (transportRequestOrderLookup.checkAllSameShipStatus(to.getId(), TransportOrderStatusEnum.COMPLETED.getValue())) {
                to.setStatus(TransportOrderStatusEnum.COMPLETED);
                transportOrderRepository.updateById(to);
                transportOrderStatusHistoryRepository.addHistoryStatus(to.getId().id(), to.getToNo(), TransportOrderStatusEnum.COMPLETED.getValue(), SecurityUtils.getUserId());
            }
            // 记录签收日志
            LogRecordContextHolder.putRecordData(String.valueOf(transportRequest.getId().id()), oldData, transportRequest);
        }

        return inboundRecordRepository.batchAdd(inboundRecordDataList);
    }

    @LogRecord(module = LogModule.TRANSPORT_REQUEST, type = LogModule.CommonDesc.EDIT_OPERATOR, desc = LogModule.TransportRequest.CANCEL_TR_DESC)
    @Transactional(rollbackFor = Exception.class)
    public void clearShipmentIdById(Integer id) {
        TransportRequest oldVal = transportRequestOrderLookup.findById(new TransportRequestId(id));
        transportRequestOrderRepository.clearShipmentIdById(id);
        TransportRequest newVal = transportRequestOrderLookup.findById(new TransportRequestId(id));
        LogRecordContextHolder.putRecordData(String.valueOf(id), oldVal, newVal);
    }


}
