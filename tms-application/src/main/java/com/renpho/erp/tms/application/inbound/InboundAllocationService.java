package com.renpho.erp.tms.application.inbound;

import com.renpho.erp.stream.utils.TraceIdUtil;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.application.transportrequest.TransportRequestQueryService;
import com.renpho.erp.tms.domain.exception.BusinessException;
import com.renpho.erp.tms.domain.exception.DingTalkWarning;
import com.renpho.erp.tms.domain.exchangerate.MonthExchangeRate;
import com.renpho.erp.tms.domain.inbound.*;
import com.renpho.erp.tms.domain.operator.Operator;
import com.renpho.erp.tms.domain.transportrequest.PushTaskStatus;
import com.renpho.erp.tms.domain.transportrequest.TransportRequest;
import com.renpho.erp.tms.infrastructure.errorhandler.SendDingTalkWhenError;
import com.renpho.erp.tms.infrastructure.persistence.inbound.po.converter.InboundAllocationConverter;
import com.renpho.erp.tms.infrastructure.remote.mdm.rate.repository.MonthExchangeRateLookup;
import com.renpho.erp.tms.infrastructure.remote.user.repository.OperatorLookup;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 入库费用分摊服务
 *
 * <AUTHOR>
 * @since 2025/7/19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InboundAllocationService {

    private static final String DEFAULT_CURRENCY_CODE = "USD";

    private final TransportRequestQueryService transportRequestQueryService;
    private final InboundAllocationLookup inboundAllocationLookup;
    private final InboundAllocationRepository inboundAllocationRepository;
    private final OperatorLookup operatorLookup;
    private final MonthExchangeRateLookup monthExchangeRateLookup;
    private final InboundAllocationConverter inboundAllocationConverter;
    private final InboundInventoryService inboundInventoryService;
    private final PushTaskService pushTaskService;

    /**
     * 处理入库记录的运费和税费分摊
     *
     * @param inboundRecord 入库记录
     */
    @Transactional(rollbackFor = Exception.class)
    @SendDingTalkWhenError
    public void allocateFreightAndTax(InboundRecord inboundRecord) {
        log.info("开始处理入库记录的费用分摊, 单据号: {}, PSKU: {}, 入库ID: {}", inboundRecord.getBizNo(), inboundRecord.getPsku(), inboundRecord.getId().id());
        // 1. 查找关联的 TR 单
        TransportRequest tr = transportRequestQueryService.findByNo(inboundRecord.getBizNo()).orElseThrow(() -> new BusinessException("error.tr.not-exist", inboundRecord.getBizNo()));

        Map<InboundAllocationType, InboundAllocation> allocations = new HashMap<>();
        try {
            // 2. 根据分摊类型计算
            for (InboundAllocationType allocationType : InboundAllocationType.values()) {
                if (Optional.ofNullable(inboundRecord.getReceivedQuantity()).filter(q -> q == 0).isPresent()) {
                    log.warn("单据号: [{}], PSKU: [{}], 入库ID: [{}], 入库数量为 0, 本次不产生分摊记录", inboundRecord.getBizNo(), inboundRecord.getPsku(), inboundRecord.getId().id());
                    continue;
                }
                // 3. 获取费用月度汇率
                BigDecimal exchangeRate = findExchangeRate(inboundRecord, tr, allocationType);
                // 3.1 获取费用金额
                BigDecimal amount = allocationType.getAmount(tr);
                // 3.2 若金额为空或小于等于 0, 本次不产生分摊记录
                if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
                    log.warn("单据号: [{}], PSKU: [{}], 入库ID: [{}], 分摊类型: [{}], 金额为 null, 本次不产生分摊记录", inboundRecord.getBizNo(), inboundRecord.getPsku(), inboundRecord.getId().id(), allocationType.name());
                    continue;
                }
                // 4. 计算费用分摊
                InboundAllocation command = calculateAllocation(inboundRecord, tr, allocationType, exchangeRate);
                // 5. 保存费用分摊
                InboundAllocation domain = inboundAllocationRepository.add(command);
                allocations.put(allocationType, domain);
                log.info("入库记录费用分摊处理完成, 单据号: {}, PSKU: {}, 分摊类型: {}", inboundRecord.getBizNo(), inboundRecord.getPsku(), allocationType.name());
                pushTaskService.allocation(domain, PushTaskStatus.SUCCESS, 0, domain.getId().toString());
            }
        } catch (Exception e) {
            pushTaskService.allocation(inboundRecord, PushTaskStatus.FAILURE, 5, "计算费用分摊失败,%n原因: [%s],%n入库记录 ID: [%s],%ntraceId: [%s]".formatted(e.getMessage(), inboundRecord.getId().toString(), TraceIdUtil.getTraceId()));
            throw new DingTalkWarning(e, "error.inbound.allocation", inboundRecord.getBizNo(), inboundRecord.getPsku());
        }

        // 本次入库和上架数量是否为 0, 若为 0 则不推送 IMS
        if (Objects.equals(inboundRecord.getPutawayQuantity(), 0) && Objects.equals(inboundRecord.getReceivedQuantity(), 0)) {
            log.warn("入库记录的入库和上架数量为 0, 本次不推送 IMS, 单据号: {}, PSKU: {}, 入库记录ID: {}", inboundRecord.getBizNo(), inboundRecord.getPsku(), inboundRecord.getId().id());
            return;
        }
        // 6. 入库上架推送 IMS
        inboundInventoryService.inbound(inboundRecord, tr, allocations.get(InboundAllocationType.FREIGHT), allocations.get(InboundAllocationType.TAX));
    }

    private BigDecimal findExchangeRate(InboundRecord inboundRecord, TransportRequest tr, InboundAllocationType allocationType) {
        BigDecimal exchangeRate;
        // 查找最早的一条分摊记录
        Optional<InboundAllocation> first = inboundAllocationLookup.findFirstByBiz(inboundRecord, allocationType);
        if (first.isEmpty()) {
            // 未找到分摊记录
            // 从 TR 单中获取币种
            String currency = allocationType.getCurrencyCode(tr);
            LocalDateTime now = LocalDateTime.now();
            if (StringUtils.equals(currency, DEFAULT_CURRENCY_CODE)) {
                // 如果币种是 USD，则汇率为 1
                exchangeRate = new BigDecimal(1);
            } else {
                // 否则从 MDM 获取汇率
                exchangeRate = monthExchangeRateLookup.get(currency, DEFAULT_CURRENCY_CODE, now)
                        .map(MonthExchangeRate::getExchangeRate)
                        .orElseThrow(() -> new BusinessException("error.month-exchange-rate.not.exist", currency, DEFAULT_CURRENCY_CODE, now, tr.getTrNo()));
            }
        } else {
            // 如果已有分摊记录，则检查 TR 单的币种是否与已有分摊记录一致
            String bizCode = tr.getEstimatedFreightCurrency().getCode();
            String existCode = first.get().getCurrency().getCode();
            if (!StringUtils.equals(bizCode, existCode)) {
                throw new BusinessException("error.tr.currency.not.match", bizCode, existCode, first.get().getId(), tr.getTrNo());
            }
            // 使用第一条分摊记录的汇率
            exchangeRate = first.get().getExchangeRate();
        }
        return exchangeRate;
    }

    /**
     * 计算费用分摊
     *
     * @param inbound        当前入库记录
     * @param tr             运输请求
     * @param allocationType 分摊类型
     * @param exchangeRate   汇率
     * @return 分摊记录
     * <AUTHOR>
     * @since 2025/7/19
     */
    private InboundAllocation calculateAllocation(InboundRecord inbound, TransportRequest tr, InboundAllocationType allocationType, BigDecimal exchangeRate) {
        // TR单总运费(税费)
        BigDecimal totalAmount = allocationType.getAmount(tr).multiply(exchangeRate);
        log.info("开始计算费用分摊, 费用类型: [{}], 业务单号: [{}], PSKU: [{}], 总金额: [{}], 汇率: [{}]", allocationType.name(), tr.getTrNo(), tr.getPsku(), totalAmount, exchangeRate);
        // 1. 计算此次签收应分摊的总金额
        Operator currentOperator = operatorLookup.findSystemUser();
        // 此次签收数量
        Integer currentQuantity = inbound.getReceivedQuantity();
        BigDecimal currentAllocationAmount;
        // 判断是否为最后一次签收
        // if (tr.isInboundFinished()) {
        // TODO 需要确认能否使用已签收数量和发货数量的对比来判断是否为最后一次签收
        if (inbound.getReceivedQuantityTotal() >= tr.getQuantity()) {
            // 签收完成
            // 计算历史已分摊金额
            BigDecimal lastAllocate = inboundAllocationLookup.findLastByBiz(tr, allocationType)
                    .map(InboundAllocation::getRemainAmount)
                    .orElse(new BigDecimal(0));
            // 本次签收数量推送的运费(税费) = TR总运费(税费) - 前面签收推送的运费(税费)
            // 取2位小数，第3位截取
            currentAllocationAmount = totalAmount.subtract(lastAllocate).setScale(2, RoundingMode.HALF_UP);
        } else {
            // 签收未完成
            // 本次签收数量推送的运费(税费) = 本次签收数量 / 发货总数量 * TR的总运费(税费)
            currentAllocationAmount = new BigDecimal(currentQuantity)
                    .divide(totalAmount, 4, RoundingMode.HALF_UP)
                    .multiply(totalAmount)
                    // 取2位小数，第3位截取
                    .setScale(2, RoundingMode.HALF_UP);
        }
        // 3. 创建分摊记录
        InboundAllocation allocation = inboundAllocationConverter.toDomain(tr, inbound, allocationType);
        inboundAllocationConverter.updateCurrency(allocation, allocationType.getCurrencyCode(tr), DEFAULT_CURRENCY_CODE, exchangeRate);
        // 更新原金额与总分摊金额
        inboundAllocationConverter.updateAmount(allocation, allocationType.getAmount(tr), totalAmount, currentAllocationAmount);
        // 更新良品与不良品分摊金额
        allocation.allocateByInventoryType(inbound);
        // 设置操作人信息
        inboundAllocationConverter.updateOperator(allocation, currentOperator);
        log.info("费用分摊计算完成, 费用类型: [{}], TR单号: [{}], PSKU: [{}], 总分摊金额: [{}], 良品金额: [{}], 不良品金额: [{}]", allocationType.name(), tr.getTrNo(), tr.getPsku(), allocation.getAllocatedAmount(), allocation.getAllocatedGoodAmount(), allocation.getAllocatedBadAmount());
        return allocation;
    }

}
