package com.renpho.erp.tms.application.inbound;

import com.alibaba.fastjson.JSON;
import com.renpho.erp.stream.utils.TraceIdUtil;
import com.renpho.erp.tms.application.transportorder.TransportOrderQueryService;
import com.renpho.erp.tms.application.transportrequest.PushTaskQueryService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.domain.exception.DingTalkWarning;
import com.renpho.erp.tms.domain.inbound.InboundAllocation;
import com.renpho.erp.tms.domain.inbound.InboundRecord;
import com.renpho.erp.tms.domain.transportorder.TransportOrder;
import com.renpho.erp.tms.domain.transportrequest.PushTask;
import com.renpho.erp.tms.domain.transportrequest.PushTaskStatus;
import com.renpho.erp.tms.domain.transportrequest.PushTaskType;
import com.renpho.erp.tms.domain.transportrequest.TransportRequest;
import com.renpho.erp.tms.infrastructure.remote.inventory.repository.InventoryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 入库上架推送IMS service
 *
 * <AUTHOR>
 * @since 2025/7/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InboundInventoryService {

    private final InventoryRepository inventoryRepository;
    private final TransportOrderQueryService transportOrderQueryService;
    private final PushTaskService pushTaskService;
    private final PushTaskQueryService pushTaskQueryService;

    /**
     * 货交承运人推送 IMS
     *
     * <AUTHOR>
     * @since 2025/7/25
     */
    public void handover(TransportOrder to) {
        for (TransportRequest tr : to.getTransportRequestList()) {
            for (PushTask task : pushTaskQueryService.findByBizAndType(tr, List.of(PushTaskType.HANDOVER))) {
                List<String> executed = pushTaskService.execute(() -> inventoryRepository.handover(tr, to), task);
                String msg = task.getStatus() == PushTaskStatus.SUCCESS ? JSON.toJSONString(executed) : task.getErrMsg();
                PushTask handover = pushTaskService.handover(tr, task.getStatus() == PushTaskStatus.SUCCESS, task.getRetryCount(), msg);
                log.info("货交承运人推送 IMS 结果: [{}], 任务 ID=[{}], TR单号=[{}]", handover.getStatus(), handover.getId(), handover.getBizNo());
            }
        }
    }

    /**
     * 离港推送 IMS
     *
     * <AUTHOR>
     * @since 2025/7/25
     */
    public void departure(TransportOrder to) {
        for (TransportRequest tr : to.getTransportRequestList()) {
            for (PushTask task : pushTaskQueryService.findByBizAndType(tr, List.of(PushTaskType.DEPARTURE))) {
                List<String> executed = pushTaskService.execute(() -> inventoryRepository.departure(tr, to), task);
                String msg = task.getStatus() == PushTaskStatus.SUCCESS ? JSON.toJSONString(executed) : task.getErrMsg();
                PushTask departure = pushTaskService.departure(tr, task.getStatus() == PushTaskStatus.SUCCESS, task.getRetryCount(), msg);
                log.info("离港推送 IMS 结果: [{}], 任务 ID=[{}], TR单号=[{}]", departure.getStatus(), departure.getId(), departure.getBizNo());
            }
        }
    }

    /**
     * 入库上架推送 IMS
     *
     * <AUTHOR>
     * @since 2025/7/25
     */
    public void inbound(InboundRecord inbound, TransportRequest tr, InboundAllocation freight, InboundAllocation tax) {
        String defaultMsgSuffix = "traceId: [%s], inboundId: [%s], freightAllocationId: [%s], taxAllocationId: [%s]".formatted(TraceIdUtil.getTraceId(), inbound.getId().toString(), freight.getId().toString(), tax.getId().toString());
        TransportOrder to = transportOrderQueryService.findById(tr.getTransportOrderId())
                .map(t -> transportOrderQueryService.findLogisticSupplierAssociations(List.of(t)))
                .stream()
                .flatMap(Collection::stream)
                .findAny()
                .orElseThrow(() -> {
                    pushTaskService.inventory(tr, PushTaskStatus.FAILURE, 5, "推送IMS失败,%n原因: TO单 [%s] 不存在,%n%s".formatted(tr.getToNo(), defaultMsgSuffix));
                    return new DingTalkWarning("error.to.not.exist", inbound.getBizNo());
                });
        PushTask task = pushTaskService.inventory(tr, PushTaskStatus.PENDING, 0, defaultMsgSuffix);
        try {
            List<String> receipts = pushTaskService.execute(() -> inventoryRepository.inbound(inbound, tr, to, freight, tax), task);
            task.setStatus(PushTaskStatus.SUCCESS);
            task.setErrMsg("推送IMS成功, 返回值: [%s],%n%s".formatted(JSON.toJSONString(receipts), defaultMsgSuffix));
            pushTaskService.update(task);
        } catch (Exception e) {
            task.setStatus(PushTaskStatus.FAILURE);
            task.setErrMsg("推送IMS失败,%n原因: %s,%n%s".formatted(e.getMessage(), defaultMsgSuffix));
            pushTaskService.update(task);
            throw e;
        }
    }
}
