package com.renpho.erp.tms.application.transportrequest.job.leyu;

import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;

import com.renpho.erp.apiproxy.lingxing.LingxingResponse;
import com.renpho.erp.apiproxy.lingxing.SystemAccount;
import com.renpho.erp.apiproxy.lingxing.wms.api.LxwmsInboundOrderApi;
import com.renpho.erp.apiproxy.lingxing.wms.api.LxwmsProductApi;
import com.renpho.erp.apiproxy.lingxing.wms.model.inboundOrder.CreateData;
import com.renpho.erp.apiproxy.lingxing.wms.model.inboundOrder.CreateRequest;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.application.transportrequest.TransportRequestQueryService;
import com.renpho.erp.tms.application.transportrequest.job.common.TransportRequestCommonService;
import com.renpho.erp.tms.domain.inbound.InboundBusinessType;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transportorder.dto.TransportRequestOrderData;
import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderRepository;

import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * TR-目的仓为乐鱼-创建入库单任务.
 *
 * <AUTHOR>
 * @since 2025/7/19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportRequestLeYuAddService {

    private final TransportRequestQueryService transportRequestQueryService;
    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final TransportRequestCommonService transportRequestCommonService;
    private final PushTaskService pushTaskService;

    private final PushTaskRepository pushTaskRepository;
    private final TransportRequestOrderRepository transportRequestOrderRepository;
    private final PushTaskLookup pushTaskLookup;

    private final LxwmsInboundOrderApi lxwmsInboundOrderApi;

    // 仓库类型
    private final WarehouseProviderType warehouseType = WarehouseProviderType.LEYU_XLWMS;

    @EventListener(ApplicationReadyEvent.class)
    @Transactional(rollbackFor = Exception.class)
    public void test() {
//         createInboundLeYu();
//        doingInboundLeYu(null);
    }

    /**
     * TR-目的仓为乐鱼-创建入库单任务生成.
     */
    @Lock4j(name = "transport:request:leyu:inbound:add:basic")
    @Transactional(rollbackFor = Exception.class)
    public void createInboundLeYu() {
        try {
            log.info("TR-目的仓为乐鱼的定时器任务开始");

            // 状态是待补充信息
            TransportRequestStatus status = TransportRequestStatus.PENDING;
            List<TransportRequestOrderData> trDataList = transportRequestQueryService.selectTrListByStatus(status.getValue(), warehouseType, PushTaskType.CREATE_INBOUND);

            // 生成入库单推送任务
            if (trDataList != null && !trDataList.isEmpty()) {
                // 获取tr上下文信息
                Map<Integer, TransportRequest> trMap = transportRequestQueryService.getTrMapByIds(trDataList.stream().map(TransportRequestOrderData::getId).toList());

                for (TransportRequestOrderData trData : trDataList) {
                    try {
                        TransportRequest tr = trMap.get(trData.getId());
                        if (tr == null) {
                            log.error("TR-目的仓为乐鱼-创建入库单任务生成异常, tr上下文找不到, trId={}", trData.getId());
                            continue;
                        }
                        // 目的仓限制
                        if (WarehouseProviderType.LEYU_XLWMS != tr.findWarehouseProviderType()) {
                            continue;
                        }

                        // 故意抛异常
                        // log.info((6/0)+"");

                        // 生成推送任务
                        transportRequestCommonService.createInboundTask(trData, warehouseType, PushTaskType.CREATE_INBOUND);
                    } catch (Exception e) {
                        log.error("TR-目的仓为乐鱼的定时器任务异常, 入库单推送任务生成失败, trId={}", trData.getId(), e);
                    }
                }
            }

        } catch (Exception e) {
            log.error("TR-目的仓为乐鱼的定时器任务异常", e);
        }
    }


    /**
     * 执行: TR-目的仓为乐鱼-入库单任务.
     * @param trNoList tr单号集合，用于异常重试的参数触发机制
     */
    @Lock4j(name = "transport:request:leyu:inbound:add:do")
    @Transactional(rollbackFor = Exception.class)
    public void doingInboundLeYu(List<String> trNoList) {
        try {
            log.info("TR-目的仓为乐鱼的入库单任务开始");

            // 1. load出入库单推送任务
            List<PushTask> trPushTaskList = pushTaskLookup.findByTaskType(InboundBusinessType.TR, warehouseType, PushTaskType.CREATE_INBOUND, PushTaskStatus.PENDING);

            // 补充外部任务参数任务数据
            trPushTaskList = transportRequestCommonService.buildOutPushTash(trPushTaskList, trNoList, PushTaskType.CREATE_INBOUND);

            // 2.调用乐鱼创建入库预约单
            if (trPushTaskList != null && !trPushTaskList.isEmpty()) {
                Map<Integer, TransportRequest> trMap = transportRequestQueryService.getTrMap(trPushTaskList);
                for (PushTask trPushTask : trPushTaskList) {
                    try {
                        // TR-目的仓为乐鱼的入库单任务异常
                        TransportRequest tr = trMap.get(trPushTask.getBizId());
                        if (tr == null) {
                            log.error("TR-目的仓为乐鱼的入库单任务异常, tr源数据找不到, trId={}", trPushTask.getBizId());
                            continue;
                        }
                        // 目的仓限制
                        if (warehouseType != tr.findWarehouseProviderType()) {
                            continue;
                        }

                        // 故意抛异常
                        // log.info((6/0)+"");

                        // 执行入库单创建
                        pushTaskService.execute(() -> deliveryInstock(tr, trPushTask), trPushTask,"TR-目的仓为乐鱼-执行入库单任务异常");
                    } catch (Exception e) {
                        log.error("TR-目的仓为乐鱼-执行入库单任务异常, trId={}", trPushTask.getBizId(), e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("TR-目的仓为乐鱼的入库单任务异常", e);
        }
    }

    /**
     * 执行入库单创建
     *
     * @param tr         tr上下文
     * @param trPushTask 入库预约单推送任务
     */
    private Boolean deliveryInstock(TransportRequest tr, PushTask trPushTask) {
        String account = transportRequestCommonService.getConsumerCode(tr.getDestWarehouse().getId().id());
        if (account == null) {
            log.error("TR-目的仓为乐鱼-执行入库单任务异常, 仓库编码找不到, trId={}", tr.getTrNo());
            return false;
        }

        String warehouseCode = transportRequestCommonService.getWarehouseCode(tr.getDestWarehouse().getId().id());
        if (warehouseCode == null) {
            log.error("TR-目的仓为乐鱼-执行入库单任务异常, 仓库编码找不到, trId={}", tr.getTrNo());
            return false;
        }

        TransportRequestItem trItem= tr.getItem();

        CreateRequest instockDto = new CreateRequest();
        // IMS仓库主数据-仓库绑定的第三方仓库编码
        instockDto.setWhCode(warehouseCode);
        instockDto.setInboundType(1);
        // 外部单号
        instockDto.setThirdOrderNo(tr.getTrNo());
        instockDto.setArrivalMode(2);

        // 入库明细
        List<CreateRequest.BoxType> boxTypeList =new ArrayList<>();
        CreateRequest.BoxType boxTypeDto = new CreateRequest.BoxType();
        boxTypeDto.setCtns(tr.getBoxQty().intValue());
        boxTypeDto.setLength(tr.getProduct().getActiveBoxSpec().getBoxLengthMetric());
        boxTypeDto.setWidth(tr.getProduct().getActiveBoxSpec().getBoxWidthMetric());
        boxTypeDto.setHeight(tr.getProduct().getActiveBoxSpec().getBoxHeightMetric());
        boxTypeDto.setWeight(tr.getProduct().getActiveBoxSpec().getGrossWeightPerBoxMetric());

        List<CreateRequest.Product> productList =new ArrayList<>();
        CreateRequest.Product productDto = new CreateRequest.Product();
        productDto.setSku(tr.getFnSku());
        productDto.setQuantity(trItem.getQuantityPerBox());
        productList.add(productDto);
        boxTypeDto.setProductList(productList);

        boxTypeList.add(boxTypeDto);
        instockDto.setBoxTypeList(boxTypeList);

        SystemAccount systemAccount = new SystemAccount();
        systemAccount.setAccount(account);
        log.info("TR-目的仓为乐鱼-执行入库单任务, 仓库编码={}, 参数={}", warehouseCode, JSON.toJSONString(instockDto));
        R<LingxingResponse<CreateData>> result = lxwmsInboundOrderApi.create(systemAccount, instockDto);
        if (result != null) {
            if (result.isSuccess()) {
                LingxingResponse<CreateData> inboundOrderResultVo = result.getData();

                // 创建成功，保存 orderNo
                tr.setShipmentId(inboundOrderResultVo.getData().getOrderNo());
                transportRequestOrderRepository.updateById(tr);

                // 3. 生成箱唛推送任务(这个跟tr生命周期重复，所以注释掉)
                // transportRequestCommonService.createCartonFile(tr, warehouseType);

                // 更新任务状态
                trPushTask.setStatus(PushTaskStatus.SUCCESS);
                pushTaskRepository.update(trPushTask);
            }
            // 记录请求历史
            inboundRequestHistoryService.add(tr, WarehouseProviderType.LEYU_XLWMS, null, instockDto, result.getData().getCode(), null, result.getData());
        }
        return true;
    }

    /**
     * 执行: TR-目的仓为乐鱼-箱唛任务.
     * @param trNoList tr单号集合，用于异常重试的参数触发机制
     */
    @Lock4j(name = "transport:request:leyu:inbound:add:pallet")
    @Transactional(rollbackFor = Exception.class)
    public void doingPalletLeYu(List<String> trNoList) {
        transportRequestCommonService.doingPallet(warehouseType, trNoList);
    }


}
