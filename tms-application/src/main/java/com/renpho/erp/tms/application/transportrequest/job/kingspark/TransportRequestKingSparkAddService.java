package com.renpho.erp.tms.application.transportrequest.job.kingspark;

import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.apiproxy.eccang.yunwms.model.instock.CreateAsnRequest;
import com.renpho.erp.apiproxy.eccang.yunwms.model.instock.CreateAsnResponse;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.application.transportrequest.TransportRequestQueryService;
import com.renpho.erp.tms.application.transportrequest.job.common.TransportRequestCommonService;
import com.renpho.erp.tms.domain.inbound.InboundBusinessType;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transportorder.dto.TransportRequestOrderData;
import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderRepository;
import com.renpho.erp.tms.infrastructure.remote.apiproxy.inbound.EccangInboundClient;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * TR-目的仓为KingSpark-创建入库单任务.
 *
 * <AUTHOR>
 * @since 2025/7/19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportRequestKingSparkAddService {

    private final TransportRequestQueryService transportRequestQueryService;
    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final TransportRequestCommonService transportRequestCommonService;
    private final PushTaskService pushTaskService;

    private final PushTaskRepository pushTaskRepository;
    private final TransportRequestOrderRepository transportRequestOrderRepository;
    private final PushTaskLookup pushTaskLookup;

    private final EccangInboundClient eccangInboundClient;

    // 仓库类型
    private final WarehouseProviderType warehouseType = WarehouseProviderType.KING_SPARK_YUNWMS;

    @EventListener(ApplicationReadyEvent.class)
    @Transactional(rollbackFor = Exception.class)
    public void test() {
//        createInboundTask();
//        doingInboundKingSpark(null);
    }

    /**
     * TR-目的仓为KingSpark-创建入库单任务生成.
     */
    @Lock4j(name = "transport:request:kingspark:inbound:add:basic")
    @Transactional(rollbackFor = Exception.class)
    public void createInboundTask() {
        try {
            log.info("TR-目的仓为KingSpark的定时器任务开始");

            // 状态是待补充信息
            TransportRequestStatus status = TransportRequestStatus.PENDING;
            List<TransportRequestOrderData> trDataList = transportRequestQueryService.selectTrListByStatus(status.getValue(), warehouseType, PushTaskType.CREATE_INBOUND);

            // 生成入库单推送任务
            if (trDataList != null && !trDataList.isEmpty()) {
                // 获取tr上下文信息
                Map<Integer, TransportRequest> trMap = transportRequestQueryService.getTrMapByIds(trDataList.stream().map(TransportRequestOrderData::getId).toList());

                for (TransportRequestOrderData trData : trDataList) {
                    try {
                        TransportRequest tr = trMap.get(trData.getId());
                        if (tr == null) {
                            log.error("TR-目的仓为KingSpark-创建入库单任务生成异常, tr上下文找不到, trId={}", trData.getId());
                            continue;
                        }
                        // 目的仓限制
                        if (WarehouseProviderType.KING_SPARK_YUNWMS != tr.findWarehouseProviderType()) {
                            continue;
                        }

                        // 故意抛异常
                        // log.info((6/0)+"");

                        // 生成推送任务
                        transportRequestCommonService.createInboundTask(trData, warehouseType, PushTaskType.CREATE_INBOUND);
                    } catch (Exception e) {
                        log.error("TR-目的仓为KingSpark的定时器任务异常, 入库单推送任务生成失败, trId={}", trData.getId(), e);
                    }
                }
            }

        } catch (Exception e) {
            log.error("TR-目的仓为KingSpark的定时器任务异常", e);
        }
    }


    /**
     * 执行: TR-目的仓为KingSpark-入库单任务.
     * @param trNoList tr单号集合，用于异常重试的参数触发机制
     */
    @Lock4j(name = "transport:request:kingspark:inbound:add:do")
    @Transactional(rollbackFor = Exception.class)
    public void doingInboundKingSpark(List<String> trNoList) {
        try {
            log.info("TR-目的仓为KingSpark的入库单任务开始");

            // 1. load出入库单推送任务
            List<PushTask> trPushTaskList = pushTaskLookup.findByTaskType(InboundBusinessType.TR, warehouseType, PushTaskType.CREATE_INBOUND, PushTaskStatus.PENDING);

            // 补充外部任务参数任务数据
            trPushTaskList = transportRequestCommonService.buildOutPushTash(trPushTaskList, trNoList, PushTaskType.CREATE_INBOUND);

            // 2.调用KingSpark创建入库预约单
            if (trPushTaskList != null && !trPushTaskList.isEmpty()) {
                Map<Integer, TransportRequest> trMap = transportRequestQueryService.getTrMap(trPushTaskList);
                for (PushTask trPushTask : trPushTaskList) {
                    try {
                        TransportRequest tr = trMap.get(trPushTask.getBizId());
                        if (tr == null) {
                            log.error("TR-目的仓为KingSpark的入库单任务异常, tr源数据找不到, trId={}", trPushTask.getBizId());
                            continue;
                        }
                        // 目的仓限制
                        if (warehouseType != tr.findWarehouseProviderType()) {
                            continue;
                        }

                        // 执行入库单创建
                        pushTaskService.execute(() -> deliveryInstock(tr, trPushTask),trPushTask,"TR-目的仓为KingSpark-创建入库单任务异常" );
                    } catch (Exception e) {
                        log.error("TR-目的仓为KingSpark的入库单任务异常, 入库单创建失败, trId={}", trPushTask.getBizId(), e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("TR-目的仓为KingSpark的入库单任务异常", e);
        }
    }

    /**
     * 执行入库单创建
     *
     * @param tr         tr上下文
     * @param trPushTask 入库预约单推送任务
     */
    private Boolean deliveryInstock(TransportRequest tr, PushTask trPushTask) {
        TransportRequestItem trItem= tr.getItem();

        String warehouseCode = transportRequestCommonService.getWarehouseCode(tr.getDestWarehouse().getId().id());
        if (warehouseCode == null) {
            log.error("TR-目的仓为乐鱼-执行入库单任务异常, 仓库编码找不到, trId={}", tr.getTrNo());
            return false;
        }

        CreateAsnRequest instockDto = new CreateAsnRequest();
        instockDto.setReference_no(tr.getTrNo());
        instockDto.setIncome_type(0);
        // instockDto.setReceiving_type("D"); // 有点奇怪，API说明是D，但是文档定义是int(1),不过默认是D
        instockDto.setWarehouse_code(warehouseCode);
        instockDto.setReceiving_desc(tr.getPoNo());
        LocalDate etaDate = tr.getCreated().getOperateTime().toLocalDate().plusDays(70);
        instockDto.setEta_date(Date.from(etaDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        instockDto.setBulk_cargo_type_piece(1);
        instockDto.setStock_type(0);
        instockDto.setSpontaneous_head_cheng_type(2);  // 客户自发

        List<CreateAsnRequest.ReceivingItemDTO> items = new ArrayList<>();

        CreateAsnRequest.ReceivingItemDTO item = new CreateAsnRequest.ReceivingItemDTO();
        item.setProduct_sku(tr.getFnSku());
        item.setQuantity(tr.getQuantity());
        item.setBox_no(tr.getBoxQty().intValue());
        item.setInventory_type(0);  // 按仓库确认

        items.add(item);
        instockDto.setItems(items);

        String consumerCode = transportRequestCommonService.getConsumerCode(tr.getDestWarehouse().getId().id());
        if (consumerCode == null) {
            log.error("TR-目的仓为JD的定时器任务异常, 无法获取客户编码, trId={}", tr.getId());
            return false;
        }

        // 故意抛异常
        // log.info((6/0)+"");

        log.info("TR-目的仓为KingSpark-创建入库单任务生成, consumerCode={}, 参数={}", consumerCode, JSON.toJSONString(instockDto));
        R<CreateAsnResponse> result = eccangInboundClient.createAsn(consumerCode, instockDto);
        log.info("TR-目的仓为KingSpark-创建入库单任务生成, trId={}, 结果={}", tr.getId(), JSON.toJSONString(result));
        if (result != null && result.getData()!=null) {
            if (result.getData().getAsk().equals("Success")) {
                CreateAsnResponse resultVo = result.getData();
                if(StringUtils.isNotBlank(resultVo.getReceiving_code())){
                    tr.setShipmentId(String.valueOf(resultVo.getReceiving_code()));
                    transportRequestOrderRepository.updateById(tr);

                    // 3. 生成箱唛推送任务(这个跟tr生命周期重复，所以注释掉)
                    // transportRequestCommonService.createCartonFile(tr, warehouseType);

                    // 更新任务状态
                    trPushTask.setStatus(PushTaskStatus.SUCCESS);
                    pushTaskRepository.update(trPushTask);
                }
            }
            // 记录请求历史
            inboundRequestHistoryService.add(tr, WarehouseProviderType.KING_SPARK_YUNWMS, null, instockDto, result.getData().getF_ask(), null, result.getData());
        }
        return true;
    }

    /**
     * 执行: TR-目的仓为KingSpark-箱唛任务.
     * @param trNoList tr单号集合，用于异常重试的参数触发机制
     */
    @Lock4j(name = "transport:request:kingspark:inbound:add:pallet")
    @Transactional(rollbackFor = Exception.class)
    public void doingPalletKingSpark(List<String> trNoList) {
        transportRequestCommonService.doingPallet(warehouseType, trNoList);
    }

}
