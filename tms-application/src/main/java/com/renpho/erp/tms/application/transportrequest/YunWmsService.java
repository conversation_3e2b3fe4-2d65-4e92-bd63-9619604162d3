package com.renpho.erp.tms.application.transportrequest;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.renpho.erp.apiproxy.eccang.yunwms.model.OldYunwmsResponse;
import com.renpho.erp.apiproxy.eccang.yunwms.model.instock.*;
import com.renpho.erp.apiproxy.eccang.yunwms.model.transfer.*;
import com.renpho.erp.ftm.client.response.FileInfoResponse;
import com.renpho.erp.tms.application.inbound.InboundAllocationService;
import com.renpho.erp.tms.application.inbound.InboundRecordQueryService;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transportorder.TransportOrderService;
import com.renpho.erp.tms.application.transportrequest.stream.TransportRequestProducer;
import com.renpho.erp.tms.domain.exception.BusinessException;
import com.renpho.erp.tms.domain.inbound.*;
import com.renpho.erp.tms.domain.saleschannel.SalesChannelConstant;
import com.renpho.erp.tms.domain.transportorder.TransportOrderStatusEnum;
import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.domain.warehouse.Warehouse;
import com.renpho.erp.tms.domain.warehouse.WarehouseConfig;
import com.renpho.erp.tms.domain.warehouse.WarehouseId;
import com.renpho.erp.tms.infrastructure.common.constant.CommonConstant;
import com.renpho.erp.tms.infrastructure.common.constant.YunWmsConstant;
import com.renpho.erp.tms.infrastructure.common.converter.MultiLanguageConverter;
import com.renpho.erp.tms.infrastructure.persistence.inbound.po.converter.InboundRecordConverter;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderItemRepository;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderRepository;
import com.renpho.erp.tms.infrastructure.remote.apiproxy.inbound.EccangInboundClient;
import com.renpho.erp.tms.infrastructure.remote.file.RemoteFileFeign;
import com.renpho.erp.tms.infrastructure.remote.warehouse.repository.WarehouseLookup;
import com.renpho.erp.tms.infrastructure.util.DateUtils;
import com.renpho.karma.dto.R;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2025/7/18
 */
@Slf4j
@Service
@RequiredArgsConstructor

public class YunWmsService {
    private final TransportRequestQueryService transportRequestQueryService;
    private final TransportRequestOrderRepository transportRequestOrderRepository;
    private final TransportRequestService transportRequestService;
    private final TransportRequestOrderItemRepository transportRequestOrderItemRepository;
    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final RemoteFileFeign remoteFileFeign;
    private final PushTaskService pushTaskService;
    private final EccangInboundClient eccangInboundClient;
    private final WarehouseLookup warehouseLookup;
    private final TransportRequestProducer transportRequestProducer;
    private final PushTaskQueryService pushTaskQueryService;
    private final InboundRecordRepository inboundRecordRepository;
    private final InboundRecordQueryService inboundRecordQueryService;
    private final InboundRecordConverter inboundRecordConverter;
    private final InboundAllocationService inboundAllocationService;
    private final MultiLanguageConverter multiLanguageConverter;
    private final TransportOrderService transportOrderService;

    /**
     * 创建入库单
     *
     * @param trNos    TR单号
     * @param trIds    TR单ID
     * @param isAmazon true-amazon平台TR单 false-非amazon平台TR单
     */
    public void creatAsn(List<String> trNos, List<TransportRequestId> trIds, Boolean isAmazon) {

        //“待补充信息”状态TR单
        List<TransportRequest> trList =
                transportRequestQueryService.findByWarehouseProviderType(WarehouseProviderType.POLARIS_YUNWMS, trIds,
                        trNos, null, TransportRequestStatus.PENDING);

        if (isAmazon) {
            //amazon tr单，走中转逻辑
            List<TransportRequest> amzTrs = trList.stream()
                    .filter(tr -> SalesChannelConstant.AMAZON.equalsIgnoreCase(tr.getSalesChannel().getChannelName()))
                    .toList();
            creatAsnForAmzTr(amzTrs);
        } else {
            //非Amazon tr单，走一件代发逻辑
            List<TransportRequest> nonAmzTrs = trList.stream()
                    .filter(tr -> !SalesChannelConstant.AMAZON.equalsIgnoreCase(tr.getSalesChannel().getChannelName()))
                    .toList();
            creatAsnForNonAmzTr(nonAmzTrs);
        }
    }


    /**
     * 创建 转运单（中转，amazon平台TR）
     *
     * @param trs TR单
     */
    public void creatAsnForAmzTr(List<TransportRequest> trs) {

        List<WarehouseId> destinationIds = trs.stream().map(TransportRequest::getDestWarehouse).map(Warehouse::getId).toList();
        Map<WarehouseId, WarehouseConfig> warehouseConfigMap = warehouseLookup.getWarehouseConfigMap(destinationIds);
        for (TransportRequest tr : trs) {
            PushTask inbound = pushTaskService.createInbound(tr, PushTaskStatus.PENDING, 0, null);

            //构建请求参数
            WarehouseConfig warehouseConfig = warehouseConfigMap.get(tr.getDestWarehouse().getId());
            TransferOrderSaveRequest req = this.buildTransferOrderSaveRequest(tr);
            // 保存请求记录
            InboundRequestHistory history = inboundRequestHistoryService.add(tr, WarehouseProviderType.POLARIS_YUNWMS, null, req);

            try {
                // 调API代理中心接口，创建转运单
                R<String> ret = pushTaskService.execute(() -> {
                    R<String> r = eccangInboundClient.transferOrderSave(warehouseConfig.getCustomerCode(), req);
                    //将异常抛出才能触发重试机制
                    if (!r.isSuccess()) {
                        log.error("调用易仓-创建入库单接口异常：, trNo={}", tr.getTrNo());
                        throw new BusinessException("create.inbound.order.fail", r.getMessage());
                    }
                    return r;
                }, inbound);


                String transferNo = ret.getData();
                inboundRequestHistoryService.update(history.getId(), Integer.parseInt(ret.getCode()), null, transferNo);
                tr.setShipmentId(transferNo);
                //更新TR单的shipmentId
                transportRequestOrderRepository.updateById(tr);
            } catch (Exception e) {
                inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null,
                        e.getMessage());
            }
        }
    }

    private TransferOrderSaveRequest buildTransferOrderSaveRequest(TransportRequest tr) {
        TransferOrderSaveRequest request = new TransferOrderSaveRequest();
        request.setAddService(0);
        request.setTransferWay(1);
        request.setWarehouseNo(tr.getDestWarehouse().getThirdWarehouseCode());
        request.setReferenceNo(tr.getPoNo());
        Date date = Date.from(tr.getCreated().getOperateTime().atZone(ZoneId.systemDefault()).toInstant());
        DateTime predictArriveTime = DateUtil.offsetDay(date, 70);
        request.setPredictArriveTime(DateUtil.format(predictArriveTime, DatePattern.NORM_DATE_PATTERN));

        TransportRequestItem trItem = tr.getItem();
        TransferOrderSaveRequest.InboundBoxDTO inboundItem = new TransferOrderSaveRequest.InboundBoxDTO();
        inboundItem.setBoxMark(tr.getPoNo());
        inboundItem.setHeight(trItem.getProduct().getActiveBoxSpec().getBoxHeightMetric().doubleValue());
        inboundItem.setWidth(trItem.getProduct().getActiveBoxSpec().getBoxWidthMetric().doubleValue());
        inboundItem.setLength(trItem.getProduct().getActiveBoxSpec().getBoxLengthMetric().doubleValue());
        inboundItem.setSkuDesc(trItem.getProduct().getPsku());
        inboundItem.setTotalBoxNum(trItem.getBoxQty().doubleValue());
        inboundItem.setWeight(tr.getTotalGrossWeight().doubleValue());

        //构建InboundSkuDTO
        TransferOrderSaveRequest.InboundSkuDTO skuItem = new TransferOrderSaveRequest.InboundSkuDTO();
        skuItem.setProductCode(tr.getItem().getProduct().getFnSku());
        String cnName = multiLanguageConverter.findCnName(tr.getProduct().getNames());
        skuItem.setProductDesc(cnName);
        skuItem.setSingleNum(trItem.getQuantityPerBox());
        inboundItem.setSkuList(List.of(skuItem));

        request.setInboundList(List.of(inboundItem));
        return request;
    }

    /**
     * 创建 入库单（一件代发，非amazon平台TR）
     *
     * @param trs TR单
     */
    public void creatAsnForNonAmzTr(List<TransportRequest> trs) {
        List<WarehouseId> destinationIds = trs.stream().map(TransportRequest::getDestWarehouse).map(Warehouse::getId).toList();
        Map<WarehouseId, WarehouseConfig> warehouseConfigMap = warehouseLookup.getWarehouseConfigMap(destinationIds);

        for (TransportRequest tr : trs) {
            PushTask inbound = pushTaskService.createInbound(tr, PushTaskStatus.PENDING, 0, null);

            //构建请求参数
            WarehouseConfig warehouseConfig = warehouseConfigMap.get(tr.getDestWarehouse().getId());
            CreateAsnRequest req = this.buildCreateAsnRequest(tr);
            // 保存请求记录
            InboundRequestHistory history = inboundRequestHistoryService.add(tr, WarehouseProviderType.POLARIS_YUNWMS, null, req);

            try {
                // 调API代理中心接口，创建入库单
                R<CreateAsnResponse> ret = pushTaskService.execute(() -> {
                    R<CreateAsnResponse> r = eccangInboundClient.createAsn(warehouseConfig.getCustomerCode(), req);
                    //将异常抛出才能触发重试机制
                    if (!r.isSuccess()) {
                        log.error("调用易仓-创建入库单接口异常：, trNo={}", tr.getTrNo());
                        throw new BusinessException("create.inbound.order.fail", r.getMessage());
                    }
                    return r;
                }, inbound);


                if (ret.isSuccess() && ret.getData().getF_ask() == 1) {
                    inboundRequestHistoryService.update(history.getId(), Integer.parseInt(ret.getCode()), null, ret.getData());
                    String receiveCode = Optional.ofNullable(ret.getData()).map(CreateAsnResponse::getReceiving_code).orElse("");
                    tr.setShipmentId(receiveCode);
                    //更新TR单的shipmentId
                    transportRequestOrderRepository.updateById(tr);
                }
            } catch (Exception e) {
                inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, e.getMessage());
            }
        }
    }

    private void handleErrorResult(R<? extends OldYunwmsResponse> r, InboundRequestHistory history) {
        String errMsg = "";
        Optional<OldYunwmsResponse> rOpt = Optional.ofNullable(r.getData());

        if (!r.isSuccess()) {
            errMsg = r.getMessage();
        }
        if (rOpt.isEmpty()) {
            errMsg = "[易仓]返回的data为空";
        }
        String ask = rOpt.map(OldYunwmsResponse::getAsk).orElse("0");
        if (ask.equals("0")) {
            errMsg = String.format("[易仓]返回的ask为：%s", ask);
        }

        inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, errMsg);
    }

    /**
     * 创建入库单请求参数
     *
     * @param tr TR单
     * @return 请求参数
     */
    private CreateAsnRequest buildCreateAsnRequest(TransportRequest tr) {
        CreateAsnRequest request = new CreateAsnRequest();
        request.setReference_no(tr.getTrNo());
        request.setIncome_type(0);
        request.setReceiving_type("D");
        request.setWarehouse_code(tr.getDestWarehouseCode());
        request.setReceiving_desc(tr.getPoNo());
        request.setSpontaneous_head_cheng_type(2);
        request.setBulk_cargo_type_piece(1);
        request.setStock_type(0);

        //注意：：：如果后面一个TR对应多个sku，则需要调整下面逻辑
        CreateAsnRequest.ReceivingItemDTO itemDTO = new CreateAsnRequest.ReceivingItemDTO();
        itemDTO.setProduct_sku(tr.getProduct().getFnSku());
        itemDTO.setQuantity(tr.getQuantity());
        itemDTO.setBox_no(tr.getBoxQty().intValue());
        itemDTO.setInventory_type(0);

        request.setItems(List.of(itemDTO));

        return request;
    }


    /**
     * 中转-获取箱唛
     *
     * @param trNos TR单号
     * @param trIds TR单ID
     */
    public void syncTransferOrderBoxLabel(List<String> trNos, List<TransportRequestId> trIds) {
        int PUBLIC = 1;

        //“待补充信息”状态TR单
        List<TransportRequest> trList =
                transportRequestQueryService.findByWarehouseProviderType(WarehouseProviderType.POLARIS_YUNWMS, trIds,
                        trNos, null, TransportRequestStatus.PENDING);

        //todo 异常信息的也需要过滤掉？？？
        List<TransportRequest> trs = trList.stream()
                .filter(tr -> SalesChannelConstant.AMAZON.equalsIgnoreCase(tr.getSalesChannel().getChannelName())
                        && StringUtil.isNotBlank(tr.getShipmentId())).toList();

        List<WarehouseId> destinationIds = trs.stream().map(TransportRequest::getDestWarehouse).map(Warehouse::getId).toList();
        Map<WarehouseId, WarehouseConfig> warehouseConfigMap = warehouseLookup.getWarehouseConfigMap(destinationIds);
        for (TransportRequest tr : trs) {
            //构建请求参数
            WarehouseConfig warehouseConfig = warehouseConfigMap.get(tr.getDestWarehouse().getId());
            TransferOrderLabelRenderBoxMarkRequest req = this.buildTransferOrderLabelRenderBoxMarkRequest(tr);
            // 保存请求记录
            InboundRequestHistory history = inboundRequestHistoryService.add(tr, WarehouseProviderType.POLARIS_YUNWMS, null, req);

            Optional<PushTask> first = pushTaskQueryService.findByBizAndType(tr, List.of(PushTaskType.CARTON_FILE_GENERATION)).stream().findFirst();

            try {
                // 调API代理中心接口，获取箱唛文件
                R<String> ret = pushTaskService.execute(() -> {
                    R<String> r = eccangInboundClient.transferOrderLabelRenderBoxMark(warehouseConfig.getCustomerCode(), req);
                    //将异常抛出才能触发重试机制
                    if (!r.isSuccess()) {
                        log.error("调用易仓-获取转运单箱唛接口异常：, trNo={}", tr.getTrNo());
                        throw new BusinessException("get.box.mark.fail", r.getMessage());
                    }
                    return r;
                }, first.orElse(null));

                String cartonLabelUrl = ret.getData();
                inboundRequestHistoryService.update(history.getId(), Integer.parseInt(ret.getCode()), null, cartonLabelUrl);

                //将箱唛上传到FTM，保存FileId
                // 上传至S3
                R<List<FileInfoResponse>> r = remoteFileFeign.uploadByUrl(List.of(cartonLabelUrl), PUBLIC,
                        RemoteFileFeign.UPLOAD_SYSTEM, "carton-label", true);

                if (!r.isSuccess()) {
                    log.error("转运单箱唛上传FTM失败:{}", r);
                    throw new BusinessException("carton-label.upload.ftm.error");
                }

                List<String> fileIds = r.getData().stream().map(FileInfoResponse::getId).toList();
                tr.setCartonLabelFileIds(fileIds);
                tr.setStatus(TransportRequestStatus.PENDING_CONSOLIDATION);

                transportRequestProducer.sendTrCartonFiles(tr);
                transportRequestOrderRepository.updateById(tr);
            } catch (Exception e) {
                inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null,
                        e.getMessage());
            }

        }
    }

    private TransferOrderLabelRenderBoxMarkRequest buildTransferOrderLabelRenderBoxMarkRequest(TransportRequest tr) {
        TransferOrderLabelRenderBoxMarkRequest req = new TransferOrderLabelRenderBoxMarkRequest();
        req.setTransferNo(tr.getShipmentId());
        req.setBoxMarkType(1);
        req.setTemplateId(14721);
        return req;
    }

    /**
     * 作废转运单
     *
     * @param tr 传TR单的shipmentID
     */
    public void cancelTransferOrder(TransportRequest tr) {
        //构建请求参数
        WarehouseConfig warehouseConfig = warehouseLookup.getWarehouseConfig(tr.getDestWarehouse().getId());
        TransferOrderCancelRequest req = this.buildTransferOrderCancelRequest(tr.getShipmentId());
        // 保存请求记录
        InboundRequestHistory history = inboundRequestHistoryService.add(tr, WarehouseProviderType.POLARIS_YUNWMS, null, req);

        PushTask cancelTask = pushTaskService.cancel(tr, PushTaskStatus.PENDING, 0, null);

        try {
            // 调API代理中心接口，取消入库单
            R<String> ret = pushTaskService.execute(() -> {
                R<String> r = eccangInboundClient.transferOrderCancel(warehouseConfig.getCustomerCode(), req);
                //将异常抛出才能触发重试机制
                if (!r.isSuccess()) {
                    log.error("调用易仓-取消转运单接口异常：, trNo={}", tr.getTrNo());
                    throw new BusinessException("cancel.inbound.order.fail", r.getMessage());
                }
                return r;
            }, cancelTask);

            inboundRequestHistoryService.update(history.getId(), Integer.parseInt(ret.getCode()), null, ret.getData());
            //清空shipmentId
            transportRequestService.clearShipmentIdById(tr.getId().id());
        } catch (Exception e) {
            inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null,
                    e.getMessage());
        }
    }

    private TransferOrderCancelRequest buildTransferOrderCancelRequest(String shipmentId) {
        TransferOrderCancelRequest req = new TransferOrderCancelRequest();
        req.setBusinessNo(shipmentId);
        return req;
    }


    public void cancelAsn(List<String> trNos, List<TransportRequestId> trIds) {

        //“待补充信息”状态TR单
        List<TransportRequest> trList =
                transportRequestQueryService.findByWarehouseProviderType(WarehouseProviderType.POLARIS_YUNWMS, trIds,
                        trNos, null, TransportRequestStatus.CANCEL);

        //todo 异常信息的也需要过滤掉？？？
        List<TransportRequest> trs = trList.stream()
                .filter(tr -> !SalesChannelConstant.AMAZON.equalsIgnoreCase(tr.getSalesChannel().getChannelName())
                        && StringUtil.isNotBlank(tr.getShipmentId())).toList();
        for (TransportRequest tr : trs) {
            cancelAsn(tr);
        }
    }

    private CancelAsnRequest buildCancelAsnRequest(String receivingCode) {
        CancelAsnRequest req = new CancelAsnRequest();
        req.setReceiving_code(receivingCode);
        return req;
    }


    /**
     * 取消入库单
     *
     * @param tr tr
     */
    public void cancelAsn(TransportRequest tr) {
        //构建请求参数
        WarehouseConfig warehouseConfig = warehouseLookup.getWarehouseConfig(tr.getDestWarehouse().getId());
        CancelAsnRequest req = this.buildCancelAsnRequest(tr.getShipmentId());
        // 保存请求记录
        InboundRequestHistory history = inboundRequestHistoryService.add(tr, WarehouseProviderType.POLARIS_YUNWMS, null, req);

        PushTask cancelTask = pushTaskService.cancel(tr, PushTaskStatus.PENDING, 0, null);

        try {

            // 调API代理中心接口，取消入库单
            R<CancelAsnResponse> ret = pushTaskService.execute(() -> {
                R<CancelAsnResponse> r = eccangInboundClient.cancelAsn(warehouseConfig.getCustomerCode(), req);
                //将异常抛出才能触发重试机制
                if (!r.isSuccess()) {
                    log.error("调用易仓-取消入库单接口异常：, trNo={}", tr.getTrNo());
                    throw new BusinessException("cancel.inbound.order.fail", r.getMessage());
                }
                return r;
            }, cancelTask);


            if (ret.isSuccess() && Objects.equals(ret.getData().getAsk(), YunWmsConstant.ResponseCode.SUCCESS)) {
                inboundRequestHistoryService.update(history.getId(), Integer.parseInt(ret.getCode()), null, ret.getData());
            } else {
                handleErrorResult(ret, history);
                return;
            }

            //清空shipmentId
            transportRequestService.clearShipmentIdById(tr.getId().id());
        } catch (Exception e) {
            inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null,
                    e.getMessage());
        }
    }


    /**
     * 极智佳-中转-拉取转运单（Amazon平台）
     *
     * @param trNos TR单号
     * @param trIds TR单ID
     */
    public void pullTransferOrder(List<String> trNos, List<TransportRequestId> trIds) {
        //获取所有状态为已派送或部分签收的极智佳 TR单
        List<TransportRequest> trList =
                transportRequestQueryService.findByWarehouseProviderType(WarehouseProviderType.POLARIS_YUNWMS, trIds, trNos,
                        Arrays.asList(TransportOrderStatusEnum.OUT_FOR_DELIVERY,
                                TransportOrderStatusEnum.DELIVERED_PART), null);


        //todo 异常信息的也需要过滤掉？？？
        List<TransportRequest> trs = trList.stream()
                .filter(tr -> SalesChannelConstant.AMAZON.equalsIgnoreCase(tr.getSalesChannel().getChannelName())
                        && StringUtil.isNotBlank(tr.getShipmentId())).toList();

        List<WarehouseId> destinationIds = trs.stream().map(TransportRequest::getDestWarehouse).map(Warehouse::getId).toList();
        Map<WarehouseId, WarehouseConfig> warehouseConfigMap = warehouseLookup.getWarehouseConfigMap(destinationIds);
        for (TransportRequest tr : trs) {
            //构建请求参数
            WarehouseConfig warehouseConfig = warehouseConfigMap.get(tr.getDestWarehouse().getId());
            GetTransferOrderInfoRequest req = this.buildGetTransferOrderInfoRequest(tr);
            // 保存请求记录
            InboundRequestHistory history = inboundRequestHistoryService.add(tr, WarehouseProviderType.POLARIS_YUNWMS, null, req);

            Optional<PushTask> inboundPushTask = pushTaskQueryService.findByBizAndType(tr, List.of(PushTaskType.INBOUND)).stream().findFirst();


            try {
                // 调API代理中心接口，查询转运单
                R<GetTransferOrderInfoResponse> ret = pushTaskService.execute(() -> {
                    R<GetTransferOrderInfoResponse> r = eccangInboundClient.getTransferOrderInfo(warehouseConfig.getCustomerCode(), req);
                    //将异常抛出才能触发重试机制
                    if (!r.isSuccess()) {
                        log.error("调用易仓-获取转运单列表接口异常：, trNo={}", tr.getTrNo());
                        throw new BusinessException("get.inbound.order.fail", r.getMessage());
                    }
                    return r;
                }, inboundPushTask.orElse(null));

                GetTransferOrderInfoResponse data = ret.getData();
                inboundRequestHistoryService.update(history.getId(), Integer.parseInt(ret.getCode()), null, data);

                // 处理转运单签收数据
                SpringUtil.getBean(this.getClass()).handleTransferOrderData(data, tr, history.getId());

                pushTaskService.inbound(inboundPushTask.get(), tr);
            } catch (Exception e) {
                inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null,
                        e.getMessage());
            }
        }

    }

    /**
     * 处理转运单数据
     *
     * @param data      转运单数据
     * @param tr        TR单
     * @param historyId API请求记录ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleTransferOrderData(GetTransferOrderInfoResponse data, TransportRequest tr,
                                        InboundRequestHistoryId historyId) {
        Integer status = Integer.parseInt(data.getStatus());
        //入库单状态为“已完成”，保存TR单的签收时间、签收数量、上架时间、上架数量
        if (YunWmsConstant.TransferOrderStatus.COMPLETED.equals(status)) {

            InboundRecord lastRecord = inboundRecordQueryService.findLastByBiz(tr).orElse(null);

            //签收次数
            int receiveCnt = data.getReceiveRecords().size();
            for (int i = 0; i < receiveCnt; i++) {
                GetTransferOrderInfoResponse.ReceiveRecords receiveRecord = data.getReceiveRecords().get(i);
                String receiveNum = receiveRecord.getReceiveNum();
                //签收数量：receiveNum * TR的装箱数量
                int receivedQuantity = Integer.parseInt(receiveNum) * tr.getItem().getProduct().getQuantityPerBox();


                InboundRecord record = inboundRecordConverter.toDomain(tr);

                //收货完成或者上架完成，则保存TR单的签收时间、签收数量、上架时间、上架数量
                record.setWarehouseType(WarehouseProviderType.POLARIS_YUNWMS);
                record.setRequestHistoryId(historyId);
                //最后一次签收
                boolean isLastReceived = i == receiveCnt - 1;
                //第一次签收
                boolean isFirstReceived = i == 0;
                record.setStatus(isLastReceived ? InboundStatus.FINISH : InboundStatus.WORKING);
                record.setPutawayStatus(isLastReceived ? InboundStatus.FINISH : InboundStatus.WORKING);

                //TR单的实际的签收数量、上架数量
                tr.getItem().setReceivedQuantity(receivedQuantity);
                tr.getItem().setShelvedQuantity(receivedQuantity);

                record.buildReceivedInfo(receivedQuantity, lastRecord);
                record.buildPutawayInfo(receivedQuantity, lastRecord);

                //签收时间
                String receiveTime = receiveRecord.getEcCreateTime();
                if (StringUtil.isNotBlank(receiveTime)) {
                    LocalDateTime receivedTime = DateUtils.parse(receiveTime);
                    record.setReceivedTime(receivedTime);
                    //上架时间
                    record.setPutawayTime(receivedTime);
                    //记录开始签收时间
                    if (isFirstReceived) {
                        tr.setReceivedTime(receivedTime);
                    }
                    //记录结束签收时间
                    if (isLastReceived) {
                        tr.setReceivedEndTime(receivedTime);
                        tr.setShipStatus(TransportOrderStatusEnum.DELIVERED);
                    }
                }

                //更新TR单
                transportRequestOrderRepository.updateById(tr);
                transportRequestOrderItemRepository.update(tr);
                // 判断此TR单对应的TO单下所有TR是否全部都是已签收状态，如果是，则将TO状态改为已签收
                transportOrderService.syncTransportOrderStatus(tr, TransportOrderStatusEnum.DELIVERED);


                if (isLastReceived) {
                    //签收完成后，计算签收、上架差异数量
                    boolean haveDiscrepancy = tr.getItem().calculateTheDifferenceValue(tr.getItem().getReceivedQuantity(), tr.getItem().getShelvedQuantity());
                    transportRequestOrderItemRepository.update(tr);
                    //差异值处理完后，若无差异，更新TR单出运状态更新“已完成"
                    if (!haveDiscrepancy) {
                        tr.setShipStatus(TransportOrderStatusEnum.COMPLETED);
                        //更新TR单
                        transportRequestOrderRepository.updateById(tr);
                        transportOrderService.syncTransportOrderStatus(tr, TransportOrderStatusEnum.COMPLETED);
                    }
                }

                //保存签收、上架数据
                record = inboundRecordRepository.add(record);
                //处理入库记录的运费和税费分摊
                inboundAllocationService.allocateFreightAndTax(record);

            }

        }
    }

    private GetTransferOrderInfoRequest buildGetTransferOrderInfoRequest(TransportRequest tr) {
        GetTransferOrderInfoRequest req = new GetTransferOrderInfoRequest();
        req.setTransferNo(tr.getShipmentId());
        return req;
    }

    /**
     * 极智佳-中转-作废转运单（Amazon平台）
     *
     * @param trNos TR单号
     * @param trIds TR单ID
     */
    public void cancelTransferOrder(List<String> trNos, List<TransportRequestId> trIds) {

        //“待补充信息”状态TR单
        List<TransportRequest> trList =
                transportRequestQueryService.findByWarehouseProviderType(WarehouseProviderType.POLARIS_YUNWMS, trIds,
                        trNos, null, TransportRequestStatus.CANCEL);

        //todo 异常信息的也需要过滤掉？？？
        List<TransportRequest> trs = trList.stream()
                .filter(tr -> SalesChannelConstant.AMAZON.equalsIgnoreCase(tr.getSalesChannel().getChannelName())
                        && StringUtil.isNotBlank(tr.getShipmentId())).toList();
        for (TransportRequest tr : trs) {
            this.cancelTransferOrder(tr);
        }
    }


    /**
     * 一件代发-获取箱唛文件
     *
     * @param trNos TR单号
     * @param trIds TR单ID
     */
    public void syncYplBoxLabel(List<String> trNos, List<TransportRequestId> trIds) {
        int PUBLIC = 1;

        //“待补充信息”状态TR单
        List<TransportRequest> trList =
                transportRequestQueryService.findByWarehouseProviderType(WarehouseProviderType.POLARIS_YUNWMS, trIds,
                        trNos, null, TransportRequestStatus.PENDING);

        //todo 异常信息的也需要过滤掉？？？
        List<TransportRequest> trs = trList.stream()
                .filter(tr -> !SalesChannelConstant.AMAZON.equalsIgnoreCase(tr.getSalesChannel().getChannelName())
                        && StringUtil.isNotBlank(tr.getShipmentId())).toList();

        List<WarehouseId> destinationIds = trs.stream().map(TransportRequest::getDestWarehouse).map(Warehouse::getId).toList();
        Map<WarehouseId, WarehouseConfig> warehouseConfigMap = warehouseLookup.getWarehouseConfigMap(destinationIds);
        for (TransportRequest tr : trs) {
            //构建请求参数
            WarehouseConfig warehouseConfig = warehouseConfigMap.get(tr.getDestWarehouse().getId());
            GetReceivingBoxPdfByCodeRequest req = this.buildGetBoxLabelRequest(tr);
            // 保存请求记录
            InboundRequestHistory history = inboundRequestHistoryService.add(tr, WarehouseProviderType.POLARIS_YUNWMS, null, req);

            Optional<PushTask> first = pushTaskQueryService.findByBizAndType(tr, List.of(PushTaskType.CARTON_FILE_GENERATION)).stream().findFirst();


            try {
                // 调API代理中心接口，获取箱唛文件
                R<GetReceivingBoxPdfByCodeResponse> ret = pushTaskService.execute(() -> {
                    R<GetReceivingBoxPdfByCodeResponse> r = eccangInboundClient.getReceivingBoxPdfByCode(warehouseConfig.getCustomerCode(), req);
                    //将异常抛出才能触发重试机制
                    if (!r.isSuccess()) {
                        log.error("调用易仓-获取箱唛接口异常：, trNo={}", tr.getTrNo());
                        throw new BusinessException("get.box.mark.fail", r.getMessage());
                    }
                    return r;
                }, first.orElse(null));


                if (ret.isSuccess() && Objects.equals(ret.getData().getAsk(), YunWmsConstant.ResponseCode.SUCCESS)) {
                    inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_SUCCESS_CODE, null,
                            ret.getData());
                } else {
                    handleErrorResult(ret, history);
                    break;
                }

                String cartonLabelUrl = ret.getData().getBase64();
                //将箱唛上传到FTM，保存FileId
                // 上传至S3
                R<List<FileInfoResponse>> r = remoteFileFeign.uploadByUrl(List.of(cartonLabelUrl), PUBLIC,
                        RemoteFileFeign.UPLOAD_SYSTEM, "carton-label", true);

                if (!r.isSuccess()) {
                    log.error("箱唛上传FTM失败:{}", r);
                    throw new BusinessException("carton-label.upload.ftm.error");
                }

                List<String> fileIds = r.getData().stream().map(FileInfoResponse::getId).toList();
                tr.setCartonLabelFileIds(fileIds);
                tr.setStatus(TransportRequestStatus.PENDING_CONSOLIDATION);

                transportRequestProducer.sendTrCartonFiles(tr);
                transportRequestOrderRepository.updateById(tr);
            } catch (Exception e) {
                inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, e.getMessage());
            }
        }
    }

    /**
     * 构建箱唛请求参数
     *
     * @param tr TR单
     * @return 箱唛请求参数
     */
    private GetReceivingBoxPdfByCodeRequest buildGetBoxLabelRequest(TransportRequest tr) {
        GetReceivingBoxPdfByCodeRequest req = new GetReceivingBoxPdfByCodeRequest();
        req.setReceiving_code(tr.getShipmentId());
        req.setPdf_type("box");
        req.setPdf_size("100*100-PT");
        req.setMade_in_china(1);
        req.setContent_type("url");

        return req;
    }

    private CetAsnListRequest buildGetAsnListRequest(TransportRequest tr) {
        CetAsnListRequest req = new CetAsnListRequest();
        req.setReceiving_code(tr.getShipmentId());
        return req;
    }


    /**
     * 极智佳-获取ASN列表
     *
     * @param trNos TR单号
     * @param trIds TR单ID
     */
    public void pullAsnList(List<String> trNos, List<TransportRequestId> trIds) {
        //获取所有状态为已派送或部分签收的极智佳 TR单
        List<TransportRequest> trList =
                transportRequestQueryService.findByWarehouseProviderType(WarehouseProviderType.POLARIS_YUNWMS, trIds, trNos,
                        Arrays.asList(TransportOrderStatusEnum.OUT_FOR_DELIVERY,
                                TransportOrderStatusEnum.DELIVERED_PART), null);


        //todo 异常信息的也需要过滤掉？？？
        List<TransportRequest> trs = trList.stream()
                .filter(tr -> !SalesChannelConstant.AMAZON.equalsIgnoreCase(tr.getSalesChannel().getChannelName())
                        && StringUtil.isNotBlank(tr.getShipmentId())).toList();

        List<WarehouseId> destinationIds = trs.stream().map(TransportRequest::getDestWarehouse).map(Warehouse::getId).toList();
        Map<WarehouseId, WarehouseConfig> warehouseConfigMap = warehouseLookup.getWarehouseConfigMap(destinationIds);

        for (TransportRequest tr : trs) {
            //构建请求参数
            WarehouseConfig warehouseConfig = warehouseConfigMap.get(tr.getDestWarehouse().getId());
            CetAsnListRequest req = this.buildGetAsnListRequest(tr);
            // 保存请求记录
            InboundRequestHistory history = inboundRequestHistoryService.add(tr, WarehouseProviderType.POLARIS_YUNWMS, null, req);

            Optional<PushTask> inboundTushTask = pushTaskQueryService.findByBizAndType(tr, List.of(PushTaskType.INBOUND)).stream().findFirst();

            try {
                // 调API代理中心接口，查询入库单
                R<GetAsnListResponse> ret = pushTaskService.execute(() -> {
                    R<GetAsnListResponse> r = eccangInboundClient.getAsnList(warehouseConfig.getCustomerCode(), req);
                    //将异常抛出才能触发重试机制
                    if (!r.isSuccess()) {
                        log.error("调用易仓-获取入库单列表接口异常：, trNo={}", tr.getTrNo());
                        throw new BusinessException("get.inbound.order.fail", r.getMessage());
                    }
                    return r;
                }, inboundTushTask.orElse(null));

                if (ret.isSuccess() && Objects.equals(ret.getData().getAsk(), YunWmsConstant.ResponseCode.SUCCESS)) {
                    inboundRequestHistoryService.update(history.getId(), Integer.parseInt(ret.getCode()), null, ret.getData());
                } else {
                    handleErrorResult(ret, history);
                    return;
                }

                List<GetAsnListResponse.AsnListData> dataList = ret.getData().getData();
                SpringUtil.getBean(this.getClass()).handleAsnListData(dataList, tr, history.getId());
                pushTaskService.inbound(inboundTushTask.get(), tr);
            } catch (Exception e) {
                inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, e.getMessage());
            }
        }
    }


    /**
     * 处理入库单列表数据
     *
     * @param dataList  入库单列表数据
     * @param tr        tr单
     * @param historyId 入库请求记录ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleAsnListData(List<GetAsnListResponse.AsnListData> dataList, TransportRequest tr, InboundRequestHistoryId historyId) {
        dataList.forEach(asn -> {
            String receivingStatus = asn.getReceiving_status();

            InboundRecord lastRecord = inboundRecordQueryService.findLastByBiz(tr).orElse(null);


            for (GetAsnListResponse.Item item : asn.getItems()) {
                InboundRecord record = inboundRecordConverter.toDomain(tr);

                boolean haveNewReceived = false, haveNewPutaway = false;

                //收货完成或者上架完成，则保存TR单的签收时间、签收数量、上架时间、上架数量
                if (Set.of(YunWmsConstant.InboundOrderStatus.RECEIVE_COMPLETED_DEST,
                        YunWmsConstant.InboundOrderStatus.COMPLETED_PUTAWAY).contains(receivingStatus)) {
                    record.setWarehouseType(WarehouseProviderType.POLARIS_YUNWMS);
                    record.setRequestHistoryId(historyId);
                    record.setStatus(InboundStatus.WORKING);
                    record.setPutawayStatus(InboundStatus.WORKING);
                    tr.setShipStatus(TransportOrderStatusEnum.DELIVERED_PART);
                    int receivedQuantity = Integer.parseInt(item.getReceived_quantity());

                    haveNewReceived = record.buildReceivedInfo(receivedQuantity, lastRecord);
                    haveNewPutaway = record.buildPutawayInfo(receivedQuantity, lastRecord);

                    //签收时间
                    String warehouseReceivingCompleteTime = asn.getWarehouse_receiving_complete_time();
                    if (StringUtil.isNotBlank(warehouseReceivingCompleteTime)) {
                        LocalDateTime receivedTime = DateUtils.parse(warehouseReceivingCompleteTime);
                        record.setReceivedTime(receivedTime);
                        tr.setReceivedTime(receivedTime);
                    }
                    //上架时间
                    String warehouseShelfTime = asn.getWarehouse_shelf_time();
                    if (StringUtil.isNotBlank(warehouseShelfTime)) {
                        LocalDateTime shelfTime = DateUtils.parse(warehouseShelfTime);
                        record.setPutawayTime(shelfTime);
                    }

                    //记录TR单的实际签收数量、上架数量
                    tr.getItem().setReceivedQuantity(record.getReceivedQuantityTotal());
                    tr.getItem().setShelvedQuantity(record.getReceivedQuantityTotal());

                    //入库单状态上架完成了的，则TR单出运状态更新“签收完成”；
                    if (YunWmsConstant.InboundOrderStatus.COMPLETED_PUTAWAY.equals(receivingStatus)) {
                        //更新TR单出运状态更新“已签收”
                        tr.setShipStatus(TransportOrderStatusEnum.DELIVERED);
                        tr.setReceivedEndTime(record.getReceivedTime());
                    }
                }


                //存在增量数据
                if (haveNewReceived || haveNewPutaway) {
                    //更新TR单
                    transportRequestOrderRepository.updateById(tr);
                    transportRequestOrderItemRepository.update(tr);
                    // 判断此TR单对应的TO单下所有TR是否全部都是已签收状态，如果是，则将TO状态改为已签收
                    transportOrderService.syncTransportOrderStatus(tr, TransportOrderStatusEnum.DELIVERED);

                    //入库单状态上架完成了的，则TR单出运状态更新“签收完成”；
                    if (YunWmsConstant.InboundOrderStatus.COMPLETED_PUTAWAY.equals(receivingStatus)) {
                        record.setStatus(InboundStatus.FINISH);
                        record.setPutawayStatus(InboundStatus.FINISH);

                        //签收完成后，计算签收、上架差异数量
                        boolean haveDiscrepancy = tr.getItem().calculateTheDifferenceValue(record.getReceivedQuantityTotal(), record.getPutawayQuantityTotal());
                        transportRequestOrderItemRepository.update(tr);
                        //差异值处理完后，若无差异，更新TR单出运状态更新“已完成"

                        if (!haveDiscrepancy) {
                            tr.setShipStatus(TransportOrderStatusEnum.COMPLETED);
                            //更新TR单
                            transportRequestOrderRepository.updateById(tr);
                            transportOrderService.syncTransportOrderStatus(tr, TransportOrderStatusEnum.COMPLETED);
                        }
                    }

                    //保存签收、上架数据
                    record = inboundRecordRepository.add(record);
                    //处理入库记录的运费和税费分摊
                    inboundAllocationService.allocateFreightAndTax(record);
                }
            }
        });
    }
}
