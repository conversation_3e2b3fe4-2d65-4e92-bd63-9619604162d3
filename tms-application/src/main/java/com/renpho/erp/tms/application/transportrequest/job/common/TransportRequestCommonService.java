package com.renpho.erp.tms.application.transportrequest.job.common;

import com.alibaba.fastjson.JSON;
import com.renpho.erp.ftm.client.response.FileInfoResponse;
import com.renpho.erp.ims.client.feign.warehouse.RemoteWarehouseService;
import com.renpho.erp.ims.client.feign.warehouse.query.IdListQuery;
import com.renpho.erp.ims.client.feign.warehouse.query.WarehouseQuery;
import com.renpho.erp.ims.client.feign.warehouse.vo.WarehouseVo;
import com.renpho.erp.tms.application.inbound.InboundAllocationService;
import com.renpho.erp.tms.application.inbound.InboundRecordQueryService;
import com.renpho.erp.tms.application.transportorder.TransportOrderService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.application.transportrequest.TransportRequestQueryService;
import com.renpho.erp.tms.domain.inbound.*;
import com.renpho.erp.tms.domain.transportorder.TransportOrderStatusEnum;
import com.renpho.erp.tms.domain.transportorder.dto.TransportRequestOrderData;
import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.domain.transportrequest.dto.ConfigJSONContextLy;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderRepository;
import com.renpho.erp.tms.infrastructure.util.cartonlabel.CartonLabelGenerator;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * API对接公共服务.
 *
 * <AUTHOR>
 * @since 2025/7/22
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportRequestCommonService {

    private final TransportRequestQueryService transportRequestQueryService;
    private final PushTaskService pushTaskService;
    private final TransportOrderService transportOrderService;
    private final RemoteWarehouseService remoteWarehouseService;
    private final InboundRecordQueryService inboundRecordQueryService;
    private final InboundRecordRepository inboundRecordRepository;
    private final InboundAllocationService inboundAllocationService;

    private final PushTaskRepository pushTaskRepository;
    private final TransportRequestOrderRepository transportRequestOrderRepository;
    private final PushTaskLookup pushTaskLookup;

    // 客户编码缓存 仓库ID：IMS的客户编码
    private final Map<Integer, ConfigJSONContextLy> consumerMap = new ConcurrentHashMap<>();

    // 仓库编码缓存 仓库ID：IMS的仓库编码
    private final Map<Integer, WarehouseVo> warehouseMap = new ConcurrentHashMap<>();

    /**
     * 获取客户编码.
     *
     * @param warehouseId 仓库ID
     * @return 客户编码
     */
    public synchronized String getConsumerCode(Integer warehouseId) {
        if (consumerMap.containsKey(warehouseId)) {
            return consumerMap.get(warehouseId).getAccount();
        }

        IdListQuery query = new IdListQuery();
        List<Integer> idList = List.of(warehouseId);
        query.setIdList(idList);
        R<Map<Integer, String>> result = remoteWarehouseService.listThirdWarehouseConfig(query);
        if (result.isSuccess()) {
            if (result.getData().containsKey(warehouseId)) {
                String data = result.getData().get(warehouseId);
                ConfigJSONContextLy configJSONContextLy = JSON.parseObject(data, ConfigJSONContextLy.class);
                consumerMap.put(warehouseId, configJSONContextLy);
                return configJSONContextLy.getAccount();
            } else {
                return null;
            }
        } else {
            log.error("获取客户编码失败, 仓库ID: {}", warehouseId);
            return null;
        }
    }

    /**
     * 获取getWarehouseCode.
     *
     * @param warehouseId 仓库ID
     * @return 客户编码
     */
    public synchronized String getWarehouseCode(Integer warehouseId) {
        if (warehouseMap.containsKey(warehouseId)) {
            return warehouseMap.get(warehouseId).getThirdWarehouseCode();
        }

        WarehouseQuery warehouseQuery = new WarehouseQuery();
        warehouseQuery.setIdList(List.of(warehouseId));
        R<List<WarehouseVo>> result = remoteWarehouseService.list(warehouseQuery);
        if (result.isSuccess()) {
            Map<Integer, WarehouseVo> voMap = result.getData().stream().collect(Collectors.toMap(WarehouseVo::getId, v -> v));
            if (voMap.containsKey(warehouseId)) {
                WarehouseVo warehouseVo = voMap.get(warehouseId);
                warehouseMap.put(warehouseId, warehouseVo);
                return warehouseVo.getThirdWarehouseCode();
            } else {
                return null;
            }
        } else {
            log.error("获取仓库编码失败, 仓库ID: {}", warehouseId);
            return null;
        }
    }

    /**
     * 生成入库任务.
     *
     * @param trData        入库预约单数据
     * @param warehouseType 仓库类型
     * @param taskType      任务类型
     */
    public void createInboundTask(TransportRequestOrderData trData, WarehouseProviderType warehouseType, PushTaskType taskType) {
        PushTask trPushTask = new PushTask(null);
        trPushTask.setBizId(trData.getId());
        trPushTask.setBizNo(trData.getTrNo());
        trPushTask.setBizType(InboundBusinessType.TR);
        trPushTask.setPsku(trData.getPsku());
        trPushTask.setFnSku(trData.getFnSku());
        trPushTask.setTaskType(taskType);
        trPushTask.setWarehouseType(warehouseType);
        trPushTask.setStatus(PushTaskStatus.PENDING);
        trPushTask.setTargetSystem(PushTaskSystem.API);
        trPushTask.setPushTime(LocalDateTime.now());

        pushTaskRepository.add(trPushTask);
    }

    /**
     * 创建箱唛文件任务
     *
     * @param tr            tr 信息
     * @param warehouseType 仓库类型
     */
    public void createCartonFile(TransportRequest tr, WarehouseProviderType warehouseType) {
        PushTask trPushTaskPack = new PushTask(null);
        trPushTaskPack.setBizId(tr.getId().id());
        trPushTaskPack.setBizNo(tr.getTrNo());
        trPushTaskPack.setBizType(InboundBusinessType.TR);
        trPushTaskPack.setPsku(tr.getProduct().getPsku());
        trPushTaskPack.setFnSku(tr.getProduct().getFnSku());
        trPushTaskPack.setTaskType(PushTaskType.CARTON_FILE_GENERATION);
        trPushTaskPack.setWarehouseType(warehouseType);
        trPushTaskPack.setStatus(PushTaskStatus.PENDING);
        trPushTaskPack.setTargetSystem(PushTaskSystem.API);
        trPushTaskPack.setPushTime(LocalDateTime.now());
        pushTaskRepository.add(trPushTaskPack);
    }

    /**
     * 箱唛任务.
     *
     * @param warehouseType 仓库类型
     */
    public void doingPallet(WarehouseProviderType warehouseType, List<String> trNoList) {
        String warehouseTypeName = warehouseType.toString();
        try {
            log.info("TR-目的仓为{}的箱唛任务开始", warehouseTypeName);

            // 1. load出箱唛推送任务
            List<PushTask> trPushTaskList = pushTaskLookup.findByTaskType(InboundBusinessType.TR, warehouseType, PushTaskType.CARTON_FILE_GENERATION, PushTaskStatus.PENDING);

            // 补充外部任务参数任务数据
            trPushTaskList = this.buildOutPushTash(trPushTaskList, trNoList, PushTaskType.CARTON_FILE_GENERATION);

            // 2. 找出对应已经完成入库单任务的推送数据
            Set<String> trNoSet = new HashSet<>();
            for (PushTask trPushTask : trPushTaskList) {
                trNoSet.add(trPushTask.getBizNo());
            }
            List<PushTask> trPushTaskListSuccess = pushTaskLookup.findByTaskTypeAndBizNo(InboundBusinessType.TR, trNoSet.stream().toList(), PushTaskType.CREATE_INBOUND, PushTaskStatus.SUCCESS);
            Map<String, PushTask> trPushTaskMapSuccess = trPushTaskListSuccess.stream().collect(Collectors.toMap(PushTask::getBizNo, t -> t));

            // 3.调用箱唛创建接口，包括箱唛主数据跟箱唛文件数据
            if (!trPushTaskList.isEmpty()) {
                Map<Integer, TransportRequest> trMap = transportRequestQueryService.getTrMap(trPushTaskList);
                for (PushTask trPushTask : trPushTaskList) {
                    TransportRequest tr = trMap.get(trPushTask.getBizId());
                    if (tr == null) {
                        log.error("TR-目的仓为{}的箱唛任务异常, tr源数据找不到, trId={}", warehouseTypeName, trPushTask.getBizId());
                        continue;
                    }
                    // 目的仓限制
                    if (warehouseType != tr.findWarehouseProviderType()) {
                        continue;
                    }
                    // 不是待补充信息状态就跳过
                    if (TransportRequestStatus.PENDING != tr.getStatus()) {
                        continue;
                    }
                    // 需要限制一下，对应的入库单任务已经执行成功
                    if (!trPushTaskMapSuccess.containsKey(tr.getTrNo())) {
                        continue;
                    }

                    // 保存箱唛信息
                    pushTaskService.execute(() -> this.savePalletInfo(tr, trPushTask), trPushTask, "TR-目的仓为" + warehouseTypeName + "的箱唛任务异常");
                }
            }
        } catch (Exception e) {
            log.error("TR-目的仓为{}的箱唛任务异常", warehouseTypeName, e);
        }
    }

    /**
     * 保存箱唛信息
     *
     * @param tr         tr信息
     * @param trPushTask 推送任务
     * @throws Exception
     */
    private Boolean savePalletInfo(TransportRequest tr, PushTask trPushTask) throws Exception {
        // 调用手动创建箱唛接口
        CartonLabelData cartonLabelData = CartonLabelData.extractCartonLabelData(tr);
        Optional<FileInfoResponse> fileInfoResponse = CartonLabelGenerator.generateCartonLabelFileToS3(cartonLabelData);
        tr.setCartonLabelFileIds(fileInfoResponse.stream().map(FileInfoResponse::getId).toList());

        // 3. 成功的话保存相关信息，并更新tr单的状态为已拼柜
        tr.setStatus(TransportRequestStatus.CONSOLIDATION);
        transportRequestOrderRepository.updateById(tr);

        // 4. 更新任务状态, 触发下游推送任务
        pushTaskService.cartonFileGeneration(tr, true, trPushTask.getRetryCount(), PushTaskStatus.SUCCESS.name());
        return true;
    }

    /**
     * 签收记录+分摊计算触发
     * @param receiveQty 签收数量
     * @param putAwayQty 上架数量
     * @param signTime 签收时间
     * @param putWayTime 上架时间
     * @param tr 入库预约单
     * @param hisRecord 历史记录
     * @param inboundStatus 签收状态
     */
    public void doRecord(Integer receiveQty, Integer putAwayQty, LocalDateTime signTime, LocalDateTime putWayTime, TransportRequest tr, InboundRequestHistory hisRecord, InboundStatus inboundStatus){
        // 记录record
        InboundRecord lastRecord = inboundRecordQueryService.findLastByBiz(tr).orElse(null);
        InboundRecord currentRecord = new InboundRecord(null);
        currentRecord.setBizId(tr.getId().id());
        currentRecord.setBizNo(tr.getTrNo());
        currentRecord.setBizType(InboundBusinessType.TR);
        currentRecord.setPsku(tr.getProduct().getPsku());
        currentRecord.setFnSku(tr.getProduct().getFnSku());
        currentRecord.setRequestHistoryId(hisRecord.getId());
        currentRecord.buildPutawayInfo(receiveQty, putAwayQty, lastRecord);
        currentRecord.setReceivedTime(signTime);
        currentRecord.setPutawayTime(putWayTime);
        // 状态值一致，所以直接设置
        currentRecord.setStatus(inboundStatus);
        currentRecord.setPutawayStatus(inboundStatus);
        inboundRecordRepository.add(currentRecord);

        // 触发分摊计算
        inboundAllocationService.allocateFreightAndTax(currentRecord);
    }

    public void doRecord(Integer receivedQuantity, Integer receivedGoodQuantity, Integer receivedBadQuantity,
                         Integer putawayQuantity, Integer putawayGoodQuantity, Integer putawayBadQuantity, LocalDateTime signTime, LocalDateTime putWayTime, TransportRequest tr, InboundRequestHistory hisRecord, InboundStatus inboundStatus, InboundStatus putAwayStatus){
        // 记录record
        InboundRecord lastRecord = inboundRecordQueryService.findLastByBiz(tr).orElse(null);
        InboundRecord currentRecord = new InboundRecord(null);
        currentRecord.setBizId(tr.getId().id());
        currentRecord.setBizNo(tr.getTrNo());
        currentRecord.setBizType(InboundBusinessType.TR);
        currentRecord.setPsku(tr.getProduct().getPsku());
        currentRecord.setFnSku(tr.getProduct().getFnSku());
        currentRecord.setRequestHistoryId(hisRecord.getId());
        currentRecord.buildInfo(receivedQuantity, receivedGoodQuantity, receivedBadQuantity, putawayQuantity, putawayGoodQuantity, putawayBadQuantity, lastRecord);
        currentRecord.setReceivedTime(signTime);
        currentRecord.setPutawayTime(putWayTime);
        currentRecord.setStatus(inboundStatus);
        currentRecord.setPutawayStatus(putAwayStatus);
        inboundRecordRepository.add(currentRecord);

        // 触发分摊计算
        inboundAllocationService.allocateFreightAndTax(currentRecord);
    }

    /**
     * 补充外部参数任务
     * @param trPushTaskList 推送任务列表
     * @param trNoList 外部tr参数
     * @param pushTaskType 推送任务类型限制
     * @return 完整的推送任务列表
     */
    public List<PushTask> buildOutPushTash(List<PushTask> trPushTaskList, List<String> trNoList, PushTaskType pushTaskType){
        // 外部参数数据补充
        if (trPushTaskList != null && !trPushTaskList.isEmpty()) {
            Map<String,PushTask> trPushTaskMap = new HashMap<>();
            for (PushTask trPushTask : trPushTaskList) {
                trPushTaskMap.put(trPushTask.getBizNo(), trPushTask);
            }
            Map<String, List<PushTask>> outPushTaskList = pushTaskLookup.findByBizNos(trNoList);
            if (MapUtils.isNotEmpty(outPushTaskList) && trNoList.stream().allMatch(outPushTaskList::containsKey)) {
                for (PushTask outTask : outPushTaskList.values().stream().flatMap(Collection::stream).toList()) {
                    // 任务类型限制，避免外部参数错误
                    if (outTask.getTaskType() != pushTaskType) {
                        continue;
                    }
                    if (!trPushTaskMap.containsKey(outTask.getBizNo())) {
                        // 重置推送状态
                        trPushTaskMap.put(outTask.getBizNo(), outTask);
                    } else {
                        // 重置推送状态
                        trPushTaskMap.get(outTask.getBizNo()).setErrMsg("");
                        trPushTaskMap.get(outTask.getBizNo()).setStatus(PushTaskStatus.PENDING);
                    }
                }
            }
            trPushTaskList = trPushTaskMap.values().stream().toList();
        }
        return trPushTaskList;
    }

    /**
     * 同步TR单状态，有差异数量已签收，没有差异数量已完成
     * @param disCount 差异数量
     * @param transportRequest tr
     */
    public void syncTransportRequestStatus(Integer disCount, TransportRequest transportRequest){
        if(disCount == 0){
            transportRequest.setShipStatus(TransportOrderStatusEnum.COMPLETED);
            transportRequestOrderRepository.updateById(transportRequest);

            // 判断此TR单对应的TO单下所有TR是否全部都是已完成，如果是，则将TO状态改为已完成
            transportOrderService.syncTransportOrderStatus(transportRequest, TransportOrderStatusEnum.COMPLETED);
        } else {
            transportRequest.setShipStatus(TransportOrderStatusEnum.DELIVERED);
            transportRequestOrderRepository.updateById(transportRequest);

            // 判断此TR单对应的TO单下所有TR是否全部都是已签收状态，如果是，则将TO状态改为已签收
            transportOrderService.syncTransportOrderStatus(transportRequest, TransportOrderStatusEnum.DELIVERED);
        }
    }

}
