package com.renpho.erp.tms.application.transportrequest;

import com.renpho.erp.instantmessaging.dingtalk.utils.DingTalkUtil;
import com.renpho.erp.stream.utils.TraceIdUtil;
import com.renpho.erp.tms.domain.exception.BusinessException;
import com.renpho.erp.tms.domain.inbound.*;
import com.renpho.erp.tms.domain.operator.Operator;
import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.po.converter.PushTaskConverter;
import com.renpho.erp.tms.infrastructure.remote.user.repository.OperatorLookup;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.retry.support.RetryTemplateBuilder;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.function.ThrowingFunction;
import org.springframework.util.function.ThrowingSupplier;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.OptionalInt;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/7/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PushTaskService {

    private static final String DEFAULT_ERROR_MSG_TEMPLATE = "执行任务失败, 任务ID=[%s], traceId: [%s], 错误信息:%n%s";
    private static final int MAX_RETRY_COUNT = 5;

    private final PushTaskRepository pushTaskRepository;
    private final PushTaskLookup pushTaskLookup;
    private final PushTaskConverter pushTaskConverter;
    private final TransportRequestQueryService transportRequestQueryService;
    private final OperatorLookup operatorLookup;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Value("${dingtalk.internalTransaction.accessToken}")
    private String dingtalkAccessToken;


    private final RetryTemplate retryTemplate = new RetryTemplateBuilder()
            // 指数退避, 初始 1s, 1.5 倍递增, 最大 60s
            .exponentialBackoff(1000L, 1.5, 60 * 1000L)
            // 最大重试次数
            .maxAttempts(MAX_RETRY_COUNT)
            .build();

    @Transactional(rollbackFor = Exception.class)
    public List<PushTask> init(TransportRequest tr) {
        Operator operator = operatorLookup.findSystemUser();
        List<PushTaskAddCmd> commands = pushTaskConverter.toAddCmds(tr, operator);
        return pushTaskRepository.addBatch(commands);
    }

    @Transactional(rollbackFor = Exception.class)
    public PushTask createTrFailed(BizPskuWarehouseContainer biz, String errMsg) {
        Operator operator = operatorLookup.findSystemUser();
        PushTaskAddCmd cmd = pushTaskConverter.toAddCmd(biz, PushTaskType.TR_NO, operator);
        pushTaskConverter.updateRetryCountAndErrMsg(cmd, MAX_RETRY_COUNT, errMsg);
        PushTask task = pushTaskRepository.add(cmd, PushTaskStatus.FAILURE);
        dingTalkAlert(task, errMsg);
        return task;
    }

    /**
     * 记录入库操作, 若(TR单入库数量 + TR单入库差异数量) == TR单数量, 则额外记录入库完成操作
     *
     * @param tr         TR单
     * @param status     任务状态
     * @param retryCount 重试次数
     * @param errMsg     错误信息
     * <AUTHOR>
     * @since 2025/7/21
     */
    @Transactional(rollbackFor = Exception.class)
    public PushTask createInbound(TransportRequest tr, PushTaskStatus status, int retryCount, String errMsg) {
        Operator operator = operatorLookup.findSystemUser();
        PushTaskAddCmd cmd = pushTaskConverter.toAddCmd(tr, PushTaskType.CREATE_INBOUND, operator);
        pushTaskConverter.updateRetryCountAndErrMsg(cmd, retryCount, errMsg);
        return pushTaskRepository.add(cmd, status);
    }

    /**
     * 记录入库操作, 若(TR单入库数量 + TR单入库差异数量) == TR单数量, 则额外记录入库完成操作
     *
     * @param tr         TR单
     * @param status     任务状态
     * @param retryCount 重试次数
     * @param errMsg     错误信息
     * <AUTHOR>
     * @since 2025/7/21
     */
    @Transactional(rollbackFor = Exception.class)
    public PushTask inbound(TransportRequest tr, PushTaskStatus status, int retryCount, String errMsg) {
        Operator operator = operatorLookup.findSystemUser();
        PushTaskAddCmd cmd = pushTaskConverter.toAddCmd(tr, PushTaskType.INBOUND, operator);
        pushTaskConverter.updateRetryCountAndErrMsg(cmd, retryCount, errMsg);
        PushTask added = pushTaskRepository.add(cmd, status);
        if (tr.isInboundFinished() && status == PushTaskStatus.SUCCESS) {
            PushTaskAddCmd completed = pushTaskConverter.toAddCmd(tr, PushTaskType.INBOUND_COMPLETE, operator);
            completed.setErrMsg(errMsg);
            return pushTaskRepository.add(completed, status);
        }
        return added;
    }

    /**
     * 更新入库任务状态, 若入库总数量 == TR单数量 && status == SUCCESS, 则额外记录入库完成操作
     *
     * <AUTHOR>
     * @since 2025/7/28
     */
    @Transactional(rollbackFor = Exception.class)
    public PushTask inbound(PushTask task, TransportRequest tr) {
        Operator operator = operatorLookup.findSystemUser();
        task.setUpdated(operator);
        if (tr.isInboundFinished() && task.getStatus() == PushTaskStatus.SUCCESS) {
            PushTaskAddCmd completed = pushTaskConverter.toAddCmd(tr, PushTaskType.INBOUND_COMPLETE, operator);
            return pushTaskRepository.add(completed, PushTaskStatus.SUCCESS);
        }
        pushTaskRepository.update(task);
        return task;
    }

    /**
     * 记录入库操作, 若入库总数量 == TR单数量 && status == SUCCESS, 则额外记录入库完成操作
     *
     * @param inbound    入库记录
     * @param status     任务状态
     * @param retryCount 重试次数
     * @param errMsg     错误信息
     * <AUTHOR>
     * @since 2025/7/21
     */
    @Transactional(rollbackFor = Exception.class)
    public PushTask inbound(InboundRecord inbound, PushTaskStatus status, int retryCount, String errMsg) {
        Operator operator = operatorLookup.findSystemUser();
        PushTaskAddCmd cmd = pushTaskConverter.toAddCmd(inbound, PushTaskType.INBOUND, operator);
        TransportRequest tr = transportRequestQueryService.findByNo(inbound.getBizNo()).orElseThrow(() -> new BusinessException("TR单不存在: %s".formatted(inbound.getBizNo())));
        pushTaskConverter.updateRetryCountAndErrMsg(cmd, retryCount, errMsg);
        PushTask added = pushTaskRepository.add(cmd, status);
        if (Objects.equals(inbound.getReceivedQuantityTotal(), tr.getQuantity()) && status == PushTaskStatus.SUCCESS) {
            PushTaskAddCmd completed = pushTaskConverter.toAddCmd(tr, PushTaskType.INBOUND_COMPLETE, operator);
            completed.setErrMsg(errMsg);
            return pushTaskRepository.add(completed, status);
        }
        return added;
    }

    /**
     * 记录上架操作, 若上架总数量 == TR单数量 && status == SUCCESS, 则额外记录上架完成操作
     *
     * <AUTHOR>
     * @since 2025/7/22
     */
    @Transactional(rollbackFor = Exception.class)
    public PushTask putaway(TransportRequest tr, PushTaskStatus status, int retryCount, String errMsg) {
        Operator operator = operatorLookup.findSystemUser();
        PushTaskAddCmd cmd = pushTaskConverter.toAddCmd(tr, PushTaskType.PUTAWAY, operator);
        pushTaskConverter.updateRetryCountAndErrMsg(cmd, retryCount, errMsg);
        if (tr.isPutAwayFinished() && status == PushTaskStatus.SUCCESS) {
            PushTaskAddCmd completed = pushTaskConverter.toAddCmd(tr, PushTaskType.PUTAWAY_COMPLETE, operator);
            completed.setErrMsg(errMsg);
            return pushTaskRepository.add(completed, status);
        }
        return pushTaskRepository.add(cmd, status);
    }

    /**
     * 更新上架任务状态, 若上架总数量 == TR单数量 && status == SUCCESS, 则额外记录上架完成操作
     *
     * <AUTHOR>
     * @since 2025/7/28
     */
    @Transactional(rollbackFor = Exception.class)
    public PushTask putaway(PushTask task, TransportRequest tr) {
        Operator operator = operatorLookup.findSystemUser();
        task.setUpdated(operator);
        if (tr.isPutAwayFinished() && task.getStatus() == PushTaskStatus.SUCCESS) {
            PushTaskAddCmd completed = pushTaskConverter.toAddCmd(tr, PushTaskType.PUTAWAY_COMPLETE, operator);
            return pushTaskRepository.add(completed, PushTaskStatus.SUCCESS);
        }
        pushTaskRepository.update(task);
        return task;
    }

    @Transactional(rollbackFor = Exception.class)
    public PushTask trNo(BizPskuWarehouseContainer biz, boolean taskStatus, Integer retryCount, String errMsg) {
        PushTaskType current = PushTaskType.TR_NO;
        return addOrUpdate(biz, current, taskStatus ? PushTaskStatus.SUCCESS : PushTaskStatus.FAILURE, retryCount, errMsg);
    }

    @Transactional(rollbackFor = Exception.class)
    public PushTask cartonFileGeneration(BizPskuWarehouseContainer biz, boolean taskStatus, Integer retryCount, String errMsg) {
        PushTaskType current = PushTaskType.CARTON_FILE_GENERATION;
        return addOrUpdate(biz, current, taskStatus ? PushTaskStatus.SUCCESS : PushTaskStatus.FAILURE, retryCount, errMsg);
    }

    @Transactional(rollbackFor = Exception.class)
    public PushTask cartonFile(BizPskuWarehouseContainer biz, boolean taskStatus, Integer retryCount, String errMsg) {
        PushTaskType current = PushTaskType.CARTON_FILE;
        return addOrUpdate(biz, current, taskStatus ? PushTaskStatus.SUCCESS : PushTaskStatus.FAILURE, retryCount, errMsg);
    }

    @Transactional(rollbackFor = Exception.class)
    public PushTask deliveryTime(BizPskuWarehouseContainer biz, boolean taskStatus, Integer retryCount, String errMsg) {
        PushTaskType current = PushTaskType.DELIVERY_TIME;
        return addOrUpdate(biz, current, taskStatus ? PushTaskStatus.SUCCESS : PushTaskStatus.FAILURE, retryCount, errMsg);
    }

    @Transactional(rollbackFor = Exception.class)
    public PushTask handover(BizPskuWarehouseContainer biz, boolean taskStatus, Integer retryCount, String errMsg) {
        PushTaskType current = PushTaskType.HANDOVER;
        return addOrUpdate(biz, current, taskStatus ? PushTaskStatus.SUCCESS : PushTaskStatus.FAILURE, retryCount, errMsg);
    }

    @Transactional(rollbackFor = Exception.class)
    public PushTask departure(BizPskuWarehouseContainer biz, boolean taskStatus, Integer retryCount, String errMsg) {
        PushTaskType current = PushTaskType.DEPARTURE;
        return addOrUpdate(biz, current, taskStatus ? PushTaskStatus.SUCCESS : PushTaskStatus.FAILURE, retryCount, errMsg);
    }

    @Transactional(rollbackFor = Exception.class)
    public PushTask allocation(InboundAllocation allocation, PushTaskStatus taskStatus, int retryCount, String errMsg) {
        PushTaskType current = PushTaskType.ALLOCATION;
        PushTaskAddCmd cmd = pushTaskConverter.toAddCmd(allocation, current, operatorLookup.findSystemUser());
        cmd.setErrMsg(errMsg);
        cmd.setRetryCount(retryCount);
        return pushTaskRepository.add(cmd, taskStatus);
    }

    @Transactional(rollbackFor = Exception.class)
    public PushTask allocation(BizPskuWarehouseContainer biz, PushTaskStatus taskStatus, int retryCount, String errMsg) {
        PushTaskType current = PushTaskType.ALLOCATION;
        PushTaskAddCmd cmd = pushTaskConverter.toAddCmd(biz, current, operatorLookup.findSystemUser());
        cmd.setErrMsg(errMsg);
        cmd.setRetryCount(retryCount);
        return pushTaskRepository.add(cmd, taskStatus);
    }

    @Transactional(rollbackFor = Exception.class)
    public PushTask inventory(BizPskuWarehouseContainer biz, PushTaskStatus taskStatus, int retryCount, String errMsg) {
        PushTaskType current = PushTaskType.INVENTORY;
        PushTaskAddCmd cmd = pushTaskConverter.toAddCmd(biz, current, operatorLookup.findSystemUser());
        cmd.setErrMsg(errMsg);
        cmd.setRetryCount(retryCount);
        return pushTaskRepository.add(cmd, taskStatus);
    }

    public PushTask cancel(BizPskuWarehouseContainer biz, PushTaskStatus taskStatus, Integer retryCount, String errMsg) {
        PushTaskType current = PushTaskType.CANCEL;
        if (taskStatus == PushTaskStatus.PENDING) {
            return pushTaskRepository.add(pushTaskConverter.toAddCmd(biz, current, operatorLookup.findSystemUser()), taskStatus);
        }
        return addOrUpdate(biz, current, taskStatus, retryCount, errMsg);
    }

    /**
     * 新增或更新任务
     * 1. 任务状态为 {@link PushTaskStatus#PENDING} 时, 直接创建任务
     * 2. 任务状态不为 {@link PushTaskStatus#PENDING} 时, 若 {@code biz.getBizNo()} 和 {@code taskType} 不存在, 则创建任务, 否则更新任务
     *
     * <AUTHOR>
     * @since 2025/7/30
     */
    @Transactional(rollbackFor = Exception.class)
    public PushTask addOrUpdate(BizPskuContainer biz, PushTaskType taskType, PushTaskStatus taskStatus, Integer retryCount, String errMsg) {
        WarehouseProviderType warehouseType = biz instanceof BizPskuWarehouseContainer b
                ? b.getWarehouseType()
                : WarehouseProviderType.NOT_EXIST;

        // 任务状态为 PENDING 时, 直接创建任务
        if (taskStatus == PushTaskStatus.PENDING) {
            PushTaskAddCmd cmd = pushTaskConverter.toAddCmd(biz, taskType, operatorLookup.findSystemUser());
            cmd.setErrMsg(errMsg);
            cmd.setWarehouseType(warehouseType);
            cmd.setRetryCount(retryCount);
            return pushTaskRepository.add(cmd, PushTaskStatus.SUCCESS);
        }

        // 检查任务是否存在
        if (!pushTaskLookup.isTasksExist(biz.getBizNo(), List.of(taskType))) {
            PushTaskAddCmd cmd = pushTaskConverter.toAddCmd(biz, taskType, warehouseType, retryCount, operatorLookup.findSystemUser());
            cmd.setErrMsg(errMsg);
            cmd.setWarehouseType(warehouseType);
            cmd.setRetryCount(retryCount);
            PushTask added = pushTaskRepository.add(cmd, taskStatus);
            log.warn("任务不存在, 自动创建任务, 业务单号: [{}], 任务类型: [{}], 任务ID: [{}]", biz.getBizNo(), taskType.name(), added.getId());
            // throw new BusinessException("task.not.exist.placeholder", biz.getBizNo(), taskType.name());
            return added;
        }
        // 检查前置任务是否完成
        if (CollectionUtils.isNotEmpty(taskType.getPrevious())) {
            if (!pushTaskLookup.isTasksExist(biz.getBizNo(), taskType.getPrevious(), PushTaskStatus.SUCCESS)) {
                log.warn("前置任务未完成, 任务类型: [{}], 前置任务类型: [{}]", taskType.name(), taskType.getPrevious().stream().filter(Objects::nonNull).map(PushTaskType::name).collect(Collectors.joining(",")));
                // throw new BusinessException("task.previous.not.success.placeholder", biz.getBizNo(), taskType.name(), taskType.getPrevious().stream().filter(Objects::nonNull).map(PushTaskType::name).collect(Collectors.joining(",")));
            }
        }

        return pushTaskRepository.update(biz, taskType, taskStatus, retryCount, errMsg).orElse(null);
    }

    @Transactional(rollbackFor = Exception.class)
    public PushTask update(PushTask pushTask) {
        return pushTaskRepository.update(pushTask);
    }

    /**
     * 尝试重试执行任务并记录重试信息
     *
     * @param execution 实际要执行的业务逻辑
     * @param task      任务记录
     * <AUTHOR>
     * @since 2025/7/22
     */
    @Transactional(rollbackFor = Exception.class)
    public <T, R> R execute(ThrowingFunction<T, R> execution, T input, PushTask task) {
        return execute(() -> execution.apply(input), task);
    }

    /**
     * 尝试重试执行任务并记录重试信息
     *
     * @param execution 实际要执行的业务逻辑
     * @param task      任务记录
     * <AUTHOR>
     * @since 2025/7/22
     */
    @Transactional(rollbackFor = Exception.class)
    public <R> R execute(ThrowingSupplier<R> execution, PushTask task) {
        return execute(execution, task, Throwable::getMessage);
    }

    /**
     * 尝试重试执行任务并记录重试信息
     *
     * @param execution       实际要执行的业务逻辑
     * @param task            任务记录
     * @param defaultErrorMsg 当 {@code execution} 执行失败时写入 {@code task} 的错误信息
     * <AUTHOR>
     * @since 2025/7/22
     */
    @Transactional(rollbackFor = Exception.class)
    public <R> R execute(ThrowingSupplier<R> execution, PushTask task, String defaultErrorMsg) {
        return execute(execution, task, e -> defaultErrorMsg);
    }

    /**
     * 尝试重试执行任务并记录重试信息
     *
     * @param execution      实际要执行的业务逻辑
     * @param task           任务记录
     * @param errorExtractor 当 {@code execution} 执行失败时如何将 {@link Throwable} 转换为错误信息
     * <AUTHOR>
     * @since 2025/7/22
     */
    @Transactional(rollbackFor = Exception.class)
    public <R> R execute(ThrowingSupplier<R> execution, PushTask task, Function<Throwable, String> errorExtractor) {
        return retryTemplate.execute(context -> {
            task.setRetryCount(OptionalInt.of(context.getRetryCount()).orElse(0));
            try {
                return execution.get();
            } catch (Exception e) {
                String traceId = TraceIdUtil.getTraceId();
                log.error("执行任务失败, 重试次数=[{}], 任务ID=[{}], traceId=[{}], 错误信息=[{}]", task.getRetryCount() + 1, task.getId(), traceId, e.getMessage(), e);
                String errMsg = Optional.ofNullable(errorExtractor)
                        .map(ex -> ex.apply(e))
                        .orElseGet(() -> ExceptionUtils.getStackTrace(e));
                task.setErrMsg(errMsg);
                task.setStatus(PushTaskStatus.FAILURE);
                threadPoolTaskExecutor.execute(() -> update(task));
                // 次数大于等于最大重试次数, 发送钉钉告警
                if (context.getRetryCount() + 1 >= MAX_RETRY_COUNT) {
                    dingTalkAlert(task, errMsg);
                }
                throw e;
            }
        });
    }

    private void dingTalkAlert(PushTask task, String errMsg) {
        String traceId = TraceIdUtil.getTraceId();
        String message = DEFAULT_ERROR_MSG_TEMPLATE.formatted(task.getId(), traceId, StringUtils.defaultIfBlank(errMsg, StringUtils.EMPTY));
        DingTalkUtil.sendTextMessage(dingtalkAccessToken, message);
    }
}
