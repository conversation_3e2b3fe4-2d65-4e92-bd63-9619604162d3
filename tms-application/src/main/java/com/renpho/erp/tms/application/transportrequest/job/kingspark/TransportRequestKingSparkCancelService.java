package com.renpho.erp.tms.application.transportrequest.job.kingspark;

import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.apiproxy.eccang.yunwms.model.instock.CancelAsnRequest;
import com.renpho.erp.apiproxy.eccang.yunwms.model.instock.CancelAsnResponse;
import com.renpho.erp.log.repository.annotation.LogRecord;
import com.renpho.erp.log.repository.support.LogRecordContextHolder;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.application.transportrequest.TransportRequestQueryService;
import com.renpho.erp.tms.application.transportrequest.job.common.TransportRequestCommonService;
import com.renpho.erp.tms.domain.inbound.InboundBusinessType;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transportorder.TransportOrderStatusEnum;
import com.renpho.erp.tms.domain.transportorder.dto.TransportRequestOrderData;
import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.infrastructure.common.oplog.LogModule;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.po.converter.TransportRequestConverter;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderRepository;
import com.renpho.erp.tms.infrastructure.remote.apiproxy.inbound.EccangInboundClient;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * TR-目的仓为KingSpark-取消入库单任务.
 *
 * <AUTHOR>
 * @since 2025/7/21
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportRequestKingSparkCancelService {

    private final TransportRequestQueryService transportRequestQueryService;
    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final TransportRequestCommonService transportRequestCommonService;
    private final PushTaskService pushTaskService;

    private final PushTaskRepository pushTaskRepository;
    private final TransportRequestOrderRepository transportRequestOrderRepository;
    private final PushTaskLookup pushTaskLookup;

    private final TransportRequestConverter transportRequestConverter;

    private final EccangInboundClient eccangInboundClient;

    // 仓库类型
    private final WarehouseProviderType warehouseType = WarehouseProviderType.KING_SPARK_YUNWMS;

    /**
     * TR-目的仓为KingSpark-取消入库单任务生成.
     */
    @Lock4j(name = "transport:request:kingspark:inbound:cancel:basic")
    @Transactional(rollbackFor = Exception.class)
    public void createInboundCancelTask() {
        try {
            log.info("TR-目的仓为KingSpark的定时器任务开始");

            // 状态是已作废
            TransportOrderStatusEnum shipStatus = TransportOrderStatusEnum.VOIDED;
            List<TransportRequestOrderData> trDataList = transportRequestQueryService.selectTrListByShipStatus(shipStatus.getValue(), warehouseType.name());

            // 生成入库单推送任务
            if (trDataList != null && !trDataList.isEmpty()) {
                // 获取tr上下文信息
                Map<Integer, TransportRequest> trMap = transportRequestQueryService.getTrMapByIds(trDataList.stream().map(TransportRequestOrderData::getId).toList());

                for (TransportRequestOrderData trData : trDataList) {
                    try {
                        TransportRequest tr = trMap.get(trData.getId());
                        if (tr == null) {
                            log.error("TR-目的仓为KingSpark-取消入库单任务生成异常, tr上下文找不到, trId={}", trData.getId());
                            continue;
                        }
                        // 目的仓限制
                        if (warehouseType != tr.findWarehouseProviderType()) {
                            continue;
                        }

                        // 生成推送任务
                        transportRequestCommonService.createInboundTask(trData, warehouseType, PushTaskType.CANCEL);
                    } catch (Exception e) {
                        log.error("TR-目的仓为KingSpark的定时器任务异常, 入库单推送任务生成失败, trId={}", trData.getId(), e);
                    }
                }
            }

        } catch (Exception e) {
            log.error("TR-目的仓为KingSpark的定时器任务异常", e);
        }
    }


    /**
     * 执行: TR-目的仓为KingSpark-取消入库单任务生成.
     * @param trNoList tr单号集合，用于异常重试的参数触发机制
     */
    @Lock4j(name = "transport:request:kingspark:inbound:cancel:do")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(module = LogModule.TRANSPORT_REQUEST, type = LogModule.CommonDesc.CANCEL_OPERATOR, desc = LogModule.CommonDesc.CANCEL_DESC)
    public void doingInboundKingSpark(List<String> trNoList) {
        try {
            log.info("TR-目的仓为KingSpark的取消任务开始");

            // 1. load出入库单推送任务
            List<PushTask> trPushTaskList = pushTaskLookup.findByTaskType(InboundBusinessType.TR, warehouseType, PushTaskType.CANCEL, PushTaskStatus.PENDING);

            // 补充外部任务参数任务数据
            trPushTaskList = transportRequestCommonService.buildOutPushTash(trPushTaskList, trNoList, PushTaskType.CANCEL);

            // 2. 调用乐鱼取消入库单
            if (trPushTaskList != null && !trPushTaskList.isEmpty()) {
                Map<Integer, TransportRequest> trMap = transportRequestQueryService.getTrMap(trPushTaskList);
                for (PushTask trPushTask : trPushTaskList) {
                    TransportRequest tr = trMap.get(trPushTask.getBizId());
                    if (tr == null) {
                        log.error("TR-目的仓为KingSpark的取消任务-异常, 入库预约单不存在, trId={}", trPushTask.getBizId());
                        continue;
                    }
                    // 目的仓限制
                    if (warehouseType != tr.findWarehouseProviderType()) {
                        continue;
                    }

                    // 执行取消任务
                    TransportRequest oldData = pushTaskService.execute(() -> this.cancelInstock(tr, trPushTask),trPushTask,"TR-目的仓为KingSpark-执行取消任务-异常");

                    // 记录日志
                    if(oldData!=null){
                        LogRecordContextHolder.putRecordData(String.valueOf(tr.getId().id()), oldData, tr);
                    }
                }
            }
        } catch (Exception e) {
            log.error("TR-目的仓为KingSpark的取消任务-异常", e);
        }
    }

    @EventListener(ApplicationReadyEvent.class)
    @Transactional(rollbackFor = Exception.class)
    public void test() {
        // RVRENPHO-250730-0010 RVRENPHO-250730-0011
//        testCancel("RVRENPHO-250730-0010");
//        testCancel("RVRENPHO-250730-0011");
    }

    private void testCancel(String shipmentId){
        CancelAsnRequest cancelDto = new CancelAsnRequest();
        cancelDto.setReceiving_code(shipmentId);
        String consumerCode = "RENPHO";

        R<CancelAsnResponse> result = eccangInboundClient.cancelAsn(consumerCode, cancelDto);
        log.info("TR-目的仓为KingSpark的取消任务-调用取消接口, 结果={}", JSON.toJSONString(result));
    }

    private TransportRequest cancelInstock(TransportRequest tr, PushTask trPushTask) {
        CancelAsnRequest cancelDto = new CancelAsnRequest();
        cancelDto.setReceiving_code(tr.getShipmentId());

        String consumerCode = transportRequestCommonService.getConsumerCode(tr.getDestWarehouse().getId().id());
        if (consumerCode == null) {
            log.error("TR-目的仓为JD的定时器任务异常, 无法获取客户编码, trId={}", tr.getId());
            return null;
        }

        TransportRequest oldData = transportRequestConverter.trToCopy(tr);
        R<CancelAsnResponse> result = eccangInboundClient.cancelAsn(consumerCode, cancelDto);
        if (result != null && result.getData() != null) {
            if (result.getData().getAsk().equals("Success")) {
                // 清除ShipmentId信息
                transportRequestOrderRepository.clearShipmentIdById(tr.getId().id());

                // 更新任务状态
                trPushTask.setStatus(PushTaskStatus.SUCCESS);
                pushTaskRepository.update(trPushTask);
            }
            // 记录请求历史
            inboundRequestHistoryService.add(tr, WarehouseProviderType.KING_SPARK_YUNWMS, null, cancelDto, 200, null, result.getData());
        }
        return oldData;
    }

}
