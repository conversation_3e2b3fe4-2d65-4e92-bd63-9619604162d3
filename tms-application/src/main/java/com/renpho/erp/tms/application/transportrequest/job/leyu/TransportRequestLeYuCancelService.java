package com.renpho.erp.tms.application.transportrequest.job.leyu;

import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.apiproxy.lingxing.LingxingResponse;
import com.renpho.erp.apiproxy.lingxing.SystemAccount;
import com.renpho.erp.apiproxy.lingxing.wms.api.LxwmsInboundOrderApi;
import com.renpho.erp.apiproxy.lingxing.wms.model.inboundOrder.BatchCancelData;
import com.renpho.erp.apiproxy.lingxing.wms.model.inboundOrder.BatchCancelRequest;
import com.renpho.erp.log.repository.annotation.LogRecord;
import com.renpho.erp.log.repository.support.LogRecordContextHolder;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.application.transportrequest.TransportRequestQueryService;
import com.renpho.erp.tms.application.transportrequest.job.common.TransportRequestCommonService;
import com.renpho.erp.tms.domain.inbound.InboundBusinessType;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transportorder.TransportOrderStatusEnum;
import com.renpho.erp.tms.domain.transportorder.dto.TransportRequestOrderData;
import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.infrastructure.common.oplog.LogModule;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.po.converter.TransportRequestConverter;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderRepository;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * TR-目的仓为乐鱼-取消入库单任务.
 *
 * <AUTHOR>
 * @since 2025/7/19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportRequestLeYuCancelService {

    private final TransportRequestQueryService transportRequestQueryService;
    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final TransportRequestCommonService transportRequestCommonService;
    private final PushTaskService pushTaskService;

    private final PushTaskRepository pushTaskRepository;
    private final TransportRequestOrderRepository transportRequestOrderRepository;
    private final PushTaskLookup pushTaskLookup;

    private final TransportRequestConverter transportRequestConverter;

    private final LxwmsInboundOrderApi lxwmsInboundOrderApi;

    // 仓库类型
    private final WarehouseProviderType warehouseType = WarehouseProviderType.LEYU_XLWMS;

    /**
     * TR-目的仓为乐鱼-取消入库单任务生成.
     */
    @Lock4j(name = "transport:request:leyu:inbound:cancel:basic")
    @Transactional(rollbackFor = Exception.class)
    public void createInboundLeYuCancelTask() {
        try {
            log.info("TR-目的仓为乐鱼的定时器任务开始");

            // 状态是已作废
            TransportOrderStatusEnum shipStatus = TransportOrderStatusEnum.VOIDED;
            List<TransportRequestOrderData> trDataList = transportRequestQueryService.selectTrListByShipStatus(shipStatus.getValue(), warehouseType.name());

            // 生成入库单推送任务
            if (trDataList != null && !trDataList.isEmpty()) {
                // 获取tr上下文信息
                Map<Integer, TransportRequest> trMap = transportRequestQueryService.getTrMapByIds(trDataList.stream().map(TransportRequestOrderData::getId).toList());

                for (TransportRequestOrderData trData : trDataList) {
                    try {
                        TransportRequest tr = trMap.get(trData.getId());
                        if (tr == null) {
                            log.error("TR-目的仓为乐鱼-取消入库单任务生成异常, tr上下文找不到, trId={}", trData.getId());
                            continue;
                        }
                        // 目的仓限制
                        if (warehouseType != tr.findWarehouseProviderType()) {
                            continue;
                        }

                        // 生成推送任务
                        transportRequestCommonService.createInboundTask(trData, warehouseType, PushTaskType.CANCEL);
                    } catch (Exception e) {
                        log.error("TR-目的仓为乐鱼的定时器任务异常, 入库单推送任务生成失败, trId={}", trData.getId(), e);
                    }
                }
            }

        } catch (Exception e) {
            log.error("TR-目的仓为乐鱼的定时器任务异常", e);
        }
    }


    /**
     * 执行: TR-目的仓为乐鱼-取消入库单任务生成.
     * @param trNoList tr单号集合，用于异常重试的参数触发机制
     */
    @Lock4j(name = "transport:request:leyu:inbound:cancel:do")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(module = LogModule.TRANSPORT_REQUEST, type = LogModule.CommonDesc.CANCEL_OPERATOR, desc = LogModule.CommonDesc.CANCEL_DESC)
    public void doingInboundLeYu(List<String> trNoList) {
        try {
            log.info("TR-目的仓为乐鱼的取消任务开始");

            // 1. load出入库单推送任务
            List<PushTask> trPushTaskList = pushTaskLookup.findByTaskType(InboundBusinessType.TR, warehouseType, PushTaskType.CANCEL, PushTaskStatus.PENDING);

            // 补充外部任务参数任务数据
            trPushTaskList = transportRequestCommonService.buildOutPushTash(trPushTaskList, trNoList, PushTaskType.CANCEL);

            // 2. 调用乐鱼取消入库单
            if (trPushTaskList != null && !trPushTaskList.isEmpty()) {
                Map<Integer, TransportRequest> trMap = transportRequestQueryService.getTrMap(trPushTaskList);
                for (PushTask trPushTask : trPushTaskList) {
                    TransportRequest tr = trMap.get(trPushTask.getBizId());
                    if (tr == null) {
                        log.error("TR-目的仓为乐鱼的取消任务-异常, 入库预约单不存在, trId={}", trPushTask.getBizId());
                        continue;
                    }
                    // 目的仓限制
                    if (warehouseType != tr.findWarehouseProviderType()) {
                        continue;
                    }

                    // 执行取消任务
                    TransportRequest oldData = pushTaskService.execute(() -> this.cancelInstock(tr, trPushTask),trPushTask,"TR-目的仓为乐鱼-执行取消任务-异常");

                    if (oldData != null) {
                        // 记录日志
                        LogRecordContextHolder.putRecordData(String.valueOf(tr.getId().id()), oldData, tr);
                    }
                }
            }
        } catch (Exception e) {
            log.error("TR-目的仓为乐鱼的取消任务-异常", e);
        }
    }

    @EventListener(ApplicationReadyEvent.class)
    @Transactional(rollbackFor = Exception.class)
    public void test() {
//        testCancel();
    }

    private void testCancel(){
        BatchCancelRequest cancelDto = new BatchCancelRequest();
        cancelDto.setInboundOrderNoList(List.of("IB004250730RT"));

        String account = "A0166RENPHO";
        SystemAccount systemAccount = new SystemAccount();
        systemAccount.setAccount(account);

        log.info("cancelDto:{}", JSON.toJSONString(cancelDto));
        R<LingxingResponse<BatchCancelData>> result = lxwmsInboundOrderApi.batchCancel(systemAccount, cancelDto);
        log.info("result:{}", JSON.toJSONString(result));
    }

    private TransportRequest cancelInstock(TransportRequest tr, PushTask trPushTask) {
        BatchCancelRequest cancelDto = new BatchCancelRequest();
        cancelDto.setInboundOrderNoList(List.of(tr.getShipmentId()));

        String account = transportRequestCommonService.getConsumerCode(tr.getDestWarehouse().getId().id());
        if (account == null) {
            log.error("TR-目的仓为乐鱼-执行入库单任务异常, 仓库编码找不到, trId={}", tr.getTrNo());
            return null;
        }
        SystemAccount systemAccount = new SystemAccount();
        systemAccount.setAccount(account);

        TransportRequest oldData = transportRequestConverter.trToCopy(tr);
        R<LingxingResponse<BatchCancelData>> result = lxwmsInboundOrderApi.batchCancel(systemAccount, cancelDto);
        if (result != null) {
            if (result.isSuccess()) {
                // 清除ShipmentId信息
                transportRequestOrderRepository.clearShipmentIdById(tr.getId().id());

                // 更新任务状态
                trPushTask.setStatus(PushTaskStatus.SUCCESS);
                pushTaskRepository.update(trPushTask);
            }
            // 记录请求历史
            inboundRequestHistoryService.add(tr, WarehouseProviderType.LEYU_XLWMS, null, cancelDto, result.getData().getCode(), null, result.getData());
        }
        return oldData;
    }

}
