package com.renpho.erp.pms.domain.purchaseorder;

import com.renpho.erp.pms.domain.commom.CreatedContainer;
import com.renpho.erp.pms.domain.commom.Operator;
import com.renpho.erp.pms.domain.purchaserequest.UpdatedContainer;
import lombok.Data;
import org.jmolecules.ddd.types.AggregateRoot;

import java.io.Serializable;

@Data
public class PurchaseOrderFile implements AggregateRoot<PurchaseOrderFile, PurchaseOrderFileId>, CreatedContainer, UpdatedContainer, Serializable {

    private final PurchaseOrderFileId id;
    /**
     * 采购id
     */
    private Integer orderId;

    /**
     * 采购单号
     */
    private String orderNo;

    /**
     * 业务类型：0:采购单
     */
    private Integer businessType;

    /**
     * 文件类别：0:采购单合同
     */
    private Integer fileType;

    /**
     * 文件id
     */
    private String fileId;

    /**
     * 文件类型
     */
    private String ext;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 原文件名称
     */
    private String originalFilename;

    /**
     * 文件地址
     */
    private String fileUrl;

    private Operator created;

    private Operator updated;

    private Integer updateBy;
    private Integer createBy;

}
