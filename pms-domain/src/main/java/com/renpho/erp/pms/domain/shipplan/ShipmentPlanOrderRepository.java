package com.renpho.erp.pms.domain.shipplan;

import com.renpho.erp.pms.domain.shipplan.dto.EditShipPlanFile;
import org.jmolecules.ddd.types.Repository;

import java.util.List;

/**
 * 出库计划 repository
 *
 * <AUTHOR>
 * @since 2025/4/11
 */
public interface ShipmentPlanOrderRepository extends Repository<ShipmentPlanOrder, ShipmentPlanId> {

    /**
     * 批量更新出库计划状态
     * @param pdNos 出库计划编号
     * @param status 状态
     */
    void batchUpdateShipmentPlanOrderStatus(List<String> pdNos, Integer status);

    /**
     * 批量取消出库计划状态
     * @param pdNos 出库计划编号
     * @param opType 状态
     */
    void batchCancelShipmentPlanOrderStatus(List<String> pdNos, Integer opType);

    /**
     * 批量确认出库计划
     * @param shipmentPlanOrders 确认操作入参
     */
    void batchConfirmShipmentPlanOrder(List<ShipmentPlanOrder> shipmentPlanOrders);

    /**
     *  更新文件同步标记
     * @param editShipPlanFile 编辑文件同步标志入参
     */
    void updateFileSyncMark(EditShipPlanFile editShipPlanFile);

    /**
     * 更新文件同步标记
     *
     * @param pdNo                编辑文件同步标志入参
     * @param cartonImageFileId 编辑文件同步标志入参
     */
    void updateCartonImageFileId(String pdNo, String cartonImageFileId);

    /**
     * 重置状态和箱唛更新标记
     */
    void resetStatusAndCartonSyncFlag(List<String> pdNos, Integer status, Integer cartonSyncFlag);

    void batchSave(List<ShipmentPlanOrder> addData);

    void updateMailFlag(List<Integer> poIds,Integer flag,String errorMsg);

    void updateReceiptStatus(ShipmentPlanOrder shipmentPlanOrder);

    void updateShipmentId(String pdNo, String shipmentId);
}
