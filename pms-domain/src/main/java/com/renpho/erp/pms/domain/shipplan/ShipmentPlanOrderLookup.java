package com.renpho.erp.pms.domain.shipplan;

import com.renpho.erp.pms.domain.purchaseorder.PurchaseOrderId;
import com.renpho.erp.pms.domain.shipplan.dto.QueryShipPlan;
import com.renpho.karma.dto.PageQuery;
import com.renpho.karma.dto.Paging;
import org.jmolecules.ddd.integration.AssociationResolver;

import java.util.*;

/**
 * 出库计划 AssociationResolver
 * <AUTHOR>
 * @since 2025/4/11
 */
public interface ShipmentPlanOrderLookup extends AssociationResolver<ShipmentPlanOrder, ShipmentPlanId> {

    /**
     * 分页查询
     * @param condition 查询条件
     * @param pageQuery  分页条件
     * @return 分页结果
     */
    Paging<ShipmentPlanOrder> findPage(QueryShipPlan condition, PageQuery pageQuery);

    /**
     * 根据pdNo查询
     * @param poNo 采购单No
     * @param status   状态
     * @return 出库计划
     */
    List<ShipmentPlanOrder> findByPoNoAndStatus(String poNo,List<Integer> status);

    /**
     * 根据pdNoList查询
     * @param pdNoList pdNoList
     * @return 出库计划List
     */
    List<ShipmentPlanOrder> findByPdNoList(Collection<String> pdNoList);

    /**
     * 分组查询所有状态的数量
     */
    Map<Integer, Object> countByStatus(Set<Integer> userIdSet);

    /**
     * 根据pdNo查询
     * @param pdNo 出库计划编码
     */
    Optional<ShipmentPlanOrder> findByPdNo(String pdNo);

    /**
     * 根据id查询
     *
     * @param ids id
     * @return 出库计划List
     */
    List<ShipmentPlanOrder> findByPdIds(Collection<ShipmentPlanId> ids);

    List<ShipmentPlanOrder> findByPoId(PurchaseOrderId poId);

    /**
     * 根据poId查询
     * @param poIdList
     * @return
     */
    List<ShipmentPlanOrder> findByPoIdList(Collection<Integer> poIdList);

    /**
     * 根据poId查询
     * @param poIdList
     * @return
     */
    Map<Integer,List<ShipmentPlanOrder>> findPrMapByPoIdList(Collection<Integer> poIdList);

    /**
     * 根据poId查询收货日期
     * @param poIds
     * @return
     */
    Map<PurchaseOrderId, String> findReceiptDateByPoIds(Collection<PurchaseOrderId> poIds);
}
