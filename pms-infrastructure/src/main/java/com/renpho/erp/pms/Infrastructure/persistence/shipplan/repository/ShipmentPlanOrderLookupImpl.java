package com.renpho.erp.pms.Infrastructure.persistence.shipplan.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.pms.Infrastructure.enums.QueryShippingPlanDateTypeEnum;
import com.renpho.erp.pms.Infrastructure.persistence.shipplan.mapper.ShipmentPlanOrderMapper;
import com.renpho.erp.pms.Infrastructure.persistence.shipplan.po.ShipmentPlanOrderPO;
import com.renpho.erp.pms.Infrastructure.persistence.shipplan.po.coverter.ShipmentPlanOrderConverter;
import com.renpho.erp.pms.domain.purchaseorder.PurchaseOrderId;
import com.renpho.erp.pms.domain.purchaseorder.PurchaseOrderSupplierInfoLookup;
import com.renpho.erp.pms.domain.shipplan.ShipmentPlanId;
import com.renpho.erp.pms.domain.shipplan.ShipmentPlanOrder;
import com.renpho.erp.pms.domain.shipplan.ShipmentPlanOrderItemLookup;
import com.renpho.erp.pms.domain.shipplan.ShipmentPlanOrderLookup;
import com.renpho.erp.pms.domain.shipplan.dto.QueryShipPlan;
import com.renpho.erp.pms.domain.shipplan.enums.QueryOrderTypeEnum;
import com.renpho.erp.security.util.SecurityUtils;
import com.renpho.karma.dto.PageQuery;
import com.renpho.karma.dto.Paging;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/04/11
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ShipmentPlanOrderLookupImpl extends ServiceImpl<ShipmentPlanOrderMapper, ShipmentPlanOrderPO>
        implements ShipmentPlanOrderLookup {


    private final ShipmentPlanOrderConverter shipmentPlanOrderConverter;

    private final ShipmentPlanOrderItemLookup shipmentPlanItemLookup;

    private final PurchaseOrderSupplierInfoLookup purchaseOrderSupplierInfoLookup;

    @Override
    public Optional<ShipmentPlanOrder> findById(ShipmentPlanId shipmentPlanId) {
        return Optional.empty();
    }

    @Override
    public List<ShipmentPlanOrder> findByPoNoAndStatus(String poNo , List<Integer> status) {
        List<ShipmentPlanOrderPO> dbData = this.lambdaQuery().in(ShipmentPlanOrderPO::getPoNo, poNo)
                .eq(ShipmentPlanOrderPO::getDeleted, NumberUtils.INTEGER_ZERO)
                .in(ShipmentPlanOrderPO::getStatus, status)
                .list();
        if (CollectionUtils.isEmpty(dbData)){
            return List.of();
        }
        return dbData.stream().map(shipmentPlanOrderConverter::toDomain).toList();
    }

    @Override
    public Optional<ShipmentPlanOrder> findByPdNo(String pdNo) {
        LambdaQueryWrapper<ShipmentPlanOrderPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShipmentPlanOrderPO::getPdNo, pdNo); // 等于条件
        // 使用 getOne 方法查询单条数据
        return Optional.ofNullable(shipmentPlanOrderConverter.toDomain(this.getOne(queryWrapper)));
    }

    @Override
    public List<ShipmentPlanOrder> findByPdNoList(Collection<String> pdNoList) {
        if (CollectionUtils.isEmpty(pdNoList)) {
            return List.of();
        }
        List<ShipmentPlanOrderPO> dbData = this.lambdaQuery().in(ShipmentPlanOrderPO::getPdNo, pdNoList)
                .eq(ShipmentPlanOrderPO::getDeleted, NumberUtils.INTEGER_ZERO)
                .list();
        if (CollectionUtils.isEmpty(dbData)) {
            return List.of();
        }
        return dbData.stream().map(shipmentPlanOrderConverter::toDomain).toList();
    }

    @Override
    public Paging<ShipmentPlanOrder> findPage(QueryShipPlan condition, PageQuery pageQuery) {
        if (!buildQueryCondition(condition)) {
            return Paging.of();
        }
        // 构建查询条件
        LambdaQueryWrapper<ShipmentPlanOrderPO> queryWrapper = buildQueryWrapper(condition);
        // 设置默认分页参数
        pageQuery = Optional.ofNullable(pageQuery).orElse(new PageQuery());
        // 分页查询
        Page<ShipmentPlanOrderPO> pagePos = this.baseMapper.selectPage(new Page<>(pageQuery.getPageIndex(), pageQuery.getPageSize()), queryWrapper);
        return shipmentPlanOrderConverter.toPageDomains(pagePos);
    }

    private LambdaQueryWrapper<ShipmentPlanOrderPO> buildQueryWrapper(QueryShipPlan condition) {
        LambdaQueryWrapper<ShipmentPlanOrderPO> queryWrapper = new LambdaQueryWrapper<>();

        if (StringUtils.isNotBlank(condition.getOrderNo()) && Objects.nonNull(condition.getOrderType())) {
            if(condition.getOrderType().equals(QueryOrderTypeEnum.PD.getCode())){
                queryWrapper.likeRight(ShipmentPlanOrderPO::getPdNo, condition.getOrderNo());
            }else if(condition.getOrderType().equals(QueryOrderTypeEnum.PR.getCode())){
                queryWrapper.likeRight(ShipmentPlanOrderPO::getPrNo, condition.getOrderNo());
            }else if(condition.getOrderType().equals(QueryOrderTypeEnum.PO.getCode())){
                queryWrapper.likeRight(ShipmentPlanOrderPO::getPoNo, condition.getOrderNo());
            }
        }

        // 添加pdId 的查询条件
        if (CollectionUtils.isNotEmpty(condition.getPdIds())) {
            queryWrapper.in(ShipmentPlanOrderPO::getId, condition.getPdIds().stream().map(ShipmentPlanId::id).filter(Objects::nonNull).collect(Collectors.toList()));
        }
        // 添加supplierId 的查询条件
        queryWrapper.in(CollectionUtils.isNotEmpty(condition.getSupplierIds()), ShipmentPlanOrderPO::getSupplierId, condition.getSupplierIds());
        // 添加psku 的模糊查询条件
        queryWrapper.like(StringUtils.isNotBlank(condition.getPurchaseSku()), ShipmentPlanOrderPO::getPsku, condition.getPurchaseSku());
        // 添加ShipmentID 的模糊查询条件
        queryWrapper.like(StringUtils.isNotBlank(condition.getShipmentID()), ShipmentPlanOrderPO::getShipmentID, condition.getShipmentID());
        queryWrapper.like(StringUtils.isNotBlank(condition.getReferenceID()), ShipmentPlanOrderPO::getReferenceID, condition.getReferenceID());

        // 添加店铺ID 条件
        queryWrapper.in(CollectionUtils.isNotEmpty(condition.getStoreIds()), ShipmentPlanOrderPO::getStoreId, condition.getStoreIds());
        // 添加平台 ID 条件
        queryWrapper.in(CollectionUtils.isNotEmpty(condition.getPlatformIds()), ShipmentPlanOrderPO::getSalesChannelId, condition.getPlatformIds());
        // 添加出口国 条件
        queryWrapper.in(CollectionUtils.isNotEmpty(condition.getSiteCode()), ShipmentPlanOrderPO::getSiteCode, condition.getSiteCode());

        // 条码同步标志 条件
        queryWrapper.eq(Objects.nonNull(condition.getBarcodeSyncFlag()), ShipmentPlanOrderPO::getBarcodeSyncFlag, condition.getBarcodeSyncFlag());
        // 箱唛同步标志 条件
        queryWrapper.eq(Objects.nonNull(condition.getCartonMarkSyncFlag()), ShipmentPlanOrderPO::getCartonMarkSyncFlag, condition.getCartonMarkSyncFlag());
        // 邮件同步标志 条件
        queryWrapper.eq(Objects.nonNull(condition.getSendMailFlag()), ShipmentPlanOrderPO::getSendMailFlag, condition.getSendMailFlag());


        // 添加其他条件
        queryWrapper.eq(Objects.nonNull(condition.getStatus()), ShipmentPlanOrderPO::getStatus, condition.getStatus());
        queryWrapper.in(CollectionUtils.isNotEmpty(condition.getStatusList()), ShipmentPlanOrderPO::getStatus, condition.getStatusList());

        queryWrapper.in(CollectionUtils.isNotEmpty(condition.getPlanStaffIds()), ShipmentPlanOrderPO::getPlanStaffId, condition.getPlanStaffIds());
        queryWrapper.in(CollectionUtils.isNotEmpty(condition.getPurchaserStaffIds()), ShipmentPlanOrderPO::getPurchaserStaffId, condition.getPurchaserStaffIds());
        queryWrapper.in(CollectionUtils.isNotEmpty(condition.getOperationStaffIds()), ShipmentPlanOrderPO::getOperationStaffId, condition.getOperationStaffIds());

        // 处理日期类型条件
        if (Objects.nonNull(condition.getDateType()) && Objects.nonNull(condition.getDateStart()) && Objects.nonNull(condition.getDateEnd())) {
            switch (QueryShippingPlanDateTypeEnum.get(condition.getDateType())) {
                case CREATE_DATE:
                    queryWrapper.ge(ShipmentPlanOrderPO::getCreateTime, condition.getDateStart());
                    queryWrapper.le(ShipmentPlanOrderPO::getCreateTime, condition.getDateEnd());
                    break;
                case REVIEW_DATE:
                    queryWrapper.ge(ShipmentPlanOrderPO::getReviewTime, condition.getDateStart());
                    queryWrapper.le(ShipmentPlanOrderPO::getReviewTime, condition.getDateEnd());
                    break;
                case CONFIRMED_DATE:
                    queryWrapper.ge(ShipmentPlanOrderPO::getConfirmTime, condition.getDateStart());
                    queryWrapper.le(ShipmentPlanOrderPO::getConfirmTime, condition.getDateEnd());
                    break;
                case CANCELED_DATE:
                    queryWrapper.ge(ShipmentPlanOrderPO::getCancelTime, condition.getDateStart());
                    queryWrapper.le(ShipmentPlanOrderPO::getCancelTime, condition.getDateEnd());
                    break;
                default:
                    throw new IllegalArgumentException("Unexpected date type: " + condition.getDateType());
            }
        }
        // 添加数据权限控制条件
        if(!SecurityUtils.isAdmin()) {
            addDataPermission(queryWrapper, condition.getUserIdSet());
        }
        queryWrapper.orderByDesc(ShipmentPlanOrderPO::getUpdateTime);
        return queryWrapper;
    }

    private boolean buildQueryCondition(QueryShipPlan condition) {

        if (StringUtils.isNotBlank(condition.getFnSku()) || Objects.nonNull(condition.getLastDeliveryDateStart())|| Objects.nonNull(condition.getLastDeliveryDateEnd())
                || Objects.nonNull(condition.getExpectedPdDateStart())  || Objects.nonNull(condition.getExpectedPdDateEnd())|| CollectionUtils.isNotEmpty(condition.getLogisticsTypes())
                || CollectionUtils.isNotEmpty(condition.getShipWarehouseMethods()) || CollectionUtils.isNotEmpty(condition.getFirstLegMethods())) {

            List<ShipmentPlanId> pdIds = shipmentPlanItemLookup.findAllPdId(condition);
            if (CollectionUtils.isEmpty(pdIds)) {
                return Boolean.FALSE;
            }
            condition.setPdIds(pdIds);
        }

        // 获取 supplierIds 通过 supplier
        if (StringUtils.isNotBlank(condition.getSupplier())) {
            List<Integer> supplierIds = purchaseOrderSupplierInfoLookup.findDistinctSupplierIds(condition.getSupplier());
            if (CollectionUtils.isEmpty(supplierIds)) {
                return Boolean.FALSE;
            }
            condition.setSupplierIds(supplierIds);
        }
        return Boolean.TRUE;
    }

    /**
     * 出货计划数据权限：
     * 1、增加数据权限【采购】及【计划】角色需要根据PDS系统的产品管理绑定的 采购负责人 及 计划负责人
     * 拥有以上两个角色标签的用户，支持查看绑定的对应负责人相同的PR单据
     * 2、该数据权限与系统全局的用户数据权限求并集
     */
    private void addDataPermission(LambdaQueryWrapper<ShipmentPlanOrderPO> queryWrapper, Set<Integer> userIdSet) {
        Integer loginUserId = SecurityUtils.getUserId();
        // 登录用户加入可看的用户集合
        userIdSet.add(loginUserId);
        queryWrapper.and((wrapper) -> wrapper.in(ShipmentPlanOrderPO::getPlanStaffId, userIdSet).or().in(ShipmentPlanOrderPO::getPurchaserStaffId, userIdSet)
                .or().in(ShipmentPlanOrderPO::getOperationStaffId, userIdSet));
    }


    /**
     * 统计采购单状态数量
     */
    @Override
    public Map<Integer, Object> countByStatus(Set<Integer> userIdSet) {
        QueryWrapper<ShipmentPlanOrderPO> wrapper = Wrappers.query();
        LambdaQueryWrapper<ShipmentPlanOrderPO> lambda = wrapper.select( " status, COUNT(*) as count ").lambda();
        if(!SecurityUtils.isAdmin()) {
            this.addDataPermission(lambda, userIdSet);
        }
        lambda.groupBy(ShipmentPlanOrderPO::getStatus);
        List<ShipmentPlanOrderPO> result = this.baseMapper.selectList(wrapper);
        Map<Integer, Object> map = new HashMap<>();
        result.forEach(po -> map.put(po.getStatus(), po.getCount()));
        return map;
    }


    @Override
    public List<ShipmentPlanOrder> findByPdIds(Collection<ShipmentPlanId> ids) {
        Set<Integer> pdIds = CollectionUtils.emptyIfNull(ids).stream().filter(Objects::nonNull)
                .map(ShipmentPlanId::id).filter(Objects::nonNull).collect(Collectors.toCollection(TreeSet::new));
        if (pdIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<ShipmentPlanOrderPO> dbData = this.lambdaQuery().in(ShipmentPlanOrderPO::getId, pdIds)
                .eq(ShipmentPlanOrderPO::getDeleted, NumberUtils.INTEGER_ZERO)
                .list();
        if (CollectionUtils.isEmpty(dbData)){
            return List.of();
        }
        return dbData.stream().map(shipmentPlanOrderConverter::toDomain).toList();
    }

    /**
     * 根据poId查询
     *
     * @param poId 采购单Id
     * @return 待出库明细计划List
     */
    @Override
    public List<ShipmentPlanOrder> findByPoId(PurchaseOrderId poId) {
        return Optional.ofNullable(poId).map(PurchaseOrderId::id)
                .map(id-> this.lambdaQuery().eq(ShipmentPlanOrderPO::getPoId, id).list())
                .map(pos->pos.stream().map(shipmentPlanOrderConverter::toDomain).toList())
                .orElse(List.of());
    }

    @Override
    public List<ShipmentPlanOrder> findByPoIdList(Collection<Integer> poIdList) {
        Set<Integer> poIds = CollectionUtils.emptyIfNull(poIdList).stream().filter(Objects::nonNull)
                .collect(Collectors.toCollection(TreeSet::new));
        if (poIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<ShipmentPlanOrderPO> dbData = this.lambdaQuery().in(ShipmentPlanOrderPO::getPoId, poIds)
                .eq(ShipmentPlanOrderPO::getDeleted, NumberUtils.INTEGER_ZERO)
                .list();
        if (CollectionUtils.isEmpty(dbData)){
            return List.of();
        }
        return dbData.stream().map(shipmentPlanOrderConverter::toDomain).toList();
    }

    @Override
    public Map<Integer, List<ShipmentPlanOrder>> findPrMapByPoIdList(Collection<Integer> poIdList) {
        List<ShipmentPlanOrder> byPoIdList = findByPoIdList(poIdList);
        if (CollectionUtils.isEmpty(byPoIdList)){
            return Collections.emptyMap();
        }
        return byPoIdList.stream().collect(Collectors.groupingBy(ShipmentPlanOrder::getPrId));
    }

    @Override
    public Map<PurchaseOrderId, String> findReceiptDateByPoIds(Collection<PurchaseOrderId> poIds) {
        Set<Integer> poIdSet = CollectionUtils.emptyIfNull(poIds).stream().filter(Objects::nonNull)
                .map(PurchaseOrderId::id).filter(Objects::nonNull).collect(Collectors.toCollection(TreeSet::new));
        if (CollectionUtils.isEmpty(poIdSet)) {
            return Collections.emptyMap();
        }

        return this.lambdaQuery().in(ShipmentPlanOrderPO::getPoId, poIdSet)
                .eq(ShipmentPlanOrderPO::getDeleted, NumberUtils.INTEGER_ZERO)
                .select(ShipmentPlanOrderPO::getPoId, ShipmentPlanOrderPO::getReceiptDate)
                .list().stream()
                .filter(po -> po.getReceiptDate() != null)
                .collect(Collectors.groupingBy(
                        po -> new PurchaseOrderId(po.getPoId()),
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.stream()
                                        .map(ShipmentPlanOrderPO::getReceiptDate)
                                        .sorted()
                                        .map(Object::toString)
                                        .collect(Collectors.joining(","))
                        )
                ));
    }
}
