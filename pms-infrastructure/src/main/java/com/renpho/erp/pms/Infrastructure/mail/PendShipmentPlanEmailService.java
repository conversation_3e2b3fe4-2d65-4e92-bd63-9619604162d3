package com.renpho.erp.pms.Infrastructure.mail;

import com.renpho.erp.pms.Infrastructure.feign.RemoteFileFeign;
import com.renpho.erp.pms.Infrastructure.feign.saleschannel.SalesChannelLookup;
import com.renpho.erp.pms.Infrastructure.feign.supplier.RemotePurchaseSupplierFeign;
import com.renpho.erp.pms.Infrastructure.feign.user.OperatorLookup;
import com.renpho.erp.pms.Infrastructure.util.common.CustomBarcodePDFUtil;
import com.renpho.erp.pms.domain.commom.Operator;
import com.renpho.erp.pms.domain.product.Product;
import com.renpho.erp.pms.domain.purchaseorder.*;
import com.renpho.erp.pms.domain.saleschannel.SalesChannel;
import com.renpho.erp.pms.domain.shipplan.*;
import com.renpho.erp.pms.exception.BizErrorCode;
import com.renpho.erp.pms.model.common.model.enums.PmsFileBusinessType;
import com.renpho.erp.pms.model.common.model.enums.PmsOrderFileType;
import com.renpho.erp.pms.model.shipmentplan.model.mail.PoShipmentMailModel;
import com.renpho.erp.pms.model.shipmentplan.model.mail.ShipmentPlanMail;
import com.renpho.erp.srm.client.vo.PurchaseSupplierContactResp;
import com.renpho.erp.srm.client.vo.PurchaseSupplierDetailsResp;
import com.renpho.karma.exception.ApiException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;


/**
 * PD 邮件服务
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PendShipmentPlanEmailService {

    @Value("${spring.mail.defaultReceiver}")
    private String defaultReceiver;

    @Value("${spring.mail.allowSendOnline}")
    private Boolean allowSendOnline = true;

    private final TemplateEngine templateEngine;
    private final MailSenderService mailSenderService;

    private final PurchaseOrderLookup purchaseOrderLookup;
    private final PurchaseOrderSupplierInfoLookup purchaseOrderSupplierInfoLookup;
    private final ShipmentPlanOrderLookup shipmentPlanOrderLookup;
    private final ShipmentPlanOrderItemLookup shipmentPlanOrderItemLookup;
    private final OperatorLookup operatorLookup;
    private final SalesChannelLookup salesChannelLookup;
    private final RemoteFileFeign remoteFileFeign;
    private final PurchaseOrderFileLookup orderFileLookup;
    private final RemotePurchaseSupplierFeign remotePurchaseSupplierFeign;

    public boolean isValidEmail(String email) {
        String regex = "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$";
        return email != null && email.matches(regex);
    }

    /**
     * 发送PD邮件
     */
    public void sendMail(Integer poId, List<Integer> pdIds) {
        // 根绝POid查询最新的PO数据
        PurchaseOrder po = purchaseOrderLookup.findById(new PurchaseOrderId(poId)).orElse(null);
        PurchaseOrderSupplierInfo supplierInfo = purchaseOrderSupplierInfoLookup.findByPoId(new PurchaseOrderId(poId));
        if (po == null || supplierInfo == null) {
            throw new ApiException(BizErrorCode.DATA_DOES_NOT_EXIST);
        }

        Map<Integer, PurchaseSupplierDetailsResp> purchaseSupplierDetailsRespMap = remotePurchaseSupplierFeign.getPurchaseSupplierDetailsRespMap(List.of(supplierInfo.getSupplierId()));
        PurchaseSupplierDetailsResp purchaseSupplierDetailsResp = purchaseSupplierDetailsRespMap.get(supplierInfo.getSupplierId());

        List<ShipmentPlanId> list = pdIds.stream().distinct().map(ShipmentPlanId::new).toList();
        List<ShipmentPlanOrder> pdList = shipmentPlanOrderLookup.findByPdIds(list);
        Map<String, ShipmentPlanOrder> pdNoMap = pdList.stream().collect(Collectors.toMap(ShipmentPlanOrder::getPdNo, e -> e));
        Map<Integer, ShipmentPlanOrder> pdIdMap = pdList.stream().collect(Collectors.toMap(e -> e.getId().id(), e -> e));

        List<ShipmentPlanOrderItem> pdItemList = shipmentPlanOrderItemLookup.findByPdIdList(pdIds);
        Map<Integer, List<ShipmentPlanOrderItem>> pdItemMap = pdItemList.stream().collect(Collectors.groupingBy(ShipmentPlanOrderItem::getPdId));

        PoShipmentMailModel poMobileModel = new PoShipmentMailModel();
        poMobileModel.setPoNo(po.getPoNo());
        poMobileModel.setSupplierName(supplierInfo.getSupplierShortName());
        if (purchaseSupplierDetailsResp != null) {
            List<PurchaseSupplierContactResp> contactBOList = purchaseSupplierDetailsResp.getContactBOList();
            if (CollectionUtils.isNotEmpty(contactBOList)) {
                PurchaseSupplierContactResp purchaseSupplierContactResp = contactBOList.get(0);
                poMobileModel.setSupplierContact(purchaseSupplierContactResp.getContactName());
                poMobileModel.setSupplierEmail(purchaseSupplierContactResp.getEmail());
            }
        }
        poMobileModel.setPsku(Optional.ofNullable(po.getProduct()).map(Product::getPsku).orElse(null));

        // 总数 = pd 求和
        poMobileModel.setTotalQty(po.getPoQty());

        // 明细
        List<ShipmentPlanMail> details = pdList.stream().map(e -> {

            List<ShipmentPlanOrderItem> shipmentPlanOrderItems = pdItemMap.get(e.getId().id());
            ShipmentPlanOrderItem item = shipmentPlanOrderItems.get(0);

            ShipmentPlanMail pdMail = new ShipmentPlanMail();
            pdMail.setPdNo(e.getPdNo());
            pdMail.setWarehouse(e.getSiteCode());
            pdMail.setBarcode(item.getFnSku());
            pdMail.setQty(item.getPdQty());
            pdMail.setShipmentId(e.getShipmentID());
            int boxCount = (item.getPdQty() + item.getBoxPcs() - 1) / item.getBoxPcs();
            pdMail.setBoxCount(boxCount);
            return pdMail;
        }).toList();
        poMobileModel.setShipmentPlans(details);

        // 准备邮件内容
        Operator buyer = po.getBuyer();
        Set<String> receives = getOperatorEmails(buyer, po);

        String template = "[%s]出货资料（条码+箱唛）\n\n供应商：[%s]";
        String title = String.format(template, po.getPoNo(), supplierInfo.getSupplierShortName());

        String content = buildShipmentMailText(poMobileModel);

        List<MailAttachment> barcodeAttrs = buildStreamMailAttachment(pdItemList, pdIdMap);
        List<MailAttachment> attachments = new ArrayList<>(barcodeAttrs);

        // 获取PD 箱唛附件id
        List<String> pdNoList = pdList.stream().map(ShipmentPlanOrder::getPdNo).distinct().toList();
        Map<String, List<PurchaseOrderFile>> allByOrderNoList = orderFileLookup.findAllByOrderNoList(pdNoList, PmsFileBusinessType.PD_ORDER.getCode(), PmsOrderFileType.PD_ORDER_MARK.getCode());

        allByOrderNoList.forEach((pdNo, fileList) -> {
            // 根据附件id 获取文件url
            if (fileList.isEmpty()) {
                return;
            }
            ShipmentPlanOrder shipmentPlanOrder = pdNoMap.get(pdNo);
                for (PurchaseOrderFile file : fileList) {
                    String fileId = file.getFileId();
                    Map<String, String> fileInfo = remoteFileFeign.getFileInfo(List.of(fileId));
                    if (MapUtils.isNotEmpty(fileInfo) && StringUtils.isNotEmpty(fileInfo.get(fileId))) {
                        // 根据url 组装附件数据
                        String fileName = "【箱唛】" + shipmentPlanOrder.getShipmentID() + "_" + System.currentTimeMillis();
                        try {
                            MailAttachment downloadAttachment = downloadFromUrl(fileInfo.get(fileId), fileName, "application/pdf");
                            attachments.add(downloadAttachment);
                        } catch (IOException e) {
                            log.error("发邮件，下载箱唛文件失败");
                        }
                    }
                }
        });

        mailSenderService.sendWithAttachments(receives, null, title, content, attachments);
    }

    /**
     * 获取发送邮件的收件人
     * @param buyer
     * @param po
     * @return
     */
    private Set<String> getOperatorEmails(Operator buyer, PurchaseOrder po) {
        // 获取收件人邮箱
        Operator operator = operatorLookup.findById(buyer.getId()).orElse(null);
        if (operator == null) {
            log.error("PO[{}] 未找到采购人信息", po.getPoNo());
        }
        Set<String> receives = new HashSet<>();
        String receiver = null;
        // 在职员工才发送
        if (allowSendOnline && operator != null
                && operator.getStatus() == 1
                && StringUtils.isNotEmpty(operator.getEmail())) {
            receiver = operator.getEmail();
        }
        if (!isValidEmail(receiver)) {
            // 兜底邮箱
            receives = Arrays.stream(defaultReceiver.split(","))
                    .map(String::trim)
                    .filter(this::isValidEmail)
                    .collect(Collectors.toSet());
        } else {
            receives.add(receiver);
        }
        return receives;
    }


    /**
     * 构建流文件包
     */
    private List<MailAttachment> buildStreamMailAttachment(List<ShipmentPlanOrderItem> pdItemList, Map<Integer, ShipmentPlanOrder> pdIdMap) {
        ArrayList<MailAttachment> result = new ArrayList<>();

        // PD 按条码相同分组
        Map<String, List<ShipmentPlanOrderItem>> pdItemGroup = pdItemList.stream().collect(Collectors.groupingBy(ShipmentPlanOrderItem::getFnSku));
        pdItemGroup.forEach((fnSku, partPdItems) -> {

            ShipmentPlanOrderItem item = partPdItems.get(0);
            ShipmentPlanOrder shipmentPlanOrder = pdIdMap.get(item.getPdId());
            SalesChannel salesChannel = salesChannelLookup.findById(shipmentPlanOrder
                    .getSalesChannelId()).orElseThrow(() ->
                    new ApiException(BizErrorCode.DATA_DOES_NOT_EXIST));
            String chanelName = salesChannel.getChannelName();
            int groupTotalAmount = partPdItems.stream().mapToInt(ShipmentPlanOrderItem::getPdQty).sum();
            try (ByteArrayOutputStream baos = CustomBarcodePDFUtil.generateFnskuPDF(70f,
                    20f,
                    chanelName,
                    item.getFnSku(),
                    item.getPdId())) {
                String s = "【条码】" + item.getFnSku() + "_" + groupTotalAmount + "_" + System.currentTimeMillis();
                MailAttachment mailAttachment = new MailAttachment(s, baos.toByteArray(), "application/pdf");
                result.add(mailAttachment);
            } catch (IOException e) {
                log.error("发邮件生成条码异常");
            }
        });
        return result;
    }

    /**
     * 渲染出货资料通知邮件正文
     *
     * @param model 填充的模板数据（poNo、supplier、shipmentList等）
     * @return 渲染后的纯文本邮件内容
     */
    private String buildShipmentMailText(PoShipmentMailModel model) {
        Context context = new Context();

        context.setVariable("poNo", model.getPoNo());
        context.setVariable("supplierName", model.getSupplierName());
        context.setVariable("supplierContact", model.getSupplierContact());
        context.setVariable("supplierEmail", model.getSupplierEmail());
        context.setVariable("psku", model.getPsku());
        context.setVariable("totalQty", model.getTotalQty());
        context.setVariable("shipmentPlans", model.getShipmentPlans());

        return templateEngine.process("po-shipment-mail.txt", context);
    }

    /**
     * url获取文件流
     */
    public MailAttachment downloadFromUrl(String fileUrl, String filename, String contentType) throws IOException {
        URL url = new URL(fileUrl);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("GET");

        try (InputStream is = conn.getInputStream()) {
            byte[] bytes = is.readAllBytes();
            return new MailAttachment(filename, bytes, contentType);
        }
    }

}
