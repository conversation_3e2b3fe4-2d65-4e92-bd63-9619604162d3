package com.renpho.erp.pms.Infrastructure.persistence.shipplan.repository;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.pms.Infrastructure.persistence.shipplan.mapper.ShipmentPlanOrderMapper;
import com.renpho.erp.pms.Infrastructure.persistence.shipplan.po.ShipmentPlanOrderPO;
import com.renpho.erp.pms.Infrastructure.persistence.shipplan.po.coverter.ShipmentPlanOrderConverter;
import com.renpho.erp.pms.domain.shipplan.ShipmentPlanOrder;
import com.renpho.erp.pms.domain.shipplan.ShipmentPlanOrderRepository;
import com.renpho.erp.pms.domain.shipplan.dto.EditShipPlanFile;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class ShipmentPlanOrderRepositoryImpl extends ServiceImpl<ShipmentPlanOrderMapper, ShipmentPlanOrderPO> implements ShipmentPlanOrderRepository {

    private final ShipmentPlanOrderConverter shipmentPlanOrderConverter;

    private final ShipmentPlanOrderMapper shipmentPlanOrderMapper;
    /**
     * 批量审核 ,支持单个审核
     * @param pdNos 出库计划编码
     * @param opType 审核类型
     */
    @Override
    public void batchUpdateShipmentPlanOrderStatus(List<String> pdNos, Integer opType) {
        this.lambdaUpdate()
                .set(ShipmentPlanOrderPO::getStatus, opType)
                .set(ShipmentPlanOrderPO::getReviewTime, LocalDateTime.now())
                .in(ShipmentPlanOrderPO::getPdNo, pdNos)
                .update();
    }

    @Override
    public void batchCancelShipmentPlanOrderStatus(List<String> pdNos, Integer opType) {
        this.lambdaUpdate()
                .set(ShipmentPlanOrderPO::getStatus, opType)
                .set(ShipmentPlanOrderPO::getCancelTime, LocalDateTime.now())
                .in(ShipmentPlanOrderPO::getPdNo, pdNos)
                .update();
    }

    @Override
    public void batchConfirmShipmentPlanOrder(List<ShipmentPlanOrder> shipmentPlanOrders) {
        List<ShipmentPlanOrderPO> pos = shipmentPlanOrderConverter.toPos(shipmentPlanOrders);
        updateBatchById(pos);
    }


    /**
     * 文件同步标记更新
     * @param editShipPlanFile 入参
     */
    @Override
    public void updateFileSyncMark(EditShipPlanFile editShipPlanFile) {
        this.lambdaUpdate()
                .set(ShipmentPlanOrderPO::getBarcodeSyncFlag,editShipPlanFile.getBarcodeSyncFlag() )
                .set(ShipmentPlanOrderPO::getCartonMarkSyncFlag, editShipPlanFile.getCartonMarkSyncFlag())
                .in(ShipmentPlanOrderPO::getPdNo, editShipPlanFile.getPdNos())
                .update();
    }

    /**
     * 更新箱唛图片Id
     * @param pdNo 主键
     * @param cartonImageFileId 箱唛图片Id
     */
    @Override
    public void updateCartonImageFileId(String pdNo,String cartonImageFileId) {
        this.lambdaUpdate()
                .set(ShipmentPlanOrderPO::getCartonImageFileId, cartonImageFileId)
                .eq(ShipmentPlanOrderPO::getPdNo, pdNo)
                .update();
    }

    /**
     * 重置状态和箱唛更新标记
     */
    @Override
    public void resetStatusAndCartonSyncFlag(List<String> pdNos,Integer status,Integer cartonSyncFlag){
        this.lambdaUpdate()
                .set(ShipmentPlanOrderPO::getStatus, status)
                .set(ShipmentPlanOrderPO::getCartonMarkSyncFlag, cartonSyncFlag)
                .in(ShipmentPlanOrderPO::getPdNo, pdNos)
                .update();
    }

    @Override
    public void batchSave(List<ShipmentPlanOrder> addData) {
        addData.forEach(ShipmentPlanOrder::createPdNo);
        List<ShipmentPlanOrderPO> pos = shipmentPlanOrderConverter.toPos(addData);
        ShipmentPlanOrderRepositoryImpl bean = SpringUtil.getBean(ShipmentPlanOrderRepositoryImpl.class);
        bean.saveBatch(pos);
        Map<String, Integer> collect = pos.stream().collect(Collectors.toMap(ShipmentPlanOrderPO::getPdNo, ShipmentPlanOrderPO::getId));
        addData.forEach(item -> {
            item.getItem().setPdId(collect.get(item.getPdNo()));
            item.getItem().setPdNo(item.getPdNo());
        });
    }

    @Override
    public void updateMailFlag(List<Integer> poIds, Integer flag, String errorMsg) {
        this.lambdaUpdate()
                .set(ShipmentPlanOrderPO::getSendMailFlag, flag)
                .set(ShipmentPlanOrderPO::getMailErrorMsg, errorMsg)
                .in(ShipmentPlanOrderPO::getId, poIds)
                .update();
    }

    /**
     * 收货确认
     * @param shipmentPlanOrder 入参
     */
    @Override
    public void updateReceiptStatus(ShipmentPlanOrder shipmentPlanOrder) {
        this.lambdaUpdate()
               .set(ShipmentPlanOrderPO::getReceiptStatus, shipmentPlanOrder.getReceiptStatus())
               .set(ShipmentPlanOrderPO::getReceiptDate, shipmentPlanOrder.getReceiptDate())
               .eq(ShipmentPlanOrderPO::getPdNo, shipmentPlanOrder.getPdNo());
    }

    @Override
    public void updateShipmentId(String pdNo, String shipmentId) {
        shipmentPlanOrderMapper.updateShipmentId(pdNo, shipmentId);
    }


}
