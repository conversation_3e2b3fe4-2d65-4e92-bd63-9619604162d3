<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.renpho</groupId>
        <artifactId>renpho-build</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.renpho.erp</groupId>
    <artifactId>pms-parent</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>${project.artifactId}</name>

    <modules>
        <module>pms-domain</module>
        <module>pms-infrastructure</module>
        <module>pms-application</module>
        <module>pms-client</module>
        <module>pms-adapter</module>
        <module>pms-start</module>
        <module>pms-model</module>
    </modules>

    <properties>
        <!-- Project revision -->
        <revision>1.0.0-SNAPSHOT</revision>
        <erp-common.version>1.0.0-SNAPSHOT</erp-common.version>
        <!--  -->
        <karma.version>1.0.0-SNAPSHOT</karma.version>
        <soraka.version>1.0.0-SNAPSHOT</soraka.version>
        <itext-pdf.version>********</itext-pdf.version>
        <pdfbox.version>2.0.29</pdfbox.version>
        <apiproxy-sdk.version>1.1.0-SNAPSHOT</apiproxy-sdk.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- ==================================== -->
            <!-- 框架依赖 -->
            <!-- ==================================== -->
            <dependency>
                <groupId>com.renpho.soraka</groupId>
                <artifactId>soraka-dependencies</artifactId>
                <version>${soraka.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.renpho.soraka</groupId>
                <artifactId>soraka-bom</artifactId>
                <version>${soraka.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.renpho.karma</groupId>
                <artifactId>karma-bom</artifactId>
                <version>${karma.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- ==================================== -->
            <!-- ERP-公共业务中心（Public Business Center） -->
            <!-- ==================================== -->
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>erp-openfeign-sdk</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>erp-operator-log</artifactId>
                <version>1.1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>erp-security</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>erp-data-trans</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>erp-data-sensitive</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.renpho.erp</groupId>
                <artifactId>erp-generator</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.renpho.erp</groupId>
                <artifactId>bpm-api</artifactId>
                <version>1.1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.renpho.erp</groupId>
                <artifactId>erp-stream</artifactId>
                <version>1.1.0-SNAPSHOT</version>
            </dependency>
            <!-- ==================================== -->
            <!-- 模块依赖 -->
            <!-- ==================================== -->
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>pms-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>pms-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>pms-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>pms-model</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>pms-application</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>pms-adapter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>erp-data-permission</artifactId>
                <version>${erp-common.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>ftm-client</artifactId>
                <version>${erp-common.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>pds-client</artifactId>
                <version>${erp-common.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>srm-client</artifactId>
                <version>${erp-common.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>mdm-client</artifactId>
                <version>${erp-common.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>bpm-client</artifactId>
                <version>${erp-common.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>ims-client</artifactId>
                <version>${erp-common.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>oms-client</artifactId>
                <version>${erp-common.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>qms-client</artifactId>
                <version>${erp-common.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>tms-client</artifactId>
                <version>${erp-common.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>tms-model</artifactId>
                <version>${erp-common.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>fms-client</artifactId>
                <version>${erp-common.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>amazon-apiproxy-sdk</artifactId>
                <version>${apiproxy-sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>erp-biz-log</artifactId>
                <version>1.1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>openai-gpt-sdk</artifactId>
                <version>${erp-common.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>erp-instant-messaging</artifactId>
                <version>${erp-common.version}</version>
            </dependency>

            <!-- ==================================== -->
            <!-- 第三方依赖 -->
            <!-- ==================================== -->
            <!-- https://mvnrepository.com/artifact/com.itextpdf/itextpdf -->
            <!--A Free Java-PDF library-->
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itextpdf</artifactId>
                <version>${itext-pdf.version}</version>
            </dependency><!-- PDFBox -->
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>${pdfbox.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-mail</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-thymeleaf</artifactId>
                <version>3.2.4</version>
            </dependency>
            <!-- jackson 支持 Java 8 日期模块 -->
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>net.bytebuddy</groupId>
                    <artifactId>byte-buddy</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- smart-doc插件 -->
            <plugin>
                <groupId>com.ly.smart-doc</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <version>3.0.2</version>
                <configuration>
                    <!--指定生成文档的使用的配置文件-->
                    <configFile>${basedir}/smart-doc.json</configFile>
                    <!--指定项目名称-->
                    <projectName>pms项目</projectName>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
            </plugin>
            <!--
                        <plugin>
                            <groupId>io.spring.javaformat</groupId>
                            <artifactId>spring-javaformat-maven-plugin</artifactId>
                        </plugin>
                        <plugin>
                            <groupId>org.apache.maven.plugins</groupId>
                            <artifactId>maven-checkstyle-plugin</artifactId>
                        </plugin>
            -->

        </plugins>
    </build>
    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <url>http://renpho.master.com:10081/repository/maven-releases/</url>
        </repository>

        <snapshotRepository>
            <id>maven-snapshots</id>
            <url>http://renpho.master.com:10081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
