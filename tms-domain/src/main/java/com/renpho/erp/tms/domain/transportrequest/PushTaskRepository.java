package com.renpho.erp.tms.domain.transportrequest;

import com.renpho.erp.tms.domain.inbound.BizPskuContainer;
import org.jmolecules.ddd.types.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 物流计划推送任务表 Repository
 *
 * <AUTHOR>
 * @since 2025/7/14
 */
public interface PushTaskRepository extends Repository<PushTask, PushTaskId> {

    /**
     * 新增物流计划推送任务表
     *
     * @param pushTask 物流计划推送任务表
     * @return 主键ID
     */
    Integer add(PushTask pushTask);

    PushTask add(PushTaskAddCmd cmd, PushTaskStatus status);

    PushTask update(PushTask trPushTask);

    List<PushTask> addBatch(Collection<PushTaskAddCmd> commands);

    Optional<PushTask> update(BizPskuContainer biz, PushTaskType taskType, PushTaskStatus status, Integer retryCount, String errMsg);

}
