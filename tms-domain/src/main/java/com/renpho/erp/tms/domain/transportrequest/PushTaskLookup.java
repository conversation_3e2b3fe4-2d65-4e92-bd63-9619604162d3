package com.renpho.erp.tms.domain.transportrequest;

import com.renpho.erp.tms.domain.inbound.BizPskuWarehouseContainer;
import com.renpho.erp.tms.domain.inbound.InboundBusinessType;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import org.jmolecules.ddd.integration.AssociationResolver;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 物流计划推送任务表 Lookup
 *
 * <AUTHOR>
 * @since 2025/7/14
 */
public interface PushTaskLookup extends AssociationResolver<PushTask, PushTaskId> {

    List<PushTask> findByIds(Collection<PushTaskId> historyIds);

    List<PushTask> findByBizNo(String bizNo);

    Map<String, List<PushTask>> findByBizNos(Collection<String> bizNo);

    List<PushTask> findByBizNoAndPskuAndTaskType(String bizNo, Collection<String> pskus, Collection<PushTaskType> taskTypes);

    List<PushTask> findByBizNoAndPskuAndTaskType(String bizNo, Collection<String> pskus, Collection<PushTaskType> taskTypes, PushTaskStatus taskStatus);

    /**
     * 根据业务类型、仓库类型、任务类型、重试次数查询
     *
     * @param bizType        业务类型
     * @param warehouseType  仓库类型
     * @param taskType       任务类型
     * @param pushTaskStatus 推送状态
     */
    List<PushTask> findByTaskType(InboundBusinessType bizType, WarehouseProviderType warehouseType, PushTaskType taskType, PushTaskStatus pushTaskStatus);

    boolean isTasksExist(String trNo, Collection<PushTaskType> taskTypes);

    boolean isTasksExist(String trNo, Collection<PushTaskType> taskTypes, PushTaskStatus taskStatus);

    boolean isTasksExist(String trNo, Collection<String> pskus, Collection<PushTaskType> taskTypes);

    boolean isTasksExist(String trNo, Collection<String> pskus, Collection<PushTaskType> taskTypes, PushTaskStatus taskStatus);

    <T extends BizPskuWarehouseContainer> Map<String, PushTask> findLastFailureByBizNo(Collection<T> bizNos);

    /**
     * 根据业务类型、业务号列表、任务类型、推送状态查询
     *
     * @param inboundBusinessType 业务类型
     * @param bizNoList           业务号列表
     * @param pushTaskType        任务类型
     * @param pushTaskStatus      推送状态
     */
    List<PushTask> findByTaskTypeAndBizNo(InboundBusinessType inboundBusinessType, List<String> bizNoList, PushTaskType pushTaskType, PushTaskStatus pushTaskStatus);
}
