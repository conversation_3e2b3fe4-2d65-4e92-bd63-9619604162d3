package com.renpho.erp.tms.domain.transportrequest;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * 推送任务类型
 *
 * <AUTHOR>
 * @since 2025/7/15
 */
@Getter
@RequiredArgsConstructor
public enum PushTaskType {
    /**
     * 推送TR单号
     */
    TR_NO(true, PushTaskSystem.BROADCAST, List.of()),
    /**
     * 生成箱唛文件
     */
    CARTON_FILE_GENERATION(true, PushTaskSystem.BROADCAST, List.of(TR_NO)),
    /**
     * 推送箱唛文件ID
     */
    CARTON_FILE(true, PushTaskSystem.BROADCAST, List.of(CARTON_FILE_GENERATION)),
    /**
     * 推送交货时间
     */
    DELIVERY_TIME(true, PushTaskSystem.BROADCAST, List.of(CARTON_FILE)),
    /**
     * 交货动作
     */
    HANDOVER(true, PushTaskSystem.IMS, List.of(DELIVERY_TIME)),
    /**
     * 标记离港
     */
    DEPARTURE(true, PushTaskSystem.IMS, List.of(HANDOVER)),
    /**
     * 创建入库单
     */
    CREATE_INBOUND(false, PushTaskSystem.API, List.of(TR_NO)),
    /**
     * 入库
     */
    INBOUND(false, PushTaskSystem.TMS, List.of(CREATE_INBOUND)),
    /**
     * 入库完成
     */
    INBOUND_COMPLETE(true, PushTaskSystem.TMS, List.of(DELIVERY_TIME, INBOUND)),
    /**
     * 上架
     */
    PUTAWAY(false, PushTaskSystem.TMS, List.of(INBOUND, INBOUND_COMPLETE)),
    /**
     * 上架完成
     */
    PUTAWAY_COMPLETE(true, PushTaskSystem.TMS, List.of(INBOUND, INBOUND_COMPLETE, PUTAWAY)),
    /**
     * 取消
     */
    CANCEL(false, PushTaskSystem.API, List.of()),
    /**
     * 更新
     */
    UPDATE(false, PushTaskSystem.API, List.of()),
    /**
     * 费用分摊
     */
    ALLOCATION(false, PushTaskSystem.TMS, List.of(INBOUND)),
    /**
     * IMS 入库上架
     */
    INVENTORY(false, PushTaskSystem.IMS, List.of(ALLOCATION)),
    ;

    private final boolean trInitializable;
    private final PushTaskSystem targetSystem;
    private final List<PushTaskType> previous;

    public static List<PushTaskType> initializable() {
        return Arrays.stream(values()).filter(PushTaskType::isTrInitializable).toList();
    }
}
