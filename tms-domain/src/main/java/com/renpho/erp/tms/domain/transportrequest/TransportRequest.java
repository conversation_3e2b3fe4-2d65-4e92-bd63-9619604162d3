package com.renpho.erp.tms.domain.transportrequest;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.renpho.erp.generator.util.GeneratorCodeUtil;
import com.renpho.erp.tms.domain.common.CreatedContainer;
import com.renpho.erp.tms.domain.common.ReceiptGenerationType;
import com.renpho.erp.tms.domain.common.UpdatedContainer;
import com.renpho.erp.tms.domain.currency.Currency;
import com.renpho.erp.tms.domain.customsinfo.PskuCustomsInfo;
import com.renpho.erp.tms.domain.exchangerate.FluctuateExchangeRate;
import com.renpho.erp.tms.domain.firstleg.mode.FirstLegMode;
import com.renpho.erp.tms.domain.inbound.*;
import com.renpho.erp.tms.domain.operator.Operator;
import com.renpho.erp.tms.domain.owner.Owner;
import com.renpho.erp.tms.domain.product.BoxSpec;
import com.renpho.erp.tms.domain.product.Product;
import com.renpho.erp.tms.domain.product.ProductId;
import com.renpho.erp.tms.domain.saleschannel.SalesChannel;
import com.renpho.erp.tms.domain.shippingcompany.ShippingCompany;
import com.renpho.erp.tms.domain.site.CountryRegion;
import com.renpho.erp.tms.domain.store.Store;
import com.renpho.erp.tms.domain.supplier.PurchaseSupplier;
import com.renpho.erp.tms.domain.transportorder.TransportOrderId;
import com.renpho.erp.tms.domain.transportorder.TransportOrderStatusEnum;
import com.renpho.erp.tms.domain.warehouse.Warehouse;
import jakarta.annotation.Generated;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.jmolecules.ddd.types.AggregateRoot;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * TR（物流计划）单 domain
 *
 * <AUTHOR>
 * @since 2025/5/19
 */
@Data
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-05-19T18:06:01.*********+08:00[Asia/Shanghai]", comments = "Generator version: 7.12.0")
public class TransportRequest implements AggregateRoot<TransportRequest, TransportRequestId>, BizPskuWarehouseContainer, CreatedContainer, UpdatedContainer, Serializable {

    @Serial
    private static final long serialVersionUID = 2830913911518944127L;

    private final TransportRequestId id;

    /**
     * TR单号
     */
    private String trNo;

    /**
     * PD单ID
     */
    private Integer pdId;

    /**
     * 交付单号（PD单号）
     */
    private String pdNo;

    /**
     * PO单号
     */
    private String poNo;

    /**
     * PO单ID
     */
    private Integer poId;

    /**
     * TO单号
     */
    private String toNo;

    /**
     * TO单ID
     */
    private Integer toId;

    /**
     * TO单ID
     */
    private TransportOrderId transportOrderId;

    /**
     * 出运状态。字典: TO_STATUS。可选值:  1-询价中 2-订舱中 3-已订舱 4-货交承运人 5-已离港 6-已到港 7-已派送 8-已签收 9-已完成 10-已作废
     */
    private TransportOrderStatusEnum shipStatus;

    /**
     * 运单号
     */
    private String trackingNo;

    /**
     * QC单ID
     */
    private Integer qcId;

    /**
     * QC单号
     */
    private String qcNo;

    private TransportRequestStatus status;

    /**
     * 头程方式
     */
    private FirstLegMode firstLegMode;

    /**
     * 头程类型, 字典：first_leg_type
     */
    private String firstLegType;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 平台
     */
    private SalesChannel salesChannel;

    /**
     * 店铺
     */
    private Store store;

    /**
     * 货主
     */
    private Owner owner;

    /**
     * 服务商类型, 字典: logistics_type
     */
    private String carrierType;

    /**
     * 起运港, 字典: trade_terms_ship_to
     */
    private String shippingPort;

    /**
     * 贸易条款, 字典: trade_terms
     */
    private String tradeTerms;

    /**
     * 发运与入库, 字典: ship_warehouse_type
     */
    private String shipWarehouseMethod;

    /**
     * 发货仓
     */
    private Warehouse shippingWarehouse;

    /**
     * 目的国
     */
    private CountryRegion destCountry;

    /**
     * 目的仓
     */
    private Warehouse destWarehouse;

    /**
     * 目的仓Code（destWarehouse的code可能会和destWarehouseCode不同？）
     */
    private String destWarehouseCode;

    /**
     * 计划出货日期-开始
     */
    private LocalDate plannedShipStartDate;

    /**
     * 计划出货日期-结束
     */
    private LocalDate plannedShipEndDate;

    /**
     * 工厂交期
     */
    private LocalDate latestDeliveryDate;

    /**
     * 采购供应商（乙方）
     */
    private PurchaseSupplier purchaseSupplier;

    /**
     * 采购公司(甲方)
     */
    private Owner purchasingCompany;

    /**
     * ReferenceID
     */
    private String referenceId;

    /**
     * ShipmentID
     */
    private String shipmentId;

    /**
     * 质检结果
     */
    private QcResult qcResult;

    /**
     * 运营人员
     */
    private Operator salesStaff;

    /**
     * 计划人员
     */
    private Operator planningStaff;

    /**
     * 采购人员
     */
    private Operator purchaseStaff;

    /**
     * 箱唛文件ID
     */
    private Collection<String> cartonLabelFileIds;

    /**
     * 交货时间
     */
    private LocalDate deliveryTime;

    /**
     * 实际离港时间
     */
    private LocalDate actualDepartureTime;

    /**
     * 实际到港时间
     */
    private LocalDate actualArrivalTime;

    /**
     * 实际派送时间
     */
    private LocalDate actualDeliveryTime;

    /**
     * 签收时间(开始时间)
     */
    private LocalDateTime receivedTime;

    /**
     * 签收时间(结束时间)
     */
    private LocalDateTime receivedEndTime;

    /**
     * 签收数量
     */
    private Integer receivedQuantity;

    /**
     * 签收差异
     */
    private Integer receiptDiscrepancy;

    /**
     * 上架数量
     */
    private Integer shelvedQuantity;

    /**
     * 上架差异
     */
    private Integer shelvedDiscrepancy;

    /**
     * 预估运费
     */
    private BigDecimal estimatedFreight;

    /**
     * 预估运费币种
     */
    private Currency estimatedFreightCurrency;

    /**
     * 申报货值
     */
    private BigDecimal declaredValue;

    /**
     * 申报货值币种
     */
    private Currency declaredValueCurrency;

    /**
     * 申报货值汇率
     */
    private FluctuateExchangeRate declaredValueRate;

    /**
     * 预估税费
     */
    private BigDecimal estimatedTax;

    /**
     * 预估税费币种
     */
    private Currency estimatedTaxCurrency;

    /**
     * 产品信息
     */
    private Product product;

    private ProductId productId;

    private String psku;

    /**
     * 产品数量
     */
    private Integer quantity;

    /**
     * 产品箱数= PSKU数量 ÷ 装箱数量 【向上取整】
     */
    private BigDecimal boxQty;

    /**
     * 统计体积 = PSKU数量 × 单品长 × 单品宽 × 单品高【四舍五入，取3位小数】
     */
    private BigDecimal totalVolume;

    /**
     * 统计彩盒体积= PSKU数量 × 包装长 × 包装宽 × 包装高【四舍五入，取3位小数】
     */
    private BigDecimal totalPackageVolume;

    /**
     * 统计装箱体积 = 单箱长 × 单箱宽 × 单箱高 × PSKU 箱数【四舍五入，取3位小数】
     */
    private BigDecimal totalBoxVolume;

    /**
     * 统计毛重 = PSKU 箱数 × 整箱毛重【四舍五入，取3位小数】
     */
    private BigDecimal totalGrossWeight;

    /**
     * 统计净重 = PSKU 数量 × 单品净重【四舍五入，取3位小数】
     */
    private BigDecimal totalNetWeight;

    /**
     * 船司
     */
    private ShippingCompany shippingCompany;

    /**
     * 船名
     */
    private String vesselName;

    /**
     * 航次/航班号
     */
    private String voyageNo;

    /**
     * 出口查验 0:否 1:是
     */
    private Boolean exportInspection;

    /**
     * 进口查验 0:否 1:是
     */
    private Boolean importInspection;

    /**
     * 清关备注
     */
    private String customsClearanceRemark;

    /**
     * 销售链接
     */
    private Collection<String> skuMappings;

    /**
     * 创建信息
     */
    private Operator created;

    /**
     * 更新信息
     */
    private Operator updated;

    /**
     * tr单详情
     */
    private TransportRequestItem item;

    /**
     * 基础税费
     */
    private BigDecimal basicTax;

    /**
     * GST
     */
    private BigDecimal gst;

    /**
     * VAT
     */
    private BigDecimal vat;

    /**
     * 附加关税
     */
    private BigDecimal additionalCustomsTaxes;

    /**
     * 内部交易加价比例
     */
    private BigDecimal priceIncreaseRate;

    /**
     * 清关信息
     */
    private PskuCustomsInfo pskuCustomsInfo;

    /**
     * 装柜类型，字典类型：LOADING_TYPE。整柜、散货、自拼柜【FCL、LCL、Self】
     */
    private String loadingType;

    public Integer sourceId() {
        return this.id.id();
    }

    public void createTrNo() {
        this.trNo = GeneratorCodeUtil.generateIncr(ReceiptGenerationType.TR.name());
    }

    public void sumTotalVolume() {
        Optional.ofNullable(this.product)
                .flatMap(Product::calculateVolume)
                .flatMap(this::multiplyQuantity)
                .ifPresent(this::setTotalVolume);
    }

    public void sumPackageTotalVolume() {
        Optional.ofNullable(this.product)
                .flatMap(Product::calculatePackageVolume)
                .flatMap(this::multiplyQuantity)
                .ifPresent(this::setTotalPackageVolume);
    }

    public void sumBoxTotalVolume() {
        Optional.ofNullable(this.product)
                .flatMap(Product::calculateBoxVolume)
                .flatMap(this::multiplyBoxQuantity)
                .ifPresent(this::setTotalBoxVolume);
    }

    public void sumTotalNetWeight() {
        Optional.ofNullable(this.product)
                .map(Product::getWeightMetric)
                .flatMap(this::multiplyQuantity)
                .ifPresent(this::setTotalNetWeight);
    }

    public void sumTotalGrossWeight() {
        Optional.ofNullable(this.product)
                .map(Product::getActiveBoxSpec)
                .map(BoxSpec::getGrossWeightPerBoxMetric)
                .flatMap(this::multiplyBoxQuantity)
                .ifPresent(this::setTotalGrossWeight);
    }

    private Optional<BigDecimal> multiplyQuantity(BigDecimal num) {
        return Optional.ofNullable(num)
                .flatMap(n -> Optional.ofNullable(this.quantity).map(BigDecimal::new).map(n::multiply))
                .map(n -> n.setScale(3, RoundingMode.HALF_UP));
    }

    private Optional<BigDecimal> multiplyBoxQuantity(BigDecimal num) {
        return Optional.ofNullable(num)
                .flatMap(n -> Optional.ofNullable(this.boxQty).map(n::multiply))
                .map(n -> n.setScale(3, RoundingMode.HALF_UP));
    }

    public TransportRequest fillPriceIncreaseRate(BigDecimal priceIncreaseRate) {
        setPriceIncreaseRate(priceIncreaseRate.add(BigDecimal.ONE));
        return this;
    }

    public TransportRequest fillDeclaredValueRate(Map<String, FluctuateExchangeRate> usdRateMap) {
        String purchaseCostCurrency = item.getPurchaseCostCurrency();
        if (purchaseCostCurrency.equals("USD")) {
            setDeclaredValueRate(null);
        } else {
            Optional.ofNullable(usdRateMap.get(purchaseCostCurrency)).ifPresent(this::setDeclaredValueRate);
        }
        // 汇率
        BigDecimal rate = Optional.ofNullable(declaredValueRate).map(FluctuateExchangeRate::getExchangeRate).orElse(BigDecimal.ONE);
        // 申报货值 = PSKU 采购成本 × 内部交易加价比例 × 汇率 × 发货数量
        setDeclaredValue(item.getPurchaseCost().multiply(priceIncreaseRate).multiply(rate).multiply(BigDecimal.valueOf(item.getQuantity())));
        return this;
    }

    public TransportRequest fillPskuCustomsInfo(PskuCustomsInfo pskuCustomsInfo) {
        setPskuCustomsInfo(pskuCustomsInfo);
        item.setHsCodeAddTax(pskuCustomsInfo.getHsCodeAddTax());
        item.setHsCodeReducedTax(pskuCustomsInfo.getHsCodeReducedTax());
        // GST = 申报货值 × GST税率
        setGst(declaredValue.multiply(pskuCustomsInfo.getGst().movePointLeft(2)));
        // VAT = 申报货值 × VAT税率
        setVat(declaredValue.multiply(pskuCustomsInfo.getVat().movePointLeft(2)));
        // 基础税费
        setBasicTax(declaredValue.multiply(pskuCustomsInfo.getBaseDutyRate().movePointLeft(2)));
        // 附加关税
        setAdditionalCustomsTaxes(declaredValue.multiply(pskuCustomsInfo.sumAvailableAdditionalDutyRates().movePointLeft(2)));
        return this;
    }

    /**
     * 根据仓库服务商类型判断仓库API类型
     *
     * <AUTHOR>
     * @since 2025/7/16
     */
    public WarehouseProviderType findWarehouseProviderType() {
        Optional<Warehouse> warehouse = Optional.ofNullable(this.destWarehouse);
        Optional<WarehouseProviderType> serviceProvider = warehouse.map(Warehouse::getServiceProvider).map(WarehouseProviderType::fromServiceProvider);
        if (serviceProvider.isPresent()) {
            return serviceProvider.get();
        }
        Optional<WarehouseType> type = warehouse.map(Warehouse::getType).map(WarehouseType::fromDictItem);
        // 自建仓
        if (type.filter(t -> WarehouseType.SELF_BUILT == t).isPresent()) {
            return WarehouseProviderType.WMS;
        }
        // 平台仓
        Optional<Store> store = Optional.ofNullable(this.store);
        if (type.filter(t -> WarehouseType.PLATFORM == t).isPresent()) {
            Optional<String> channelCode = Optional.ofNullable(this.salesChannel).map(SalesChannel::getChannelCode);
            if (store.map(Store::getChannelWarehouseIds)
                    .stream()
                    .flatMap(Collection::stream)
                    .filter(Objects::nonNull)
                    .anyMatch(wid -> warehouse.map(Warehouse::getId).map(w -> Objects.equals(wid, w)).orElse(false))) {
                // Amazon FBA
                if (channelCode.filter(c -> StringUtils.equalsIgnoreCase(c, "AMZ")).isPresent()) {
                    return WarehouseProviderType.FBA;
                }
                // 沃尔玛
                if (channelCode.filter(c -> StringUtils.equalsIgnoreCase(c, "WMT")).isPresent()) {
                    return WarehouseProviderType.WFS;
                }
                // Tiktok
                if (channelCode.filter(c -> StringUtils.equalsIgnoreCase(c, "TT")).isPresent()) {
                    return WarehouseProviderType.FBT;
                }
            }
            // Amazon AWD
            if (store.map(Store::getAwdWarehouseIds)
                    .stream()
                    .flatMap(Collection::stream)
                    .filter(Objects::nonNull)
                    .anyMatch(wid -> warehouse.map(Warehouse::getId).map(w -> Objects.equals(wid, w)).orElse(false))) {
                return WarehouseProviderType.AWD;
            }
        }
        throw new WarehouseProviderNotFound(this);
    }

    @Override
    public Integer getBizId() {
        return Optional.ofNullable(this.id).map(TransportRequestId::id).orElse(null);
    }

    @Override
    public String getBizNo() {
        return this.trNo;
    }

    @Override
    public InboundBusinessType getBizType() {
        return InboundBusinessType.TR;
    }

    @Override
    public TransportRequestId getTrId() {
        return this.id;
    }

    @Override
    public String getFnSku() {
        return Optional.ofNullable(this.item).map(TransportRequestItem::getProduct).map(Product::getFnSku).orElse(null);
    }

    public boolean isInboundFinished() {
        if (findWarehouseProviderType() == WarehouseProviderType.WMS) {
            int receiptDiscrepancy = Optional.ofNullable(this.receiptDiscrepancy).orElse(0);
            int receivedQuantity = Optional.ofNullable(this.receivedQuantity).orElse(0);
            int received = receivedQuantity + receiptDiscrepancy;
            return Optional.ofNullable(quantity).filter(q -> Objects.equals(q, received)).isPresent();
        } else {
            return Optional.ofNullable(this.item)
                    .map(TransportRequestItem::getReceivedQuantity)
                    .filter(q -> Objects.equals(q, quantity))
                    .isPresent();
        }
    }

    public boolean isPutAwayFinished() {
        int receiptDiscrepancy = Optional.ofNullable(this.shelvedDiscrepancy).orElse(0);
        int receivedQuantity = Optional.ofNullable(this.shelvedQuantity).orElse(0);
        int received = receivedQuantity + receiptDiscrepancy;
        return Optional.ofNullable(quantity).filter(q -> Objects.equals(q, received)).isPresent();
    }

    @Override
    @JSONField(serialize = false, deserialize = false)
    @JsonIgnore
    public WarehouseProviderType getWarehouseType() {
        return findWarehouseProviderType();
    }
}

