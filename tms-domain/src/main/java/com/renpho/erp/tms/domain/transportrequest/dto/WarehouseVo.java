package com.renpho.erp.tms.domain.transportrequest.dto;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * 仓库表
 *
 * <AUTHOR>
 * @date 2024/10/18
 */
@Data
public class WarehouseVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    private String contactName;
    /**
     * 主键
     */
    private Integer id;
    /**
     * 仓库代码
     */
    private String code;
    private Integer type;
    private String typeName;
    /**
     * 状态：0 禁用；1 启用
     */
    private Integer status;
    private String statusName;
    /**
     * 国家编码
     */
    private String countryCode;
    /**
     * 省/州
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 区
     */
    private String area;
    /**
     * 地址
     */
    private String address;
    /**
     * 邮政编码
     */
    private String zipCode;
    /**
     * 联系电话
     */
    private String contactNumber;
    /**
     * 邮箱
     */
    private String contactEmail;

    /**
     * 三方仓库编码
     */
    private String thirdWarehouseCode;

    /**
     * 审核状态:0 待审批、1 审批中、2 审批通过、3 审批不通过
     */
    private Integer auditStatus;

    /**
     * 审核状态名称
     */
    private String auditStatusName;

    /**
     * 仓库负责人ID 多个,号隔开
     */
    private String managerUserId;

    /**
     * 仓库负责人 多个,号隔开
     */
    private String managerUserName;

    /**
     * 计划负责人ID 多个,号隔开
     */
    private String planManagerUserId;

    /**
     * 计划负责人 多个,号隔开
     */
    private String planManagerUserName;

    /**
     * 审批流程实例ID
     */
    private String processInstanceId;

    /**
     * 创建人ID
     *
     */
    private Integer createBy;
    private String createByName;
    private String createByCode;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新人ID
     */
    private Integer updateBy;
    private String updateByName;
    private String updateByCode;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 是否删除：0 否、1 是；默认 0
     */
    private Integer isDeleted;
    private String name;

    private String contactCode;
    private String countryName;
    private Object languages;
    private Integer serviceProvider;
    private String serviceProviderName;
    private Object configJSONDto;
}
