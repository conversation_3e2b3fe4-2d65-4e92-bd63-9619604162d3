package com.renpho.erp.tms.domain.inbound;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/7/15
 */
@RequiredArgsConstructor
@Getter
public enum WarehouseProviderType {
    /**
     * 自建 WMS API
     */
    WMS(Integer.MAX_VALUE, WarehouseType.SELF_BUILT),
    /**
     * 京东海外仓 API
     */
    JD(0, WarehouseType.THIRD_PARTY),
    /**
     * 极智佳(易仓YunWMS API)
     */
    POLARIS_YUNWMS(4, WarehouseType.THIRD_PARTY),
    /**
     * KingSpark(易仓YunWMS API)
     */
    KING_SPARK_YUNWMS(1, WarehouseType.THIRD_PARTY),
    /**
     * 乐鱼(领星 API)
     */
    LEYU_XLWMS(2, WarehouseType.THIRD_PARTY),
    /**
     * FBA (平台仓-Amazon API, Fulfillment By Amazon)
     */
    FBA(Integer.MAX_VALUE, WarehouseType.PLATFORM),
    /**
     * AWD (平台仓-Amazon API, Amazon Warehouse Dispatch)
     */
    AWD(Integer.MAX_VALUE, WarehouseType.PLATFORM),
    /**
     * FBT (平台仓-TikTok API, Fulfilled By Tiktok)
     */
    FBT(Integer.MAX_VALUE, WarehouseType.PLATFORM),
    /**
     * WFS(平台仓-Walmart API, Walmart Fulfillment Services)
     */
    WFS(Integer.MAX_VALUE, WarehouseType.PLATFORM),
    /**
     * 手动
     */
    DEFAULT(Integer.MAX_VALUE, WarehouseType.NOT_EXIST),
    /**
     * 不存在
     */
    NOT_EXIST(Integer.MAX_VALUE, WarehouseType.NOT_EXIST),
    ;

    /**
     * 服务商字典值, 0-京东, 4-极智佳, 1-KingSpark, 2-乐鱼, OTHERS-其他
     */
    private final Integer serviceProvider;

    /**
     * 仓库类型字典值, 1-平台仓, 2-三方仓, 3-自建仓, 4-虚拟仓, 5-办事处, 6-寄售仓
     */
    private final WarehouseType warehouseType;

    public static WarehouseProviderType fromServiceProvider(Integer serviceProvider) {
        return Arrays.stream(values())
                .filter(e -> Objects.equals(e.getServiceProvider(), serviceProvider))
                .findAny()
                .orElse(null);
    }

    //通过name获取WarehouseProviderType
    public static WarehouseProviderType fromName(String name) {
        return Arrays.stream(values())
                .filter(e -> Objects.equals(e.name(), name))
                .findAny()
                .orElse(null);
    }

}
