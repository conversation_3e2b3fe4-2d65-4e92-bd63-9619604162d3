<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 1999-2018 Alibaba Group Holding Ltd.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<configuration scan="true" scanPeriod="10 seconds">

    <springProperty scope="context" name="logPath" source="nacos.logs.path" defaultValue="${nacos.home}/logs"/>
    <property name="LOG_HOME" value="${logPath}"/>

    <appender name="cmdb-main"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${nacos.home}/logs/cmdb-main.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${nacos.home}/logs/cmdb-main.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>2GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>7GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="naming-server"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/naming-server.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/naming-server.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>1GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>7GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="async-naming-server" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>1024</queueSize>
        <neverBlock>true</neverBlock>
        <appender-ref ref="naming-server"/>
    </appender>

    <appender name="naming-raft"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/naming-raft.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/naming-raft.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>1GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="async-naming-raft" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>1024</queueSize>
        <neverBlock>true</neverBlock>
        <appender-ref ref="naming-raft"/>
    </appender>


    <appender name="naming-distro"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/naming-distro.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/naming-distro.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>1GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="async-naming-distro" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>1024</queueSize>
        <neverBlock>true</neverBlock>
        <appender-ref ref="naming-distro"/>
    </appender>

    <appender name="naming-event"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/naming-event.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/naming-event.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>1GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="async-naming-event" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>1024</queueSize>
        <neverBlock>true</neverBlock>
        <appender-ref ref="naming-event"/>
    </appender>

    <appender name="naming-push"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/naming-push.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/naming-push.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>1GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <appender name="naming-rt"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/naming-rt.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/naming-rt.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>1GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%msg%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="naming-performance"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/naming-performance.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/naming-performance.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>1GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!--config module logback config-->
    <appender name="dumpFile"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/config-dump.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/config-dump.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>2GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>7GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <appender name="pullFile"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/config-pull.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/config-pull.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>20MB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>128MB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <appender name="fatalFile"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/config-fatal.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/config-fatal.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>20MB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>128MB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <appender name="memoryFile"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/config-memory.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/config-memory.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>20MB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>128MB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <appender name="pullCheckFile"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/config-pull-check.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/config-pull-check.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>1GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%msg%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="clientLog"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/config-client-request.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/config-client-request.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>2GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>7GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date|%msg%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="traceLog"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/config-trace.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/config-trace.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>2GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>7GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date|%msg%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="notifyLog"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/config-notify.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/config-notify.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>1GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="startLog"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/config-server.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/config-server.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>512MB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="rootFile"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/nacos.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/nacos.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>512MB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="nacos-address"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/nacos-address.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/nacos-address.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>2GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>7GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="istio-main"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/istio-main.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/istio-main.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>2GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>7GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="core-auth"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/core-auth.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/core-auth.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>2GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>7GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="protocol-raft"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/protocol-raft.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/protocol-raft.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>2GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>7GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="protocol-distro"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/protocol-distro.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/protocol-distro.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>2GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>7GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="nacos-cluster"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/nacos-cluster.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/nacos-cluster.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>2GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>7GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="alipay-jraft"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/alipay-jraft.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/alipay-jraft.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>2GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>7GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    
    <!--TPS control -->
    <appender name="tps-control"
        class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/tps-control.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/tps-control.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>2GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>7GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <appender name="tps-control-digest"
        class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/tps-control-digest.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/tps-control-digest.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>2GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>7GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <appender name="tps-control-detail"
        class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/tps-control-detail.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/tps-control-detail.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>2GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>7GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    
    <appender name="remote"
        class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/remote.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/remote.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>2GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>7GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <appender name="remote-digest"
        class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/remote-digest.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/remote-digest.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>2GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>7GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <appender name="remote-push"
        class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/remote-push.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/remote-push.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>2GB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>7GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <Pattern>%date %level %msg%n%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    
    <logger name="com.alibaba.nacos.address.main" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="nacos-address"/>
    </logger>

    <logger name="com.alibaba.nacos.cmdb.main" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="cmdb-main"/>
    </logger>
    
    <logger name="com.alibaba.nacos.core.remote" additivity="false">
        <level value="DEBUG"/>
        <appender-ref ref="remote"/>
    </logger>
    <logger name="com.alibaba.nacos.core.remote.push" additivity="false">
        <level value="DEBUG"/>
        <appender-ref ref="remote-push"/>
    </logger>
    
    <logger name="com.alibaba.nacos.core.remote.digest" additivity="false">
        <level value="DEBUG"/>
        <appender-ref ref="remote-digest"/>
    </logger>
    
    <!-- TPS Control-->
    <logger name="com.alibaba.nacos.core.remote.control.digest" additivity="false">
        <level value="DEBUG"/>
        <appender-ref ref="tps-control-digest"/>
    </logger>
    
    <logger name="com.alibaba.nacos.core.remote.control.detail" additivity="false">
        <level value="DEBUG"/>
        <appender-ref ref="tps-control-detail"/>
    </logger>
    
    <logger name="com.alibaba.nacos.core.remote.control" additivity="false">
        <level value="DEBUG"/>
        <appender-ref ref="tps-control"/>
    </logger>
    
    <logger name="com.alibaba.nacos.naming.main" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="async-naming-server"/>
    </logger>
    <logger name="com.alibaba.nacos.naming.raft" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="async-naming-raft"/>
    </logger>
    <logger name="com.alibaba.nacos.naming.distro" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="async-naming-distro"/>
    </logger>
    <logger name="com.alibaba.nacos.naming.event" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="async-naming-event"/>
    </logger>
    <logger name="com.alibaba.nacos.naming.push" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="naming-push"/>
    </logger>
    <logger name="com.alibaba.nacos.naming.rt" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="naming-rt"/>
    </logger>
    <logger name="com.alibaba.nacos.naming.performance" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="naming-performance"/>
    </logger>

    <logger name="com.alibaba.nacos.config.dumpLog" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="dumpFile"/>
    </logger>
    <logger name="com.alibaba.nacos.config.pullLog" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="pullFile"/>
    </logger>
    <logger name="com.alibaba.nacos.config.pullCheckLog" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="pullCheckFile"/>
    </logger>
    <logger name="com.alibaba.nacos.config.fatal" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="fatalFile"/>
    </logger>
    <logger name="com.alibaba.nacos.config.monitorLog" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="memoryFile"/>
    </logger>

    <logger name="com.alibaba.nacos.config.clientLog" additivity="false">
        <level value="info"/>
        <appender-ref ref="clientLog"/>
    </logger>

    <logger name="com.alibaba.nacos.config.notifyLog" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="notifyLog"/>
    </logger>

    <logger name="com.alibaba.nacos.config.traceLog" additivity="false">
        <level value="info"/>
        <appender-ref ref="traceLog"/>
    </logger>

    <logger name="com.alibaba.nacos.config.startLog" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="startLog"/>
    </logger>

    <logger name="com.alibaba.nacos.istio.main" additivity="false">
        <level value="DEBUG"/>
        <appender-ref ref="istio-main"/>
    </logger>

    <logger name="com.alibaba.nacos.core.auth" additivity="false">
        <level value="DEBUG"/>
        <appender-ref ref="core-auth"/>
    </logger>

    <logger name="com.alibaba.nacos.core.protocol.raft" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="protocol-raft"/>
    </logger>

    <logger name="com.alipay.sofa.jraft" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="alipay-jraft"/>
    </logger>

    <logger name="com.alibaba.nacos.core.protocol.distro" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="protocol-distro"/>
    </logger>

    <logger name="com.alibaba.nacos.core.cluster" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="nacos-cluster"/>
    </logger>

    <springProfile name="standalone">
        <logger name="org.springframework">
            <appender-ref ref="CONSOLE"/>
            <level value="INFO"/>
        </logger>

        <logger name="org.apache.catalina.startup.DigesterFactory">
            <appender-ref ref="CONSOLE"/>
            <level value="INFO"/>
        </logger>

        <logger name="org.apache.catalina.util.LifecycleBase">
            <appender-ref ref="CONSOLE"/>
            <level value="ERROR"/>
        </logger>

        <logger name="org.apache.coyote.http11.Http11NioProtocol">
            <appender-ref ref="CONSOLE"/>
            <level value="WARN"/>
        </logger>

        <logger name="org.apache.tomcat.util.net.NioSelectorPool">
            <appender-ref ref="CONSOLE"/>
            <level value="WARN"/>
        </logger>
    </springProfile>

    <logger name="com.alibaba.nacos.core.listener.StartingApplicationListener">
        <appender-ref ref="CONSOLE"/>
        <level value="INFO"/>
    </logger>

    <logger name="com.alibaba.nacos.common.notify.NotifyCenter">
        <appender-ref ref="CONSOLE"/>
        <level value="INFO"/>
    </logger>

    <logger name="com.alibaba.nacos.sys.file.WatchFileCenter">
        <appender-ref ref="CONSOLE"/>
        <level value="INFO"/>
    </logger>

    <logger name="com.alibaba.nacos.common.executor.ThreadPoolManager">
        <appender-ref ref="CONSOLE"/>
        <level value="INFO"/>
    </logger>

    <root>
        <level value="INFO"/>
        <appender-ref ref="rootFile"/>
    </root>
</configuration>

