package com.renpho.erp.tms.adapter.web.job.TransportRequest;

import com.renpho.erp.tms.application.transportrequest.job.wms.TransportRequestWmsAddService;
import com.renpho.erp.tms.application.transportrequest.job.wms.TransportRequestWmsCancelService;
import com.renpho.erp.tms.application.transportrequest.job.wms.TransportRequestWmsDeliveryService;
import com.renpho.erp.tms.application.transportrequest.job.wms.TransportRequestWmsUpdateService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * TR-目的仓为WMS的定时器.
 * <AUTHOR>
 * @since 2025.07.25
 */
@SuppressWarnings("unused")
@Slf4j
@Component
@RequiredArgsConstructor
public class TransportRequestWmsJob {

    private final TransportRequestWmsAddService transportOrderWmsService;
    private final TransportRequestWmsUpdateService transportOrderWmsUpdateService;
    private final TransportRequestWmsCancelService transportOrderWmsCancelService;
    private final TransportRequestWmsDeliveryService transportOrderWmsDeliveryService;

    /**
     * TR-目的仓为WMS的推送任务生成-Add.
     * <br/>执行频率：
     */
    @XxlJob("createInboundWmsAdd")
    public void createInboundWmsTaskAdd() throws Exception {
        transportOrderWmsService.createInboundWmsTask();
    }

    /**
     * 执行: TR-目的仓为WMS的入库单任务-Add.
     * <br/>执行频率：
     * @param trNoList tr单号集合，用于异常重试的参数触发机制
     */
    @XxlJob("doingInboundWmsAdd")
    public void doingInboundWmsAdd(List<String> trNoList) throws Exception {
        transportOrderWmsService.doingInboundWms(trNoList);
    }

    /**
     * 执行: TR-目的仓为WMS的箱唛任务-Add.
     * <br/>执行频率：
     * @param trNoList tr单号集合，用于异常重试的参数触发机制
     */
    @XxlJob("doingPalletWmsAdd")
    public void doingPalletWmsAdd(List<String> trNoList) throws Exception {
        transportOrderWmsService.doingPalletWms(trNoList);
    }

    /**
     * TR-目的仓为WMS的推送任务生成-Update.
     * <br/>执行频率：
     */
    @XxlJob("createInboundWmsUpdate")
    public void createInboundWmsTaskUpdate() throws Exception {
        transportOrderWmsUpdateService.createInboundWmsTask();
    }

    /**
     * 执行: TR-目的仓为WMS的入库单任务-Update.
     * <br/>执行频率：
     * @param trNoList tr单号集合，用于异常重试的参数触发机制
     */
    @XxlJob("doingInboundWmsUpdate")
    public void doingInboundWmsUpdate(List<String> trNoList) throws Exception {
        transportOrderWmsUpdateService.doingInboundWms(trNoList);
    }

    /**
     * TR-目的仓为WMS的推送任务生成-Cancel.
     * <br/>执行频率：
     */
    @XxlJob("createInboundWmsCancel")
    public void createInboundWmsTaskCancel() throws Exception {
        transportOrderWmsCancelService.createInboundWmsTask();
    }

    /**
     * 执行: TR-目的仓为WMS的入库单任务-Update.
     * <br/>执行频率：
     * @param trNoList tr单号集合，用于异常重试的参数触发机制
     */
    @XxlJob("doingInboundWmsCancel")
    public void doingInboundWmsCancel(List<String> trNoList) throws Exception {
        transportOrderWmsCancelService.doingInboundWms(trNoList);
    }

    /**
     * TR-目的仓为WMS的推送任务生成-Delivery.
     * <br/>执行频率：
     */
    @XxlJob("createInboundWmsDelivery")
    public void createInboundWmsTaskDelivery() throws Exception {
        transportOrderWmsDeliveryService.createInboundWmsTask();
    }

    /**
     * 执行: TR-目的仓为WMS的入库单任务-Delivery.
     * <br/>执行频率：
     * @param trNoList tr单号集合，用于异常重试的参数触发机制
     */
    @XxlJob("doingInboundWmsDelivery")
    public void doingInboundWmsDelivery(List<String> trNoList) throws Exception {
        transportOrderWmsDeliveryService.doingInboundWms(trNoList);
    }

}
