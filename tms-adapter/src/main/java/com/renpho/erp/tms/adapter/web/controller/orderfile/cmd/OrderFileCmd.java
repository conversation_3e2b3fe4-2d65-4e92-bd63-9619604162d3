package com.renpho.erp.tms.adapter.web.controller.orderfile.cmd;

import com.renpho.erp.tms.adapter.web.controller.command.group.CreateGroup;
import com.renpho.erp.tms.adapter.web.controller.command.group.OtherGroup;
import com.renpho.erp.tms.adapter.web.controller.command.group.QueryGroup;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 附件
 *
 * <AUTHOR> Zheng
 * @date 2025/6/16
 */
@Data
public class OrderFileCmd implements Serializable {

    /**
     * 业务单据id
     */
    @NotNull(message = "orderId.is.not.null", groups = {CreateGroup.class, QueryGroup.class})
    private Integer orderId;

    /**
     * 业务单据号
     */
    @NotBlank(message = "orderNo.is.not.null", groups = {CreateGroup.class})
    private String orderNo;

    /**
     * 业务类型：
     * TO-头程单
     */
    @NotBlank(message = "businessType.is.not.null", groups = {CreateGroup.class, QueryGroup.class})
    private String businessType;

    /**
     * 文件类型：
     * FREIGHT_QUOTE-货代报价单；
     * BILL_OF_LADING-提单；
     * INSURANCE_POLICY-保单；
     * POD-POD；
     * CUSTOMS_DECLARATION-报关单；
     * PACKING_LIST-箱单；
     * INVOICE-发票；
     * CUSTOMS_TAX_RECEIPT-海关税单；
     * CERTIFICATE_OF_ORIGIN-产地证；
     * OTHER-其他文件
     */
    @NotBlank(message = "fileType.is.not.null", groups = {CreateGroup.class})
    private String fileType;

    /**
     * 文件id
     */
    @Valid
    @NotEmpty(message = "fileInfos.is.not.null", groups = {CreateGroup.class, OtherGroup.class})
    @Size(max = 10, message = "fileInfos.max.size.is.10", groups = {CreateGroup.class})
    private List<OrderFileInfoCmd> fileInfos;

}
