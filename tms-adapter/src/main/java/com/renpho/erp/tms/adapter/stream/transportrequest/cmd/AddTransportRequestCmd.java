package com.renpho.erp.tms.adapter.stream.transportrequest.cmd;

import com.renpho.erp.tms.domain.firstleg.mode.FirstLegMode;
import com.renpho.erp.tms.domain.firstleg.mode.FirstLegModeId;
import com.renpho.erp.tms.domain.inbound.*;
import com.renpho.erp.tms.domain.operator.OperatorId;
import com.renpho.erp.tms.domain.owner.Owner;
import com.renpho.erp.tms.domain.owner.OwnerId;
import com.renpho.erp.tms.domain.product.Product;
import com.renpho.erp.tms.domain.product.ProductId;
import com.renpho.erp.tms.domain.saleschannel.SalesChannel;
import com.renpho.erp.tms.domain.saleschannel.SalesChannelId;
import com.renpho.erp.tms.domain.site.CountryRegion;
import com.renpho.erp.tms.domain.store.Store;
import com.renpho.erp.tms.domain.store.StoreId;
import com.renpho.erp.tms.domain.supplier.PurchaseSupplier;
import com.renpho.erp.tms.domain.supplier.SupplierId;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestType;
import com.renpho.erp.tms.domain.warehouse.Warehouse;
import com.renpho.erp.tms.domain.warehouse.WarehouseId;
import jakarta.annotation.Generated;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.jmolecules.architecture.cqrs.Command;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/5/19
 */
@Getter
@Setter
@Command
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-05-19T18:06:01.197175700+08:00[Asia/Shanghai]", comments = "Generator version: 7.12.0")
public class AddTransportRequestCmd implements Serializable, BizPskuWarehouseContainer {

    @Serial
    private static final long serialVersionUID = -7872667495405833381L;

    /**
     * PD 单 ID
     */
    @NotNull
    private Integer pdId;

    /**
     * PD单号
     */
    @NotBlank
    private String pdNo;

    /**
     * PO 单 ID
     */
    @NotNull
    private Integer poId;

    /**
     * PO 单号
     */
    @NotBlank
    private String poNo;

    /**
     * 头程方式 ID
     */
    private FirstLegModeId firstLegModeId;

    private FirstLegMode firstLegMode;

    /**
     * 头程方式
     * 字符串类型（待PMS改造）
     */
    @NotNull
    private String firstLegMethod;

    /**
     * 头程类型字典值，字典：first_leg_type
     */
    @NotBlank
    private String firstLegType;

    /**
     * 业务类型
     */
    @NotNull
    private TransportRequestType businessType;

    /**
     * 平台 ID
     */
    @NotNull
    private SalesChannelId salesChannelId;

    private SalesChannel salesChannel;

    /**
     * 店铺 ID
     */
    @NotNull
    private StoreId storeId;

    private Store store;

    /**
     * 店铺已启用货主
     */
    private Owner owner;

    /**
     * 采购供应商（乙方） ID
     */
    @NotNull
    private SupplierId purchaseSupplierId;

    private PurchaseSupplier purchaseSupplier;

    /**
     * 采购公司（甲方） ID
     */
    @NotNull
    private OwnerId purchaserOwnerId;

    private Owner purchaserOwner;

    /**
     * 起运港字典值, 字典: trade_terms_ship_to
     */
    @NotBlank
    private String shippingPort;

    /**
     * 贸易条款字典值, 字典: trade_terms
     */
    @NotBlank
    private String tradeTerms;

    /**
     * 服务商类型字典值, 字典: logistics_type
     */
    @NotBlank
    private String carrierType;

    /**
     * 发运与入库字典值, 字典: ship_warehouse_type
     */
    @NotBlank
    private String shipWarehouseMethod;

    /**
     * 发货仓 ID
     */
    private WarehouseId shippingWarehouseId;

    private Warehouse shippingWarehouse;

    /**
     * 目的国/地区编码
     */
    @NotBlank
    private String destCountryCode;

    private CountryRegion destCountry;

    /**
     * 目的仓 ID
     */
    @NotNull
    private WarehouseId destWarehouseId;

    private Warehouse destWarehouse;

    /**
     * 目的仓Code
     */
    @NotBlank
    private String destWarehouseCode;

    /**
     * 计划出货日期-开始
     */
    @NotNull
    private LocalDate plannedShipStartDate;

    /**
     * 计划出货日期-结束
     */
    @NotNull
    private LocalDate plannedShipEndDate;

    /**
     * 工厂交期
     */
    @NotNull
    private LocalDate latestDeliveryDate;

    /**
     * ReferenceId
     */
    private String referenceId;

    /**
     * ShipmentId
     */
    private String shipmentId;

    /**
     * 箱唛文件ID
     */
    @Valid
    private Set<@Valid @NotBlank String> cartonLabelFileIds;

    /**
     * 运营人员 ID
     */
    @NotNull
    private OperatorId salesStaffId;

    /**
     * 计划人员 ID
     */
    @NotNull
    private OperatorId planningStaffId;

    /**
     * 采购人员 ID
     */
    @NotNull
    private OperatorId purchaseStaffId;

    @NotBlank
    private String psku;

    private ProductId productId;

    private Product product;

    @NotBlank
    private String fnsku;

    /**
     * 型号
     */
    private String model;

    /**
     * 品牌
     */

    private String brand;

    /**
     * 产品数量
     */
    @NotNull
    @Positive
    private Integer quantity;

    /**
     * 产品箱数
     */
    @NotNull
    @Positive
    private Integer boxQty;

    /**
     * 尺寸单位
     */
    @NotBlank
    private String dimensionUnit;

    /**
     * 单品尺寸-长
     */
    @NotNull
    @Positive
    private BigDecimal productLength;

    /**
     * 单品尺寸-宽
     */
    @NotNull
    @Positive
    private BigDecimal productWidth;

    /**
     * 单品尺寸-高
     */
    @NotNull
    @Positive
    private BigDecimal productHeight;

    /**
     * 包装尺寸(含彩盒)-长
     */
    @NotNull
    @Positive
    private BigDecimal packagingLength;

    /**
     * 包装尺寸(含彩盒)-宽
     */
    @NotNull
    @Positive
    private BigDecimal packagingWidth;

    /**
     * 包装尺寸(含彩盒)-高
     */
    @NotNull
    @Positive
    private BigDecimal packagingHeight;

    /**
     * 重量单位
     */
    @NotBlank
    private String weightUnit;

    /**
     * 单品净重
     */
    @NotNull
    @Positive
    private BigDecimal weight;

    /**
     * 单品毛重
     */
    @NotNull
    @Positive
    private BigDecimal grossWeight;

    /**
     * 装箱数量
     */
    @NotNull
    @Positive
    private Integer quantityPerBox;

    /**
     * 外箱尺寸-长
     */
    @NotNull
    @Positive
    private BigDecimal boxLengthMetric;

    /**
     * 外箱尺寸-宽
     */
    @NotNull
    @Positive
    private BigDecimal boxWidthMetric;

    /**
     * 外箱尺寸-高
     */
    @NotNull
    @Positive
    private BigDecimal boxHeightMetric;

    /**
     * 整箱毛重
     */
    @NotNull
    @Positive
    private BigDecimal boxGrossWeight;

    /**
     * 是否整箱
     */
    @NotNull
    private Boolean isFullBox;

    /**
     * 敏感属性
     */
    private String hazardousPros;

    /**
     * 采购成本
     */
    @NotNull
    @Positive
    private BigDecimal purchaseCost;

    /**
     * 采购成本币种
     */
    @NotBlank
    private String purchaseCostCurrencyCode;

    @Override
    public String getBizNo() {
        return this.pdNo;
    }

    @Override
    public Integer getBizId() {
        return this.pdId;
    }

    @Override
    public InboundBusinessType getBizType() {
        return InboundBusinessType.PD;
    }

    @Override
    public String getFnSku() {
        return this.fnsku;
    }

    @Override
    public WarehouseProviderType getWarehouseType() {
        Optional<Warehouse> warehouse = Optional.ofNullable(this.destWarehouse);
        Optional<WarehouseId> warehouseId = warehouse.map(Warehouse::getId);
        Optional<WarehouseProviderType> serviceProvider = warehouse.map(Warehouse::getServiceProvider).map(WarehouseProviderType::fromServiceProvider);
        if (serviceProvider.isPresent()) {
            return serviceProvider.get();
        }
        Optional<WarehouseType> type = warehouse.map(Warehouse::getType).map(WarehouseType::fromDictItem);
        // 自建仓
        if (type.filter(t -> WarehouseType.SELF_BUILT == t).isPresent()) {
            return WarehouseProviderType.WMS;
        }
        // 平台仓
        Optional<Store> store = Optional.ofNullable(this.store);
        if (type.filter(t -> WarehouseType.PLATFORM == t).isPresent()) {
            Optional<String> channelCode = Optional.ofNullable(this.salesChannel).map(SalesChannel::getChannelCode);
            if (store.map(Store::getChannelWarehouseIds)
                    .stream()
                    .flatMap(Collection::stream)
                    .filter(Objects::nonNull)
                    .anyMatch(wid -> warehouseId.filter(w -> Objects.equals(wid, w)).isPresent())) {
                // Amazon FBA
                if (channelCode.filter(c -> StringUtils.equalsIgnoreCase(c, "AMZ")).isPresent()) {
                    return WarehouseProviderType.FBA;
                }
                // 沃尔玛
                if (channelCode.filter(c -> StringUtils.equalsIgnoreCase(c, "WMT")).isPresent()) {
                    return WarehouseProviderType.WFS;
                }
                // Tiktok
                if (channelCode.filter(c -> StringUtils.equalsIgnoreCase(c, "TT")).isPresent()) {
                    return WarehouseProviderType.FBT;
                }
            }
            // Amazon AWD
            if (store.map(Store::getAwdWarehouseIds)
                    .stream()
                    .flatMap(Collection::stream)
                    .filter(Objects::nonNull)
                    .anyMatch(wid -> warehouseId.filter(w -> Objects.equals(wid, w)).isPresent())) {
                return WarehouseProviderType.AWD;
            }
        }
        return null;
    }
}
