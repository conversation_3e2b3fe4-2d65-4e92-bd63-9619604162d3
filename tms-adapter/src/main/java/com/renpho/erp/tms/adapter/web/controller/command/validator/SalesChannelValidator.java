package com.renpho.erp.tms.adapter.web.controller.command.validator;

import com.renpho.erp.tms.adapter.web.controller.command.container.SalesChannelIdContainer;
import com.renpho.erp.tms.domain.saleschannel.SalesChannelId;
import com.renpho.erp.tms.infrastructure.common.converter.BooleanConverter;
import com.renpho.erp.tms.infrastructure.remote.saleschannel.repository.SalesChannelLookup;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.lang.annotation.Annotation;
import java.util.Optional;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @since 2025/3/18
 */
public abstract class SalesChannelValidator<A extends Annotation, T> implements ConstraintValidator<A, T> {

    protected Predicate<T> predicate;

    @Resource
    protected SalesChannelLookup salesChannelLookup;

    @Resource
    protected BooleanConverter booleanConverter;

    @Override
    public boolean isValid(T value, ConstraintValidatorContext context) {
        return salesChannelLookup == null || predicate.test(value);
    }

    public static class SalesChannelIdExist extends SalesChannelValidator<SalesChannelExist, SalesChannelIdContainer> {

        @Override
        public void initialize(SalesChannelExist constraintAnnotation) {
            predicate = cmd -> Optional.ofNullable(cmd)
                    .map(SalesChannelIdContainer::getSalesChannelId)
                    .map(SalesChannelId::new)
                    .flatMap(salesChannelLookup::findById)
                    .filter(c -> booleanConverter.toBoolean(c.getStatus()))
                    .isPresent();
        }
    }

}
