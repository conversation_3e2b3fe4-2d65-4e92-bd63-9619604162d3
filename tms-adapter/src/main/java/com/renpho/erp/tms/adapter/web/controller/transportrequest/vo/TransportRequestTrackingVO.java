package com.renpho.erp.tms.adapter.web.controller.transportrequest.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/11
 */
@Data
public class TransportRequestTrackingVO implements VO {
    @JsonIgnore
    private Integer id;

    /**
     * 运单号
     */
    private String trackingNo;

    /**
     * TR单号
     */
    private List<String> trNos;

    /**
     * TO单号
     */
    private String toNo;

    /**
     * 平台 ID
     */
    private Integer salesChannelId;


    /**
     * 平台名称
     */
    private String salesChannelName;


    /**
     * 货主 ID
     */
    private Integer ownerId;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 货主
     */
    private List<String> owners;


    /**
     * 发运与入库字典值, 字典: ship_warehouse_type
     */
    @Trans(type = TransType.DICTIONARY, key = "ship_warehouse_type", ref = "shipWarehouseMethodName")
    private String shipWarehouseMethod;

    /**
     * 发运与入库
     */
    private String shipWarehouseMethodName;

    /**
     * 工厂交期
     */
    private LocalDate latestDeliveryDate;

    /**
     * 实际离岗时间
     */
    private LocalDate actualDepartureTime;

    /**
     * 实际到港时间
     */
    private LocalDate actualArrivalTime;

    /**
     * 实际派送时间
     */
    private LocalDate actualDeliveryTime;

    /**
     * 采购供应商简称
     */
    private List<String> purchaseSupplierShortNames;

    /**
     * ShipmentId
     */
    private List<String> shipmentIds;

    /**
     * 总数量
     */
    private Integer totalQty;

    /**
     * 总箱数
     */
    private Integer totalBoxQty;

    /**
     * 总毛重 kg
     */
    private BigDecimal totalGrossWeight;

    /**
     * 总体积 m³
     */
    private BigDecimal totalVolume;

    /**
     * 出口查验 0:否 1:是
     */
    private Boolean exportInspection;

    /**
     * 进口查验 0:否 1:是
     */
    private Boolean importInspection;

    /**
     * 清关备注
     */
    private String customsClearanceRemark;
}
