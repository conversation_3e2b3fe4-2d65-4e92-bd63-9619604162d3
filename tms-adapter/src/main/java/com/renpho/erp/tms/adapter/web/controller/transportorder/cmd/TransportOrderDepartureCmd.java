package com.renpho.erp.tms.adapter.web.controller.transportorder.cmd;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2025/6/12
 */
@Getter
@Setter
public class TransportOrderDepartureCmd {
    /**
     * to单Id
     */
    @NotNull
    private Integer toId;

    /**
     * TR单号
     */
    @NotNull
    private Integer trId;

    /**
     * 实际离港时间
     */
    @NotNull
    private LocalDate actualDepartureTime;

    /**
     * 船司(简称)
     */
    private String shippingCompany;

    /**
     * 船名
     */
    private String vesselName;

    /**
     * 航次/航班号
     */
    private String voyageNo;

}
