package com.renpho.erp.tms.adapter.web.controller.customsinfo.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.renpho.erp.tms.adapter.web.controller.vo.serializer.BigDecimalScale2RoundingSerializer;
import com.renpho.erp.tms.domain.customsinfo.PskuAdditionalDutyStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Generated;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * PSKU 清关信息 VO
 */

@Getter
@Setter
@Schema(name = "PskuCustomsInfoVo", description = "PSKU 清关信息 VO")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-04-22T12:09:54.179410400+08:00[Asia/Shanghai]", comments = "Generator version: 7.12.0")
public class PskuCustomsInfoVo implements VO, Serializable {

    @Serial
    private static final long serialVersionUID = -111874950734350388L;

    private Integer id;

    /**
     * 产品 ID
     */
    @Schema(name = "productId", description = "产品 ID")
    private Integer productId;

    /**
     * PSKU
     */
    @Schema(name = "psku", description = "PSKU")
    private String psku;

    /**
     * 图片 ID
     */
    @Schema(name = "picture", description = "图片 ID")
    private String picture;

    /**
     * 国家/地区编码
     */
    @Schema(name = "countryCode", description = "国家/地区编码")
    private String countryCode;

    /**
     * 国家/地区名称
     */
    @Schema(name = "countryName", description = "国家/地区名称")
    private String countryName;

    /**
     * 海关编码-加税
     */
    @Schema(name = "hsCodeAddTax", description = "海关编码-加税")
    private String hsCodeAddTax;

    /**
     * 海关编码-减税
     */
    @Schema(name = "hsCodeReducedTax", description = "海关编码-减税")
    private String hsCodeReducedTax;

    /**
     * 币种
     */
    @Schema(name = "currencyCode", description = "币种")
    private String currencyCode;

    /**
     * 其他
     */
    @Schema(name = "others", description = "其他")
    @JsonSerialize(using = BigDecimalScale2RoundingSerializer.class)
    private BigDecimal others;

    /**
     * 基本税率
     */
    @Schema(name = "baseDutyRate", description = "基本税率")
    @JsonSerialize(using = BigDecimalScale2RoundingSerializer.class)
    private BigDecimal baseDutyRate;

    /**
     * GST（商品服务税）
     */
    @Schema(name = "gst", description = "GST（商品服务税）")
    @JsonSerialize(using = BigDecimalScale2RoundingSerializer.class)
    private BigDecimal gst;

    /**
     * VAT（增值税）
     */
    @Schema(name = "vat", description = "VAT（增值税）")
    @JsonSerialize(using = BigDecimalScale2RoundingSerializer.class)
    private BigDecimal vat;

    /**
     * 状态字典值, 字典: psku_customs_info_status
     */
    @Schema(name = "status", description = "状态字典值, 字典: psku_customs_info_status")
    @Trans(type = TransType.DICTIONARY, key = "psku_customs_info_status", ref = "statusName")
    private Integer status;

    /**
     * 状态
     */
    @Schema(name = "status", description = "状态")
    private String statusName;

    /**
     * 备注
     */
    @Schema(name = "remark", description = "备注")
    private String remark;

    /**
     * 附加关税
     */
    @Schema(name = "remark", description = "备注")
    private List<PskuCustomsAdditionalDutyVO> additionalDuties;

    /**
     * 按状态统计附加关税合计税率
     */
    @Schema(name = "totalAdditionalDutyByStatus", description = "按状态统计附加关税合计税率")
    private List<PskuCustomsAdditionalDutySumUpVO> totalAdditionalDutyByStatus;

    /**
     * 创建人 ID
     */
    @Schema(name = "createBy", description = "创建人 ID")
    private Integer createBy;

    /**
     * 创建人名称
     */
    @Schema(name = "createByName", description = "创建人名称")
    private String createByName;

    /**
     * 创建人工号
     */
    @Schema(name = "createByCode", description = "创建人工号")
    private String createByCode;

    /**
     * 创建时间
     */
    @Schema(name = "createTime", description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人 ID
     */
    @Schema(name = "updateBy", description = "更新人 ID")
    private Integer updateBy;

    /**
     * 更新人名称
     */
    @Schema(name = "updateByName", description = "更新人名称")
    private String updateByName;

    /**
     * 更新人工号
     */
    @Schema(name = "updateByCode", description = "更新人工号")
    private String updateByCode;

    /**
     * 更新时间
     */
    @Schema(name = "updateTime", description = "更新时间")
    private LocalDateTime updateTime;

    public void sum() {
        Map<Integer, PskuCustomsAdditionalDutySumUpVO> groupByStatus = new HashMap<>();
        for (PskuCustomsAdditionalDutyVO vo : CollectionUtils.emptyIfNull(this.additionalDuties)) {
            if (vo != null) {
                if (Objects.equals(vo.getStatus(), PskuAdditionalDutyStatus.EXPIRED.getValue())) {
                    continue;
                }
                groupByStatus.computeIfAbsent(vo.getStatus(), PskuCustomsAdditionalDutySumUpVO::new).add(vo);
            }
        }

        this.totalAdditionalDutyByStatus = groupByStatus.values()
                .stream()
                .sorted(Comparator.comparing(PskuCustomsAdditionalDutySumUpVO::getEffectStatus))
                .toList();
    }
}

