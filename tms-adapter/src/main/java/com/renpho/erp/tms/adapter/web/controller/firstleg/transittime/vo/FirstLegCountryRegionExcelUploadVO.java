package com.renpho.erp.tms.adapter.web.controller.firstleg.transittime.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.renpho.erp.tms.adapter.web.controller.firstleg.mode.command.validator.CountryRegionExist;
import com.renpho.erp.tms.adapter.web.controller.firstleg.mode.command.validator.FirstLegModeExist;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025/4/25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FirstLegCountryRegionExcelUploadVO {

    /**
     * 头程方式
     */
    @ExcelProperty(value = "*头程方式", index = 0)
    @NotBlank(message = "头程方式不能为空；")
    @FirstLegModeExist
    private String firstLegMode;
    @ExcelIgnore
    private Integer modeId;

    /**
     * 国家(地区)编码
     */
    @ExcelProperty(value = "*国家(地区)", index = 1)
    @NotBlank(message = "国家(地区)不能为空；")
    @CountryRegionExist
    private String countryCode;

    /**
     * 区域
     */
    @ExcelProperty(value = "区域(US必填)", index = 2)
    @Size(max = 20, message = "区域不能超过20个字符；")
    private String area;

    /**
     * 最小时效(天)
     */
    @ExcelProperty(value = "*最小时效(天)", index = 3)
    @NotBlank(message = "最小时效(天)不能为空；")
    private String minTransitTime;

    /**
     * 最大时效(天)
     */
    @ExcelProperty(value = "*最大时效(天)", index = 4)
    @NotBlank(message = "最大时效(天)不能为空；")
    private String maxTransitTime;

    /**
     * 状态
     */
    @ExcelProperty(value = "*状态", index = 5)
    @NotBlank(message = "状态不能为空；")
    @Pattern(regexp = "启用|禁用", message = "请填写启用或者禁用；")
    private String statusName;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注", index = 6)
    @Size(max = 100,message = "请填写100个字符以内；")
    private String remark;
}
