package com.renpho.erp.tms.adapter.web.job.TransportOrder;

import com.renpho.erp.tms.application.transportorder.TransportOrderCheckService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 头程定时器任务.
 * <AUTHOR>
 * @since 2025.06.19
 */
@SuppressWarnings("unused")
@Slf4j
@Component
@RequiredArgsConstructor
public class TransportOrderJob {

    private final TransportOrderCheckService transportOrderCheckService;

    /**
     * 头程异常状态检查.
     * <br/>执行频率：每天0点执行一次，一天执行4次，每次间隔6小时.
     */
    @XxlJob("checkExceptionStatusTransportOrder")
    public void checkExceptionStatus() {
        try {
            log.info("头程异常状态检查开始");
            transportOrderCheckService.check();
        } catch (Exception e) {
            log.error("头程检查异常",e);
        }

    }

}
