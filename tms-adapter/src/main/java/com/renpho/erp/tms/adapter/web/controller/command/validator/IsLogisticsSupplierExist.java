package com.renpho.erp.tms.adapter.web.controller.command.validator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * <AUTHOR>
 * @since 2025/6/17
 */
@Documented
@Constraint(validatedBy = {LogisticsSupplierValidator.IsLogisticsSupplierIdExist.class})
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER, ElementType.TYPE_USE})
@Retention(RUNTIME)
public @interface IsLogisticsSupplierExist {

    String message() default "{logistics_supplier.not.exist}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
