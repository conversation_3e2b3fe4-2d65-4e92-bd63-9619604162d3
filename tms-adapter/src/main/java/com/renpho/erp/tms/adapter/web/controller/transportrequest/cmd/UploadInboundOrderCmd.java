package com.renpho.erp.tms.adapter.web.controller.transportrequest.cmd;

import com.renpho.erp.tms.adapter.web.controller.command.container.FileIdsContainer;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.container.TrIdContainer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Generated;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.io.Serializable;
import java.util.Collection;
import java.util.Set;

/**
 * 上传入库单参数
 *
 * <AUTHOR>
 * @since 2025/5/19
 */
@lombok.Getter
@lombok.Setter
@Schema(name = "uploadInboundOrderCmd", description = "上传入库单参数")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-05-19T18:06:01.197175700+08:00[Asia/Shanghai]", comments = "Generator version: 7.12.0")
public class UploadInboundOrderCmd implements FileIdsContainer<Collection<String>, String>, TrIdContainer, Serializable {

    @Serial
    private static final long serialVersionUID = -2599520872484120943L;

    /**
     * TR单ID
     */
    @Schema(name = "id", description = "TR单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private Integer id;

    /**
     * 入库单号
     */
    @Schema(name = "inboundOrderNo", description = "入库单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank
    private String inboundOrderNo;

    /**
     * 箱唛文件ID
     */
    @Schema(name = "cartonLabelFileId", description = "箱唛文件ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @Valid
    @NotEmpty
    private Set<@Valid @NotBlank String> cartonLabelFileIds;

    @Override
    public Set<String> getFileIds() {
        return cartonLabelFileIds;
    }
}

