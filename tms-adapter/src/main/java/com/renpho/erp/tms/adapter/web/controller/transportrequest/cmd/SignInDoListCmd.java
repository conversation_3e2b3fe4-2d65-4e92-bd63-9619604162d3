package com.renpho.erp.tms.adapter.web.controller.transportrequest.cmd;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;


/**
 * 运输计划-tr单-签收.
 * <AUTHOR>
 * @since 2025/7/22
 */
@Data
public class SignInDoListCmd {

    /**
     * 签收列表
     */
    @Valid
    @NotEmpty(message = "{error.tr.sign.info-required}")
    private List<SignInDoCmd> signInList;

}
