package com.renpho.erp.tms.adapter.web.controller.basic.config.port.info.cmd;

import com.renpho.karma.dto.PageQuery;
import lombok.Data;

import java.util.List;

/**
 * 港口配置精确查询
 *
 * <AUTHOR>
 * @since 2025/6/11
 */
@Data
public class PortInfoCmd extends PageQuery {

    /**
     * 港口英文名,模糊匹配
     */
    private String portNameEn;

    /**
     * 港口中文名,模糊匹配
     */
    private String portNameCn;

    /**
     * 国家/地区 集合
     */
    private List<String> countryCodeList;

    /**
     * 区域
     */
    private String district;

    /**
     * 港口代码
     */
    private String portCode;

    /**
     * 港口类型，字典类型：PORT_TYPE，海港：SEAPORT，内陆港：INLAND_PORT，空港：AIRPORT
     */
    private String portType;

    /**
     * 状态，0=无效，1=有效
     */
    private Integer status;

}
