package com.renpho.erp.tms.adapter.web.controller.transportorder.cmd;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.command.ShippingFeeFormulaConfigIdCmd;
import com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.container.TransportOrderIdsContainer;
import com.renpho.erp.tms.domain.transportorder.TransportOrder;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/6/19
 */
@Getter
@Setter
public class CalculateShippingFeeCmd extends ShippingFeeFormulaConfigIdCmd implements TransportOrderIdsContainer<Collection<Integer>> {

    @Serial
    private static final long serialVersionUID = 4846478821687646091L;

    /**
     * 运费公式变动项参数(不包税)
     */
    @NotEmpty
    private List<@Valid @NotNull ArgCmd> argsNoTax;

    /**
     * 运费公式变动项参数(包税)
     */
    @NotEmpty
    private List<@Valid @NotNull ArgCmd> argsWithTax;

    /**
     * TO 单 ID 数组
     */
    @NotEmpty
    private Set<@Valid @NotNull Integer> toIds;

    @JsonIgnore
    private List<TransportOrder> tos;

    @Override
    @JsonIgnore
    public Set<Integer> getIds() {
        return toIds;
    }

    @Override
    @JsonIgnore
    public void setTos(Collection<TransportOrder> tos) {
        this.tos = new ArrayList<>(tos);
    }

}
