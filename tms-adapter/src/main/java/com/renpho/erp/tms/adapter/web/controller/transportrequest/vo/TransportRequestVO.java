package com.renpho.erp.tms.adapter.web.controller.transportrequest.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.github.houbb.sensitive.annotation.Sensitive;
import com.renpho.erp.data.sensitive.SensitiveDynamicCondition;
import com.renpho.erp.data.sensitive.SensitiveDynamicStrategy;
import com.renpho.erp.tms.domain.transportorder.TransportOrderStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Generated;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/19
 */
@lombok.Getter
@lombok.Setter
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-05-19T18:06:01.197175700+08:00[Asia/Shanghai]", comments = "Generator version: 7.12.0")
public class TransportRequestVO implements VO, Serializable {


    @Serial
    private static final long serialVersionUID = 7556837955219660535L;

    private Integer id;

    /**
     * TR单号
     */
    @Schema(name = "trNo", description = "TR单号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String trNo;

    /**
     * 交付单号（PD单号）
     */
    @Schema(name = "pdNo", description = "交付单号（PD单号）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pdNo;

    /**
     * PO单号
     */
    @Schema(name = "poNo", description = "PO单号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String poNo;

    /**
     * PO单ID
     */
    @Schema(name = "poId", description = "PO单ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer poId;

    /**
     * TO单号
     */
    @Schema(name = "toNo", description = "TO单号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String toNo;

    /**
     * QC单ID
     */
    @Schema(name = "qcId", description = "QC单ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer qcId;

    /**
     * QC单号
     */
    @Schema(name = "qcNo", description = "QC单号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String qcNo;

    /**
     * TO单ID
     */
    @Schema(name = "toId", description = "TO单ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer toId;

    /**
     * 产品ID
     */
    private Integer productId;

    /**
     * SKU
     */
    private String psku;

    /**
     * 敏感属性(选中的),多个用`,`号隔开，说明: 0 锂电池、1 干电池、2 纯电、3 液体、4 凝胶、5 粉末、6 营养颗粒、7 活性炭，egg: 0,1
     */
    private List<String> hazardousPros;

    /**
     * 出运状态。字典: TO_STATUS。可选值:  1-询价中 2-订舱中 3-已订舱 4-货交承运人 5-已离港 6-已到港 7-已派送 8-已签收 9-已完成 10-已作废
     */
    private Integer shipStatus;

    /**
     * TR单状态, 字典值: transport_request_status
     */
    @Schema(name = "status", description = "TR单状态, 字典值: transport_request_status", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Trans(type = TransType.DICTIONARY, key = "transport_request_status", ref = "statusName")
    private Integer status;

    /**
     * TR单状态
     */
    @Schema(name = "statusName", description = "TR单状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String statusName;

    /**
     * 头程方式 ID
     */
    @Schema(name = "firstLegModeId", description = "头程方式 ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String firstLegModeId;

    /**
     * 头程方式
     */
    @Schema(name = "firstLegModeName", description = "头程方式", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String firstLegModeName;

    /**
     * 平台 ID
     */
    @Schema(name = "salesChannelId", description = "平台 ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer salesChannelId;


    /**
     * 平台名称
     */
    @Schema(name = "salesChannelName", description = "平台名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String salesChannelName;


    /**
     * 货主 ID
     */
    @Schema(name = "ownerId", description = "货主 ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer ownerId;

    /**
     * 货主名称
     */
    @Schema(name = "ownerName", description = "货主名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String ownerName;


    /**
     * 起运港字典值, 字典: trade_terms_ship_to
     */
    @Schema(name = "shippingPort", description = "起运港字典值, 字典: trade_terms_ship_to", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
//    @Trans(type = TransType.DICTIONARY, key = "trade_terms_ship_to", ref = "shippingPortName")
    private String shippingPort;

    /**
     * 起运港
     */
    @Schema(name = "shippingPortName", description = "起运港", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String shippingPortName;

    /**
     * 贸易条款字典值, 字典: trade_terms
     */
    @Schema(name = "tradeTerms", description = "贸易条款字典值, 字典: trade_terms", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
//    @Trans(type = TransType.DICTIONARY, key = "trade_terms", ref = "tradeTermsName")
    private String tradeTerms;

    /**
     * 贸易条款
     */
    @Schema(name = "tradeTermsName", description = "贸易条款", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String tradeTermsName;

    /**
     * 服务商类型字典值, 字典: logistics_type
     */
    @Schema(name = "carrierType", description = "服务商类型字典值, 字典: logistics_type", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Trans(type = TransType.DICTIONARY, key = "logistics_type", ref = "carrierTypeName")
    private String carrierType;

    /**
     * 服务商类型
     */
    @Schema(name = "carrierTypeName", description = "服务商类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String carrierTypeName;

    /**
     * 发运与入库字典值, 字典: ship_warehouse_type
     */
    @Schema(name = "shipWarehouseMethod", description = "发运与入库字典值, 字典: ship_warehouse_type", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Trans(type = TransType.DICTIONARY, key = "ship_warehouse_type", ref = "shipWarehouseMethodName")
    private String shipWarehouseMethod;

    /**
     * 发运与入库
     */
    @Schema(name = "shipWarehouseMethodName", description = "发运与入库", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String shipWarehouseMethodName;

    /**
     * 目的国/地区  国家(地区)
     */
    private String destCountry;

    /**
     * 目的仓 ID
     */
    @Schema(name = "destWarehouseId", description = "目的仓 ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String destWarehouseId;

    /**
     * 目的仓名称
     */
    @Schema(name = "destWarehouseName", description = "目的仓名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String destWarehouseName;

    /**
     * 目的仓编码
     */
    @Schema(name = "destWarehouseCode", description = "目的仓编码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String destWarehouseCode;

    /**
     * 计划出货日期-开始
     */
    @Schema(name = "plannedShipStartDate", description = "计划出货日期-开始", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDate plannedShipStartDate;

    /**
     * 计划出货日期-结束
     */
    @Schema(name = "plannedShipEndDate", description = "计划出货日期-结束", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDate plannedShipEndDate;

    /**
     * 工厂交期
     */
    @Schema(name = "latestDeliveryDate", description = "工厂交期", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDate latestDeliveryDate;

    /**
     * 采购供应商 ID
     */
    @Schema(name = "purchaseSupplierId", description = "采购供应商 ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String purchaseSupplierId;

    /**
     * 采购供应商名称
     */
    @Schema(name = "purchaseSupplierName", description = "采购供应商名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Sensitive(permission = "SUPPLIER:NAME", condition = SensitiveDynamicCondition.class,
            strategy = SensitiveDynamicStrategy.class)
    private String purchaseSupplierName;

    /**
     * 采购供应商编码
     */
    @Schema(name = "purchaseSupplierCode", description = "采购供应商编码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String purchaseSupplierCode;

    /**
     * 采购供应商简称
     */
    @Schema(name = "purchaseSupplierShortName", description = "采购供应商简称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String purchaseSupplierShortName;

    /**
     * ReferenceId
     */
    @Schema(name = "referenceId", description = "ReferenceId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String referenceId;

    /**
     * ShipmentId
     */
    @Schema(name = "shipmentId", description = "ShipmentId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String shipmentId;

    /**
     * 总数量
     */
    private Integer totalQty;

    /**
     * 总箱数
     */
    private Integer totalBoxQty;

    /**
     * 总毛重 kg
     */
    private BigDecimal totalGrossWeight;

    /**
     * 总净重 kg
     */
    private BigDecimal totalNetWeight;

    /**
     * 总体积 m³
     */
    private BigDecimal totalVolume;


    /**
     * 创建人 ID
     */
    private Integer createBy;

    /**
     * 创建人名称
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人 ID
     */
    private Integer updateBy;

    /**
     * 更新人名称
     */
    private String updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 最后一次失败信息
     */
    private String lastErrMsg;

}

