package com.renpho.erp.tms.adapter.stream.transportorder;

import com.renpho.erp.bpm.api.dto.ProcessResultDto;
import com.renpho.erp.log.repository.annotation.LogRecord;
import com.renpho.erp.log.repository.support.LogRecordContextHolder;
import com.renpho.erp.tms.application.transportorder.TransportOrderForwarderApprovalService;
import com.renpho.erp.tms.application.transportorder.TransportOrderForwarderQueryApprovalService;
import com.renpho.erp.tms.application.transportorder.TransportOrderQueryService;
import com.renpho.erp.tms.domain.transportorder.TransportOrder;
import com.renpho.erp.tms.domain.transportorder.TransportOrderId;
import com.renpho.erp.tms.domain.transportorderforwarderapproval.TransportOrderFreightQuote;
import com.renpho.erp.tms.infrastructure.common.oplog.LogModule;
import com.renpho.erp.tms.infrastructure.remote.user.repository.OperatorLookup;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 货代完成审批 Handler
 *
 * <AUTHOR>
 * @since 2025/7/14
 */
@RequiredArgsConstructor
@Component
@Slf4j
public class TransportOrderFreightQuoteCmdHandler {

    private final TransportOrderForwarderApprovalService transportOrderForwarderApprovalService;
    private final OperatorLookup operatorLookup;
    private final TransportOrderForwarderQueryApprovalService transportOrderForwarderQueryApprovalService;
    private final TransportOrderQueryService transportOrderQueryService;

    /**
     * TO单货代审批完成回调
     *
     * <AUTHOR>
     * @since 2025/7/14
     */
    @LogRecord(module = LogModule.TRANSPORT_ORDER, type = LogModule.CommonDesc.CONFIRM_OPERATOR, desc = LogModule.CommonDesc.CONFIRM_DESC)
    public void finish(ProcessResultDto payload) throws InterruptedException {
        try {
            Thread.sleep(Duration.ofSeconds(3).toMillis());
            updateApprovalResult(payload);
        } catch (InterruptedException e) {
            updateApprovalResult(payload);
            throw new RuntimeException(e);
        }
    }

    @LogRecord(module = LogModule.TRANSPORT_ORDER, type = LogModule.CommonDesc.VOID_OPERATOR, desc = LogModule.CommonDesc.VOID_DESC)
    public void reject(ProcessResultDto payload) throws InterruptedException {
        try {
            Thread.sleep(Duration.ofSeconds(3).toMillis());
            updateApprovalResult(payload);
        } catch (InterruptedException e) {
            updateApprovalResult(payload);
            throw new RuntimeException(e);
        }
    }

    @LogRecord(module = LogModule.TRANSPORT_ORDER, type = LogModule.CommonDesc.CANCEL_OPERATOR, desc = LogModule.CommonDesc.CANCEL_DESC)
    public void cancel(ProcessResultDto payload) throws InterruptedException {
        try {
            Thread.sleep(Duration.ofSeconds(3).toMillis());
            updateApprovalResult(payload);
        } catch (InterruptedException e) {
            updateApprovalResult(payload);
            throw new RuntimeException(e);
        }
    }

    public void updateApprovalResult(ProcessResultDto payload) throws InterruptedException {
        Integer updateBy = Optional.ofNullable(payload.getFinalApprovalUserId()).orElse(operatorLookup.findSystemUser().getOperatorId().id());
        Set<TransportOrderId> toIds = transportOrderForwarderQueryApprovalService.findByProcessInstanceId(payload.getInstanceId()).stream().map(TransportOrderFreightQuote::getToId).collect(Collectors.toSet());
        Map<TransportOrderId, TransportOrder> olds = transportOrderQueryService.findByIds(toIds);
        transportOrderForwarderApprovalService.updateApprovalResultByInstanceId(payload.getInstanceId(), payload.getResult(), updateBy);
        Map<TransportOrderId, TransportOrder> news = transportOrderQueryService.findByIds(toIds);
        for (TransportOrderId id : toIds) {
            LogRecordContextHolder.putRecordData(String.valueOf(id.id()), olds.get(id), news.get(id));
        }
    }
}
