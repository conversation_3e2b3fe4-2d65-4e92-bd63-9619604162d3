package com.renpho.erp.tms.adapter.web.controller.transportorder.cmd;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.container.TransportOrderIdContainer;
import com.renpho.erp.tms.domain.transportorder.TransportOrder;
import jakarta.annotation.Generated;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/5/19
 */
@Getter
@Setter
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-05-19T18:06:01.197175700+08:00[Asia/Shanghai]", comments = "Generator version: 7.12.0")
public class IdQuery implements TransportOrderIdContainer<Integer>, Serializable {

    @Serial
    private static final long serialVersionUID = -7547392096868791764L;

    @NotNull
    private Integer id;

    @Override
    @JsonIgnore
    public void setTo(TransportOrder to) {

    }
}

