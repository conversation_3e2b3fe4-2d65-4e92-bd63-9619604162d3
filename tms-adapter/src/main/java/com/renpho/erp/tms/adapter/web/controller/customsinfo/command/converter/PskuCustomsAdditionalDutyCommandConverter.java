package com.renpho.erp.tms.adapter.web.controller.customsinfo.command.converter;

import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.AdditionalDutyCmd;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.AdditionalDutyTimeCmd;
import com.renpho.erp.tms.domain.customsinfo.PskuCustomsAdditionalDuty;
import org.mapstruct.*;

import java.util.Collection;
import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface PskuCustomsAdditionalDutyCommandConverter {

    @Mapping(target = "effectiveTimeLocal", source = "effectiveTime")
    @Mapping(target = "status", source = "effectiveStatus")
    PskuCustomsAdditionalDuty toDomain(AdditionalDutyCmd cmd);

    @IterableMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    List<PskuCustomsAdditionalDuty> toDomains(Collection<AdditionalDutyCmd> cmds);

    @Mapping(target = "effectiveTimeLocal", source = "effectiveTime")
    @Mapping(target = "status", source = "effectiveStatus")
    PskuCustomsAdditionalDuty toCountryTimeZoneDomain(AdditionalDutyTimeCmd cmd);

    List<PskuCustomsAdditionalDuty> toCountryTimeZoneDomains(Collection<AdditionalDutyTimeCmd> cmds);

}