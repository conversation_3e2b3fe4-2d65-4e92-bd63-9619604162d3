package com.renpho.erp.tms.adapter.web.controller.firstleg.transittime;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrBuilder;
import com.alibaba.excel.EasyExcel;
import com.fhs.core.trans.anno.TransMethodResult;
import com.fhs.trans.service.impl.TransService;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.IdQuery;
import com.renpho.erp.tms.adapter.web.controller.firstleg.transittime.command.*;
import com.renpho.erp.tms.adapter.web.controller.firstleg.transittime.command.converter.FirstLegModeTransitTimeCmdConverter;
import com.renpho.erp.tms.adapter.web.controller.firstleg.transittime.vo.*;
import com.renpho.erp.tms.adapter.web.controller.firstleg.transittime.vo.converter.FirstLegCountryRegionVOConverter;
import com.renpho.erp.tms.application.firstleg.FirstLegModeTransitTimeService;
import com.renpho.erp.tms.domain.exception.BusinessException;
import com.renpho.erp.tms.domain.firstleg.mode.FirstLegMode;
import com.renpho.erp.tms.domain.firstleg.mode.FirstLegModeLookup;
import com.renpho.erp.tms.domain.firstleg.transittime.FirstLegCountryRegion;
import com.renpho.erp.tms.infrastructure.util.excel.Excel18nTools;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import com.renpho.karma.exception.ApiException;
import com.renpho.karma.exception.error.PlatformErrorCode;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 头程方式时效.
 *
 * <AUTHOR>
 * @since 2025/4/21
 */
@Slf4j
@RestController
@RequestMapping("/firstleg/transittime")
@ShenyuSpringCloudClient("/firstleg/transittime/**")
@RequiredArgsConstructor
public class FirstLegModeTransitTimeController {

    private final Validator validator;
    private final TransService transService;
    private final FirstLegModeLookup firstLegModeLookup;
    private final FirstLegModeTransitTimeService firstLegModeTransitTimeService;

    private final FirstLegCountryRegionVOConverter firstLegCountryRegionVOConverter;
    private final FirstLegModeTransitTimeCmdConverter firstLegModeTransitTimeCmdConverter;


    /**
     * 新增
     *
     * @param cmd 参数
     * @return id
     */
    @PostMapping("/add")
    @PreAuthorize("hasPermission('tms:transitTime:new')")
    public R<Integer> add(@Valid @RequestBody FirstLegModeTransitTimeAddCmd cmd) {
        FirstLegCountryRegion domain = firstLegModeTransitTimeCmdConverter.toDomain(cmd);

        domain = firstLegModeTransitTimeService.add(domain);

        return R.success(domain.getId().id());
    }

    /**
     * 编辑
     *
     * @param cmd 参数
     * @return id
     */
    @PostMapping("/edit")
    @PreAuthorize("hasPermission('tms:transitTime:edit')")
    public R<Integer> edit(@Valid @RequestBody FirstLegModeTransitTimeEditCmd cmd) {
        FirstLegCountryRegion domain = firstLegModeTransitTimeCmdConverter.toDomain(cmd);
        domain = firstLegModeTransitTimeService.edit(domain);
        return R.success(domain.getId().id());
    }

    /**
     * 详情
     *
     * @param cmd 参数
     * @return id
     */
    @PostMapping("/detail")
    @PreAuthorize("hasPermission('tms:transitTime:list')")
    @TransMethodResult
    public R<FirstLegCountryRegionDetailVO> edit(@Valid @RequestBody IdQuery cmd) {
        FirstLegCountryRegion domain = firstLegModeTransitTimeCmdConverter.toDomain(cmd);
        FirstLegCountryRegion data = firstLegModeTransitTimeService.findById(domain);

        return R.success(firstLegCountryRegionVOConverter.toDetailVO(data));
    }


    /**
     * 分页查询列表
     *
     * @param cmd 参数
     * @return id
     */
    @TransMethodResult
    @PostMapping("/pageList")
    @PreAuthorize("hasPermission('tms:transitTime:list')")
    public R<Paging<FirstLegCountryRegionVO>> pageList(@Valid @RequestBody FirstLegModeTransitTimePageCmd cmd) {
        FirstLegCountryRegion domain = firstLegModeTransitTimeCmdConverter.toDomain(cmd);
        Paging<FirstLegCountryRegion> domains = firstLegModeTransitTimeService.pageList(domain, cmd);
        return R.success(firstLegCountryRegionVOConverter.toPageVOs(domains));
    }

    /**
     * 导出
     *
     * @param cmd      参数
     * @param response 响应
     */
    @PostMapping("/export")
    @PreAuthorize("hasPermission('tms:transitTime:export')")
    public void export(@RequestBody FirstLegModeTransitTimeExportCmd cmd, HttpServletResponse response) {
        FirstLegCountryRegion domain = firstLegModeTransitTimeCmdConverter.toDomain(cmd);
        List<FirstLegCountryRegion> domains = firstLegModeTransitTimeService.findAll(domain);
        List<FirstLegCountryRegionExcelExportVO> dataList = firstLegCountryRegionVOConverter.toExportVOs(domains);

        String filename = new StrBuilder().append("头程方式时效").append("_")
                .append(DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN))
                .append(".xlsx").toStringAndReset();

        try {
            transService.transBatch(dataList);
            ByteArrayOutputStream ous = Excel18nTools.handleExcelFile(dataList, FirstLegCountryRegionExcelExportVO.class);
            Excel18nTools.writeExcelFile(filename, ous, response);

        } catch (IOException e) {
            throw new BusinessException("SYSTEM_EXCEPTION");
        }

    }

    /**
     * 导入
     *
     * @param file 文件
     */
    @PostMapping("/upload")
    @PreAuthorize("hasPermission('tms:transitTime:import')")
    public void upload(@RequestPart(value = "file") MultipartFile file, HttpServletResponse response) {

        //查询头程方式
        Map<String, FirstLegMode> allModeMap = firstLegModeLookup.findAllModeMap();

        FirstLegCountryRegionExcelUploadListener listener = new FirstLegCountryRegionExcelUploadListener(validator, allModeMap);
        try {
            // 读取Excel文件
            EasyExcel.read(file.getInputStream(), FirstLegCountryRegionExcelUploadVO.class, listener).sheet().doRead();
        } catch (IOException e) {
            log.error("read.excel.error", e);
            throw new BusinessException("read.excel.error");
        }
        if (listener.hasError()) {
            // 生成错误结果文件
            try {
                ByteArrayOutputStream ous = Excel18nTools.handleExcelFile(listener.getExcelDataWithErrorList(), FirstLegCountryRegionExcelUploadErrorVO.class);
                Excel18nTools.writeExcelFile("导入失败结果.xlsx", ous, response);

            } catch (IOException e) {
                log.error("export.excel.error", e);
                throw new BusinessException("export.excel.error");
            }
        } else {
            //保存数据
            List<FirstLegCountryRegion> datas =
                    firstLegModeTransitTimeCmdConverter.groupAndMerge(listener.getExcelDataWithErrorList());

            firstLegModeTransitTimeService.batchSave(datas);

        }

    }

    /**
     * 下载头程方式时效导入模板
     */
    @PostMapping(path = "/export/template")
    public void exportTemplate(HttpServletResponse response) {
        try {
            String filename = new StrBuilder().append("头程方式时效导入模板").append("_")
                    .append(DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN))
                    .append(".xlsx").toStringAndReset();

            FirstLegCountryRegionExcelUploadVO templateData  = new FirstLegCountryRegionExcelUploadVO("空运",null,
                    "US","美西","25","30","启用","含清关派送到西海岸港口的时效"
                    );
            List<FirstLegCountryRegionExcelUploadVO> dataList = List.of(templateData);

            ByteArrayOutputStream ous = Excel18nTools.handleExcelFile(dataList, FirstLegCountryRegionExcelUploadVO.class);
            Excel18nTools.writeExcelFile(filename, ous, response);
        } catch (IOException e) {
            throw new ApiException(PlatformErrorCode.SYSTEM_EXCEPTION, e);
        }
    }
}
