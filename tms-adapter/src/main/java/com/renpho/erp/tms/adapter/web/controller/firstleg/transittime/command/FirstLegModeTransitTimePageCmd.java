package com.renpho.erp.tms.adapter.web.controller.firstleg.transittime.command;

import com.renpho.karma.dto.PageQuery;
import jakarta.validation.constraints.Positive;
import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 * @since 2025/4/23
 */
@Getter
@Setter
public class FirstLegModeTransitTimePageCmd extends PageQuery {
    /**
     * 头程方式ID
     */
    private Integer modeId;

    /**
     * 国家(地区)编码
     */
    private String countryCode;

    /**
     * 最小时效(天)
     */
    @Positive
    private Integer minTransitTime;

    /**
     * 最大时效(天)
     */
    @Positive
    private Integer maxTransitTime;

    /**
     * 状态 0：禁用，1：启用
     */
    private Integer status;

}
