package com.renpho.erp.tms.adapter.web.controller.basic.config.volumetric.factor;

import com.renpho.erp.tms.adapter.web.controller.basic.config.volumetric.factor.cmd.VolumetricFactorEditCmd;
import com.renpho.erp.tms.adapter.web.controller.basic.config.volumetric.factor.converter.VolumetricFactorConverter;
import com.renpho.erp.tms.adapter.web.controller.firstleg.mode.command.FirstLegModeEditCmd;

import com.renpho.erp.tms.application.basicconfig.VolumetricFactorService;
import com.renpho.erp.tms.domain.volumetricfactor.dto.FirstLegListResponse;
import com.renpho.erp.tms.domain.volumetricfactor.dto.FirstLegResponse;
import com.renpho.erp.tms.domain.volumetricfactor.dto.VolumetricFactorConfigResponse;
import com.renpho.erp.tms.domain.volumetricfactor.dto.VolumetricFactorEditRequest;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 寄抛系数-接口.
 *
 * <AUTHOR>
 * @since 2025/6/10
 */
@RestController
@RequestMapping("/volumetric/factor")
@ShenyuSpringCloudClient("/volumetric/factor/**")
@RequiredArgsConstructor
public class VolumetricFactorController {

    private final VolumetricFactorService volumetricFactorService;

    /**
     * 编辑
     *
     * @param cmd 参数
     * @return id
     */
    @PostMapping("/edit")
    @PreAuthorize("hasPermission('tms:volumetric:factor:edit')")
    public R<Boolean> edit(@RequestBody VolumetricFactorEditCmd cmd) {
        VolumetricFactorEditRequest request = VolumetricFactorConverter.INSTANCE.cmdToRequest(cmd);
        volumetricFactorService.edit(request);

        return R.success(true);
    }

    /**
     * 前端列表查询.
     * @return list
     */
    @GetMapping("/list")
    @PreAuthorize("hasPermission('tms:volumetric:factor:list')")
    public R<List<FirstLegListResponse>> list(){
        List<FirstLegListResponse> responses = new ArrayList<>();
        Map<Integer, List<FirstLegResponse>> configs =  volumetricFactorService.queryVolumetricFactorNotDeleted();
        for (Map.Entry<Integer, List<FirstLegResponse>> entry : configs.entrySet()) {
            FirstLegListResponse response = new FirstLegListResponse();
            response.setVolumetricFactor(entry.getKey());
            response.setFirstLegResponseList(entry.getValue());
            responses.add(response);
        }

        return R.success(responses);
    }

    /**
     * 查询所有未删除的寄抛系数配置.(无权限)
     *
     * @return 寄抛系数配置列表
     */
    @GetMapping("/findAllNotDeleted")
    public List<VolumetricFactorConfigResponse> findAllNotDeleted(){
        return volumetricFactorService.findAllNotDeleted();
    }

}
