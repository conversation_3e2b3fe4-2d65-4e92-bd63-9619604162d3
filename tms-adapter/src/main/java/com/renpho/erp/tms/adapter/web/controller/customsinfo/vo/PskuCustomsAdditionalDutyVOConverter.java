package com.renpho.erp.tms.adapter.web.controller.customsinfo.vo;

import com.renpho.erp.tms.domain.customsinfo.PskuCustomsAdditionalDuty;
import com.renpho.erp.tms.infrastructure.persistence.customsinfo.PskuCustomsInfoConverter;
import com.renpho.erp.tms.infrastructure.persistence.customsinfo.po.PskuCustomsAdditionalDutyConverter;
import com.renpho.erp.tms.infrastructure.remote.user.converter.OperatorConverter;
import org.mapstruct.*;

import java.util.Collection;
import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {PskuCustomsInfoConverter.class, PskuCustomsAdditionalDutyConverter.class, OperatorConverter.class})
public interface PskuCustomsAdditionalDutyVOConverter {

    @Mapping(target = "dutyRate", source = "dutyRate")
    @Mapping(target = "effectiveTime", source = "effectiveTimeLocal")
    @Mapping(target = "effectiveTimeUtc", expression = "java(domain.toUtc().orElse(null))")
    @Mapping(target = "createBy", source = "created.operatorId")
    @Mapping(target = "createByName", source = "created.operatorName")
    @Mapping(target = "createByCode", source = "created.operatorCode")
    @Mapping(target = "createTime", source = "created.operateTime")
    @Mapping(target = "updateBy", source = "updated.operatorId")
    @Mapping(target = "updateByName", source = "updated.operatorName")
    @Mapping(target = "updateByCode", source = "updated.operatorCode")
    @Mapping(target = "updateTime", source = "updated.operateTime")
    @BeanMapping(qualifiedByName = "updateEffectiveTimeLocale")
    PskuCustomsAdditionalDutyVO toVo(PskuCustomsAdditionalDuty domain);

    @AfterMapping
    @Named(value = "updateEffectiveTimeLocale")
    default void updateEffectiveTimeLocale(@MappingTarget PskuCustomsAdditionalDutyVO vo) {
        if (vo == null) {
            return;
        }
        vo.updateEffectiveTimeLocal();
    }

    @IterableMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    List<PskuCustomsAdditionalDutyVO> toVos(Collection<PskuCustomsAdditionalDuty> domains);

}