package com.renpho.erp.tms.adapter.stream.transportrequest.cmd.converter;

import cn.hutool.core.collection.CollUtil;
import com.renpho.erp.pms.model.common.model.FileDTO;
import com.renpho.erp.pms.model.common.model.enums.PmsOrderFileType;
import com.renpho.erp.pms.model.purchaseorder.enums.PurchaseRequestType;
import com.renpho.erp.pms.model.shipmentplan.model.ShipmentPlanOrderDTO;
import com.renpho.erp.tms.adapter.stream.transportrequest.cmd.AddTransportRequestCmd;
import com.renpho.erp.tms.domain.product.BoxSpec;
import com.renpho.erp.tms.domain.product.Product;
import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.infrastructure.persistence.firstleg.mode.po.converter.FirstLegModeConverter;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.po.converter.TransportRequestConverter;
import com.renpho.erp.tms.infrastructure.remote.owner.converter.OwnerConverter;
import com.renpho.erp.tms.infrastructure.remote.product.converter.ProductConverter;
import com.renpho.erp.tms.infrastructure.remote.product.converter.ProductPriceConverter;
import com.renpho.erp.tms.infrastructure.remote.purchasesupplier.PurchaseSupplierConverter;
import com.renpho.erp.tms.infrastructure.remote.saleschannel.converter.SalesChannelConverter;
import com.renpho.erp.tms.infrastructure.remote.store.converter.StoreConverter;
import com.renpho.erp.tms.infrastructure.remote.user.converter.OperatorConverter;
import com.renpho.erp.tms.infrastructure.remote.warehouse.converter.WarehouseConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {TransportRequestConverter.class, ProductConverter.class, ProductPriceConverter.class, WarehouseConverter.class, SalesChannelConverter.class, StoreConverter.class, OwnerConverter.class, PurchaseSupplierConverter.class, FirstLegModeConverter.class, OperatorConverter.class},
        imports = {QcResult.class})
public interface TransportRequestCmdStreamConverter {

    @Mapping(target = "pdId", source = "id")
    @Mapping(target = "poId", source = "purchaseOrder.id")
    @Mapping(target = "firstLegMethod", source = "item.firstLegMethod")
    @Mapping(target = "firstLegType", source = "item.firstLegType")
    @Mapping(target = "businessType", source = "purchaseOrder.request.type")
    @Mapping(target = "salesChannelId", source = "purchaseOrder.request.salesChannelId")
    @Mapping(target = "shippingPort", source = "purchaseOrder.supplierInfo.supplierDeliveryLocation")
    @Mapping(target = "tradeTerms", source = "purchaseOrder.supplierInfo.supplierTradeTerms")
    @Mapping(target = "carrierType", source = "item.logisticsType")
    @Mapping(target = "shipWarehouseMethod", source = "item.shipWarehouseMethod")
    @Mapping(target = "destCountryCode", source = "siteCode")
    @Mapping(target = "destWarehouseId", source = "item.destWarehouseId")
    @Mapping(target = "destWarehouseCode", source = "item.destWarehouseCode")
    @Mapping(target = "plannedShipStartDate", source = "item.expectedPdDate")
    @Mapping(target = "plannedShipEndDate", source = "item.expectedPdDate")
    @Mapping(target = "latestDeliveryDate", source = "item.lastDeliveryDate")
    @Mapping(target = "purchaserOwnerId", source = "purchaseOrder.companyId")
    @Mapping(target = "purchaseSupplierId", source = "purchaseOrder.supplierId")
    @Mapping(target = "referenceId", source = "referenceID")
    @Mapping(target = "shipmentId", source = "shipmentID")
    @Mapping(target = "cartonLabelFileIds", source = "fileList")
    @Mapping(target = "salesStaffId", source = "purchaseOrder.request.salesStaffId")
    @Mapping(target = "planningStaffId", source = "purchaseOrder.request.planningsStaffId")
    @Mapping(target = "purchaseStaffId", source = "purchaseOrder.buyerId")
    @Mapping(target = "storeId", source = "storeId")
    @Mapping(target = "productId", source = "purchaseOrder.productId")
    @Mapping(target = "product", source = "dto")
    @Mapping(target = "fnsku", source = "item.fnSku")
    @Mapping(target = "weightUnit", constant = "kg")
    @Mapping(target = "dimensionUnit", source = "purchaseOrder.productInfo.productUnit")
    @Mapping(target = "model", source = "purchaseOrder.productInfo.modelName")
    @Mapping(target = "brand", source = "purchaseOrder.productInfo.brandName")
    @Mapping(target = "quantity", source = "pdQty")
    @Mapping(target = "boxQty", source = "purchaseOrder.request.boxQty")
    @Mapping(target = "productLength", source = "purchaseOrder.productInfo.productLengthMetric")
    @Mapping(target = "productWidth", source = "purchaseOrder.productInfo.productWidthMetric")
    @Mapping(target = "productHeight", source = "purchaseOrder.productInfo.productHeightMetric")
    @Mapping(target = "packagingLength", source = "purchaseOrder.productInfo.packagingLengthMetric")
    @Mapping(target = "packagingWidth", source = "purchaseOrder.productInfo.packagingWidthMetric")
    @Mapping(target = "packagingHeight", source = "purchaseOrder.productInfo.packagingHeightMetric")
    @Mapping(target = "weight", source = "purchaseOrder.productInfo.productWeightMetric")
    @Mapping(target = "grossWeight", source = "purchaseOrder.productInfo.grossWeightMetric")
    @Mapping(target = "quantityPerBox", source = "purchaseOrder.productInfo.numberOfUnitsPerBox")
    @Mapping(target = "boxLengthMetric", source = "purchaseOrder.productInfo.boxLengthMetric")
    @Mapping(target = "boxWidthMetric", source = "purchaseOrder.productInfo.boxWidthMetric")
    @Mapping(target = "boxHeightMetric", source = "purchaseOrder.productInfo.boxHeightMetric")
    @Mapping(target = "boxGrossWeight", source = "purchaseOrder.productInfo.grossWeightPerBoxMetric")
    @Mapping(target = "isFullBox", source = "purchaseOrder.request.boxType")
    @Mapping(target = "purchaseCost", source = "purchaseOrder.priceInfo.inclusiveTaxPrice")
    @Mapping(target = "purchaseCostCurrencyCode", source = "purchaseOrder.priceInfo.currency")
    @Mapping(target = "hazardousPros", source = "purchaseOrder.productInfo.hazardousProList", qualifiedByName = "listStringToString")
    AddTransportRequestCmd toAddCmd(ShipmentPlanOrderDTO dto);

    @Mapping(target = "id", source = "purchaseOrder.productId")
    @Mapping(target = "fnSku", source = "item.fnSku")
    @Mapping(target = "modelName", source = "purchaseOrder.productInfo.modelName")
    @Mapping(target = "brandName", source = "purchaseOrder.productInfo.brandName")
    @Mapping(target = "activeBoxSpec", source = "dto")
    @Mapping(target = "lengthMetric", source = "purchaseOrder.productInfo.productLengthMetric")
    @Mapping(target = "widthMetric", source = "purchaseOrder.productInfo.productWidthMetric")
    @Mapping(target = "heightMetric", source = "purchaseOrder.productInfo.productHeightMetric")
    @Mapping(target = "packagingLengthMetric", source = "purchaseOrder.productInfo.packagingLengthMetric")
    @Mapping(target = "packagingWidthMetric", source = "purchaseOrder.productInfo.packagingWidthMetric")
    @Mapping(target = "packagingHeightMetric", source = "purchaseOrder.productInfo.packagingHeightMetric")
    @Mapping(target = "weightMetric", source = "purchaseOrder.productInfo.productWeightMetric")
    @Mapping(target = "grossWeightMetric", source = "purchaseOrder.productInfo.grossWeightMetric")
    @Mapping(target = "salesChannelId", source = "purchaseOrder.request.salesChannelId")
    @Mapping(target = "quantityPerBox", source = "purchaseOrder.productInfo.numberOfUnitsPerBox")
    Product toProduct(ShipmentPlanOrderDTO dto);

    @Mapping(target = "numberOfUnitsPerBox", source = "purchaseOrder.productInfo.numberOfUnitsPerBox")
    @Mapping(target = "boxLengthMetric", source = "purchaseOrder.productInfo.boxLengthMetric")
    @Mapping(target = "boxWidthMetric", source = "purchaseOrder.productInfo.boxWidthMetric")
    @Mapping(target = "boxHeightMetric", source = "purchaseOrder.productInfo.boxHeightMetric")
    @Mapping(target = "grossWeightPerBoxMetric", source = "purchaseOrder.productInfo.grossWeightPerBoxMetric")
    BoxSpec toProductBox(ShipmentPlanOrderDTO dto);

    default Set<String> toCartonLabelFileIds(Collection<FileDTO> files) {
        return CollectionUtils.emptyIfNull(files)
                .stream()
                .filter(Objects::nonNull)
                .filter(e -> PmsOrderFileType.PD_ORDER_MARK.equals(e.getFileType()))
                .map(FileDTO::getFileId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
    }

    default TransportRequestType toTransportRequestType(PurchaseRequestType type) {
        return TransportRequestType.fromPdType(type.name());
    }

    @Mapping(target = "latestDeliveryDate", source = "item.lastDeliveryDate")
    UpdateTransportRequestCmd toUpdateCmd(ShipmentPlanOrderDTO dto);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "salesStaff", source = "salesStaffId")
    @Mapping(target = "planningStaff", source = "planningStaffId")
    @Mapping(target = "purchaseStaff", source = "purchaseStaffId")
    @Mapping(target = "purchasingCompany", source = "purchaserOwner")
    @Mapping(target = "item", source = "cmd")
    @Mapping(target = "status", source = "cmd")
    @Mapping(target = "qcResult", expression = "java(QcResult.NOT_EXIST)")
    @BeanMapping(qualifiedByName = "createTrNoAndSetTotal")
    TransportRequest toDomain(AddTransportRequestCmd cmd);

    @Named("listStringToString")
    default String listStringToString(List<String> strList) {
        if (CollUtil.isEmpty(strList)) {
            return "";
        }
        return String.join(",", strList);
    }

    default TransportRequestStatus initTrStatus(AddTransportRequestCmd cmd) {
        TransportRequestType type = cmd.getBusinessType();
        if (type == TransportRequestType.VC || (StringUtils.isNotBlank(cmd.getShipmentId()) && CollectionUtils.isNotEmpty(cmd.getCartonLabelFileIds()) && cmd.getCartonLabelFileIds().stream().allMatch(StringUtils::isNotBlank))) {
            return TransportRequestStatus.PENDING_CONSOLIDATION;
        }
        return TransportRequestStatus.PENDING;
    }

    @Named("createTrNoAndSetTotal")
    @AfterMapping
    default void createTrNoAndSetTotal(@MappingTarget TransportRequest domain) {
        if (domain == null) {
            return;
        }
        domain.createTrNo();
        domain.sumTotalVolume();
        domain.sumPackageTotalVolume();
        domain.sumBoxTotalVolume();
        domain.sumTotalGrossWeight();
        domain.sumTotalNetWeight();
        TransportRequestItem item = domain.getItem();
        if (item == null) {
            return;
        }
        item.setTrNo(domain.getTrNo());
    }

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "trId", ignore = true)
    @Mapping(target = "purchaseCostCurrency", source = "purchaseCostCurrencyCode")
    TransportRequestItem toItem(AddTransportRequestCmd cmd);

    TransportRequest updateDomain(UpdateTransportRequestCmd cmd, @MappingTarget TransportRequest command);

    @Mapping(target = "qcId", source = "qcId")
    @Mapping(target = "qcNo", source = "qcNo")
    @Mapping(target = "qcResult", source = "result.value")
    void updateQcResult(@MappingTarget TransportRequest domain, Integer qcId, String qcNo, QcResult result);
}
