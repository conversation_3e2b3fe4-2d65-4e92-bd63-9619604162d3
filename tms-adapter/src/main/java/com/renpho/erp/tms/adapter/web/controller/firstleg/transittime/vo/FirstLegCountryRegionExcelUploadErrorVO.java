package com.renpho.erp.tms.adapter.web.controller.firstleg.transittime.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2025/4/25
 */
@Getter
@Setter
@NoArgsConstructor
public class FirstLegCountryRegionExcelUploadErrorVO extends FirstLegCountryRegionExcelUploadVO{

    @ExcelProperty(value = "错误信息", index = 7)
    private String error;

    public FirstLegCountryRegionExcelUploadErrorVO(FirstLegCountryRegionExcelUploadVO uploadVO) {
        setFirstLegMode(uploadVO.getFirstLegMode());
        setCountryCode(uploadVO.getCountryCode());
        setArea(uploadVO.getArea());
        setMinTransitTime(uploadVO.getMinTransitTime());
        setMaxTransitTime(uploadVO.getMaxTransitTime());
        setStatusName(uploadVO.getStatusName());
        setRemark(uploadVO.getRemark());
    }

}
