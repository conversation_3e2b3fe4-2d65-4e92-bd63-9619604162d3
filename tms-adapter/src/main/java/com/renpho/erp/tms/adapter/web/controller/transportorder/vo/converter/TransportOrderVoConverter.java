package com.renpho.erp.tms.adapter.web.controller.transportorder.vo.converter;

import cn.hutool.core.collection.CollUtil;
import com.renpho.erp.tms.adapter.web.controller.orderfile.vo.converter.OrderFileVOConverter;
import com.renpho.erp.tms.adapter.web.controller.transportorder.vo.*;
import com.renpho.erp.tms.adapter.web.controller.transportrequest.vo.TransportRequestDetailTOVO;
import com.renpho.erp.tms.adapter.web.controller.transportrequest.vo.TransportRequestTrackingVO;
import com.renpho.erp.tms.adapter.web.controller.transportrequest.vo.TransportRequestViewVO;
import com.renpho.erp.tms.adapter.web.controller.transportrequest.vo.converter.TransportRequestVOConverter;
import com.renpho.erp.tms.domain.exchangerate.FluctuateExchangeRate;
import com.renpho.erp.tms.domain.owner.Owner;
import com.renpho.erp.tms.domain.product.BoxSpec;
import com.renpho.erp.tms.domain.supplier.PurchaseSupplier;
import com.renpho.erp.tms.domain.transportorder.TransportOrder;
import com.renpho.erp.tms.domain.transportorder.TransportOrderStatusHistory;
import com.renpho.erp.tms.domain.transportorderforwarderapproval.TransportOrderFreightQuote;
import com.renpho.erp.tms.domain.transportrequest.TransportRequest;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestItem;
import com.renpho.erp.tms.infrastructure.common.converter.MultiLanguageConverter;
import com.renpho.erp.tms.infrastructure.common.converter.TransportOrderApprovalStatusConverter;
import com.renpho.erp.tms.infrastructure.common.converter.TransportOrderStatusConverter;
import com.renpho.erp.tms.infrastructure.remote.product.converter.ProductConverter;
import com.renpho.erp.tms.infrastructure.remote.user.converter.OperatorConverter;
import com.renpho.karma.dto.Paging;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2025/6/10
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE,
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {TransportOrderStatusConverter.class,
                MultiLanguageConverter.class,
                ProductConverter.class,
                TransportOrderApprovalStatusConverter.class,
                OperatorConverter.class,
                TransportOrderCommentVOConverter.class,
                OrderFileVOConverter.class,
                TransportRequestVOConverter.class})
public interface TransportOrderVoConverter {
    Logger log = LoggerFactory.getLogger(TransportOrderVoConverter.class);


    @Mappings({
            @Mapping(target = "id", source = "id.id"),
            @Mapping(target = "qcResult", source = "qcResult.value"),
            @Mapping(target = "firstLegModeId", source = "firstLegMode.id.id"),
            @Mapping(target = "firstLegModeName", source = "firstLegMode.name"),
            @Mapping(target = "destCountry", source = "destCountry.fullName"),
            @Mapping(target = "createBy", source = "created.operatorId.id"),
            @Mapping(target = "forwarderCompany", source = "logisticsSupplier.names"),
            @Mapping(target = "shippingPortName", source = "shippingPort"),
            @Mapping(target = "shipWarehouseMethods", source = "shipWarehouseMethods", qualifiedByName =
                    "mapShipWarehouseMethodVOs"),
            @Mapping(target = "trackingItems", source = "trMapByTrackingNo", qualifiedByName = "toTrackingItems"),
            @Mapping(target = "qcResults", source = "transportRequestList"),
            @Mapping(target = "approvalVOS", source = "freightQuoteList"),
            @Mapping(target = "creator", source = "created", qualifiedByName = "concatOperatorInfo"),
            @Mapping(target = "createTime", source = "created.operateTime"),
            @Mapping(target = "updateBy", source = "updated.operatorId.id"),
            @Mapping(target = "updater", source = "updated", qualifiedByName = "concatOperatorInfo"),
            @Mapping(target = "updateTime", source = "updated.operateTime"),
    })
    TransportOrderPageVO toPageVO(TransportOrder domain);

    List<TransportOrderPageVO> toPageVOs(Collection<TransportOrder> domains);

    Paging<TransportOrderPageVO> toPageListVOs(Paging<TransportOrder> domains);


    @Mapping(target = "id", ignore = true)
    @Mapping(target = "instanceId", source = "instanceId.id")
    TransportOrderApprovalVO toApprovalVO(TransportOrderFreightQuote quote);

    List<TransportOrderApprovalVO> toApprovalVOS(List<TransportOrderFreightQuote> quotes);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "qcResult", source = "qcResult.value")
    TransportOrderPageVO.TrQcResultVO toTrQcResultVO(TransportRequest tr);

    List<TransportOrderPageVO.TrQcResultVO> toTrQcResultVOs(List<TransportRequest> trs);

    @Mappings({
            @Mapping(source = "salesChannel.id.id", target = "salesChannelId"),
            @Mapping(source = "salesChannel.channelName", target = "salesChannelName"),
            @Mapping(source = "owner.id.id", target = "ownerId"),
            @Mapping(source = "owner.names", target = "ownerName"),
            @Mapping(source = "shipWarehouseMethod", target = "shipWarehouseMethod"),
            @Mapping(source = "quantity", target = "totalQty"),
            @Mapping(source = "boxQty", target = "totalBoxQty"),
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "trNos", ignore = true),
            @Mapping(target = "shipmentIds", ignore = true),
            @Mapping(target = "purchaseSupplierShortNames", ignore = true),
    })
    TransportRequestTrackingVO toVO(TransportRequest transportRequest);

    @Named("toTrackingItems")
    default List<TransportRequestTrackingVO> toTrackingItems(Map<String, List<TransportRequest>> map) {
        if (map == null || map.isEmpty()) {
            return Collections.emptyList();
        }


        List<TransportRequestTrackingVO> result = new ArrayList<>();

        for (Map.Entry<String, List<TransportRequest>> entry : map.entrySet()) {
            List<TransportRequest> groupList = entry.getValue();
            TransportRequest representative = groupList.get(0);

            TransportRequestTrackingVO vo = this.toVO(representative);

            vo.setTrackingNo(entry.getKey());

            vo.setTrNos(groupList.stream().map(TransportRequest::getTrNo).filter(Objects::nonNull).distinct().toList());
            vo.setOwners(groupList.stream().map(TransportRequest::getOwner).map(Owner::getShortName).filter(Objects::nonNull).distinct().toList());
            vo.setShipmentIds(groupList.stream().map(TransportRequest::getShipmentId).filter(Objects::nonNull).distinct().toList());
            vo.setPurchaseSupplierShortNames(groupList.stream().map(TransportRequest::getPurchaseSupplier).map(PurchaseSupplier::getShortName).filter(Objects::nonNull).distinct().toList());
            vo.setImportInspection(representative.getImportInspection());
            vo.setExportInspection(representative.getExportInspection());
            vo.setCustomsClearanceRemark(representative.getCustomsClearanceRemark());

            result.add(vo);
        }

        return result;
    }

    @Mapping(target = "salesChannelId", source = "salesChannel.id.id")
    @Mapping(target = "salesChannelName", source = "salesChannel.channelName")
    @Mapping(target = "productId", source = "product.id.id")
    @Mapping(target = "hazardousPros", source = "item.hazardousPros", qualifiedByName = "mapToHazardousPros")
    @Mapping(target = "firstLegModeId", source = "firstLegMode.id.id")
    @Mapping(target = "firstLegModeName", source = "firstLegMode.name")
    @Mapping(target = "categoryName", source = "item.product.cateName")
    @Mapping(target = "ownerId", source = "owner.id.id")
    @Mapping(target = "ownerName", source = "owner.names")
    @Mapping(target = "shippingPortName", source = "shippingPort")
    @Mapping(target = "destCountry", source = "destCountry.fullName")
    @Mapping(target = "destCountryCode", source = "destCountry.code")
    @Mapping(target = "destWarehouseId", source = "destWarehouse.id.id")
    @Mapping(target = "destWarehouseName", source = "destWarehouse.languages")
    @Mapping(target = "destWarehouseCode", source = "destWarehouseCode")
    @Mapping(target = "destination", source = "destWarehouse.fullAddress")
    @Mapping(target = "shipWarehouseMethod", source = "shipWarehouseMethod")
    @Mapping(target = "purchaseSupplierId", source = "purchaseSupplier.id.id")
    @Mapping(target = "purchaseSupplierName", source = "purchaseSupplier.names")
    @Mapping(target = "purchaseSupplierCode", source = "purchaseSupplier.supplierCode")
    @Mapping(target = "purchaseSupplierShortName", source = "purchaseSupplier.shortName")
    @Mapping(target = "estimatedTaxCurrencyCode", source = "estimatedTaxCurrency.id.code")
    @Mapping(target = "estimatedFreightCurrencyCode", source = "estimatedFreightCurrency.id.code")
    @Mapping(target = "totalQty", source = "quantity")
    @Mapping(target = "totalBoxQty", source = "boxQty")
    @Mapping(target = "id", source = "id.id")
    @Mapping(target = "fnSku", source = "item.product.fnSku")
    @Mapping(target = "nameEn", source = "item.product.names", qualifiedByName = "findEnName")
    TransportRequestDetailTOVO toDetailVO(TransportRequest transportRequest);

    @Mapping(target = "id", source = "id.id")
    @Mapping(target = "firstLegModeLabel", source = "firstLegMode.label")
    TransportRequestViewVO toViewDetailVO(TransportRequest transportRequest);

    List<TransportRequestViewVO> toViewDetailVOs(List<TransportRequest> transportRequests);

    @Named("mapShipWarehouseMethodVOs")
    default List<ShipWarehouseMethodVO> mapShipWarehouseMethodVOs(List<String> shipWarehouseMethods) {
        if (shipWarehouseMethods == null) {
            return null;
        }
        return shipWarehouseMethods.stream().map(s -> {
            ShipWarehouseMethodVO shipWarehouseMethodVO = new ShipWarehouseMethodVO();
            shipWarehouseMethodVO.setShipWarehouseMethod(s);
            return shipWarehouseMethodVO;
        }).toList();
    }

    @Mapping(source = "id.id", target = "id")
    @Mapping(source = "taxCurrency", target = "estimatedTaxCurrencyCode")
    @Mapping(source = "forwarderCompanyCode", target = "forwarderCompanyCode")
    @Mapping(source = "files", target = "files", qualifiedByName = "groupByFileType")
    @Mapping(source = "comments", target = "comments")
    @Mapping(source = "transportRequestList", target = "trItems")
    @Mapping(source = "statusHistoryList", target = "statusChangeTimeRecords")
    @Mapping(target = "forwarderCompany", source = "logisticsSupplier.names")
    TransportOrderDeatilVO toDetailVO(TransportOrder domain);

    @Mapping(source = "id.id", target = "id")
    @Mapping(source = "files", target = "files", qualifiedByName = "groupByFileType")
    @Mapping(target = "destCountry", source = "destCountry.fullName")
    @Mapping(target = "firstLegModeName", source = "firstLegMode.name")
    TransportOrderDeatilSimpleVO toTransportOrderSimpleDetailVO(TransportOrder domain);

    @Mapping(target = "id", source = "id.id")
    @Mapping(target = "createTime", source = "created.operateTime")
    TransportOrderStatusHistoryVO mapToStatus(TransportOrderStatusHistory statusHistory);

    List<TransportOrderStatusHistoryVO> mapToStatusList(List<TransportOrderStatusHistory> statusHistoryList);


    // 映射 TransportOrder + TransportRequest 为 ExportVO
    @Mappings({
            @Mapping(target = "id", source = "to.id.id"),
            @Mapping(target = "toNo", source = "to.toNo"),
            @Mapping(target = "poNo", source = "tr.poNo"),
            @Mapping(target = "trNo", source = "tr.trNo"),
            @Mapping(target = "psku", source = "tr.psku"),
            @Mapping(target = "pskuName", source = "tr.item.product.names"),
            @Mapping(target = "model", source = "tr.item.product.modelName"),
            @Mapping(target = "brand", source = "tr.item.product.brandName"),
            @Mapping(target = "totalQty", source = "to.totalQty"),
            @Mapping(target = "totalBoxQty", source = "to.totalBoxQty"),
            @Mapping(target = "totalGrossWeight", source = "to.totalGrossWeight"),
            @Mapping(target = "totalNetWeight", source = "to.totalNetWeight"),
            @Mapping(target = "totalVolume", source = "to.totalVolume"),
            @Mapping(target = "hazardousPros", source = "tr.item.hazardousPros", qualifiedByName = "mapToHazardousProString"),
            @Mapping(target = "destCountry", source = "to.destCountry.fullName"),
            @Mapping(target = "destWarehouseCode", source = "tr.destWarehouseCode"),
            @Mapping(target = "destination", source = "tr.destWarehouse.fullAddress"),
            @Mapping(target = "firstLegModeName", source = "to.firstLegMode.name"),
            @Mapping(target = "cartonDimension", source = "tr.item", qualifiedByName = "mapCartonDimension"),
            @Mapping(target = "purchaseSupplierId", source = "tr.purchaseSupplier.id.id"),
            @Mapping(target = "purchaseSupplierName", source = "tr.purchaseSupplier.names"),
            @Mapping(target = "shipmentId", source = "tr.shipmentId"),
            @Mapping(target = "referenceId", source = "tr.referenceId"),
            @Mapping(target = "trackingNo", source = "tr.trackingNo"),
            @Mapping(target = "hsCode", source = "tr", qualifiedByName = "buildHsCode"),
            @Mapping(target = "fnSku", source = "tr.item.product.fnSku"),
            @Mapping(target = "customsUnitPrice", source = "tr", qualifiedByName = "calSkuDeclaredValue"),
            @Mapping(target = "currency", constant = "USD"),
            @Mapping(target = "image", source = "tr.product.picture", qualifiedByName = "mapToImageByte"),
            @Mapping(target = "material", source = "tr.product.productLogistic.englishMaterial"),
            @Mapping(target = "salesLink", source = "tr.skuMappings", qualifiedByName = "listToString"),
    })
    TransportOrderExportVO toExportVO(TransportOrder to, TransportRequest tr);

    /**
     * 计算SKU申报货值
     *
     * @param tr tr
     * @return SKU申报货值
     */
    @Named("calSkuDeclaredValue")
    default BigDecimal calSkuDeclaredValue(TransportRequest tr) {
        // SKU申报货值 USD = PSKU 采购成本 × 内部交易加价比例 x 汇率
        // 汇率
        BigDecimal rate = Optional.ofNullable(tr.getDeclaredValueRate()).map(FluctuateExchangeRate::getExchangeRate).orElse(BigDecimal.ONE);
        return tr.getItem().getPurchaseCost().multiply(tr.getPriceIncreaseRate()).multiply(rate).setScale(3, RoundingMode.HALF_UP);
    }

    @Named("buildHsCode")
    default String buildHsCode(TransportRequest tr) {
        if (tr == null || tr.getItem() == null) {
            return "";
        }

        String addTax = tr.getItem().getHsCodeAddTax();
        String reducedTax = tr.getItem().getHsCodeReducedTax();

        if (StringUtils.isNotBlank(addTax) && StringUtils.isNotBlank(reducedTax)) {
            return addTax + ";" + reducedTax;
        } else if (StringUtils.isNotBlank(addTax)) {
            return addTax;
        } else if (StringUtils.isNotBlank(reducedTax)) {
            return reducedTax;
        } else {
            return "";
        }
    }

    @Named("listToString")
    default String listToString(Collection<String> strList) {
        if (CollUtil.isEmpty(strList)) {
            return "";
        }
        return String.join(",", strList);
    }

    @Named("mapCartonDimension")
    default String mapCartonDimension(TransportRequestItem item) {
        if (item == null || item.getProduct() == null || item.getProduct().getActiveBoxSpec() == null) {
            return ""; // 或者根据业务需求返回默认值，例如 "0*0*0"
        }
        BoxSpec boxSpec = item.getProduct().getActiveBoxSpec();
        return boxSpec.getBoxLengthMetric().toString() + "*" + boxSpec.getBoxWidthMetric().toString() + "*" + boxSpec.getBoxHeightMetric().toString();
    }

    @Named("mapToImageByte")
    default byte[] mapToImageByte(String imgUrl) {
        if (StringUtils.isBlank(imgUrl)) {
            return null;
        }
        try (InputStream in = new URL(imgUrl).openStream();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {

            // 使用 Thumbnailator 压缩图片到 100x100 像素
            Thumbnails.of(in)
                    .size(100, 100)
                    .outputFormat("png")
                    .toOutputStream(out);

            return out.toByteArray();
        } catch (Exception e) {
            log.error("图片压缩失败", e);
            return null;
        }
    }
}
