package com.renpho.erp.tms.adapter.web.controller.customsinfo.command.validator;

import com.renpho.erp.tms.adapter.web.controller.command.container.CountryCodeContainer;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.AdditionalDutyCmd;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.container.CountryCodeAndItemsContainer;
import com.renpho.erp.tms.domain.regiontimezone.CountryTimeZoneLookup;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.annotation.Annotation;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @since 2025/4/25
 */
public abstract class PskuCustomInfoCountryTimeZoneValidator<A extends Annotation, T> implements ConstraintValidator<A, T> {

    protected Predicate<T> predicate;

    @Resource
    protected CountryTimeZoneLookup countryTimeZoneLookup;

    @Override
    public boolean isValid(T value, ConstraintValidatorContext context) {
        return countryTimeZoneLookup == null || predicate.test(value);
    }

    public static class IsCountryTimeZoneExist extends PskuCustomInfoCountryTimeZoneValidator<CountryTimeZoneExist, CountryCodeAndItemsContainer<List<AdditionalDutyCmd>, AdditionalDutyCmd>> {
        @Override
        public void initialize(CountryTimeZoneExist constraintAnnotation) {
            predicate = cmd -> {
                // 若附加关税为空则不校验时区
                Optional<CountryCodeAndItemsContainer<List<AdditionalDutyCmd>, AdditionalDutyCmd>> optional = Optional.ofNullable(cmd);
                if (optional.map(CountryCodeAndItemsContainer::getItems)
                        .filter(CollectionUtils::isEmpty)
                        .isPresent()) {
                    return true;
                }
                return optional.map(CountryCodeContainer::getCountryCode)
                        .filter(StringUtils::isNotBlank)
                        .flatMap(countryTimeZoneLookup::findByCode)
                        .stream()
                        .peek(c -> optional.ifPresent(cm -> cm.setCountryTimeZone(c)))
                        .findAny()
                        .isPresent();
            };
        }
    }
}
