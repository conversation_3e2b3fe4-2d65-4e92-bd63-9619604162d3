package com.renpho.erp.tms.adapter.web.controller.customsinfo.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.renpho.erp.tms.adapter.web.controller.vo.serializer.BigDecimalScale2RoundingSerializer;
import com.renpho.karma.timezone.TimeZoneContextHolder;
import com.renpho.karma.timezone.annotation.TimeZoneIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Generated;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Optional;

/**
 * PSKU清关信息附加关税
 */
@Getter
@Setter
@Schema(name = "PskuCustomsAdditionalDutyVO", description = "PSKU清关信息附加关税")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-04-22T12:09:54.179410400+08:00[Asia/Shanghai]", comments = "Generator version: 7.12.0")
public class PskuCustomsAdditionalDutyVO implements VO, Serializable {

    @Serial
    private static final long serialVersionUID = -1008452467114915922L;

    private Integer id;

    /**
     * PSKU 清关信息 ID
     */
    @Schema(name = "pskuCustomsInfoId", description = "PSKU 清关信息 ID")
    private Integer pskuCustomsInfoId;

    /**
     * 税种
     */
    @Schema(name = "name", description = "税种")
    private String name;

    /**
     * 税率
     */
    @Schema(name = "dutyRate", description = "税率")
    @JsonSerialize(using = BigDecimalScale2RoundingSerializer.class)
    private BigDecimal dutyRate;

    /**
     * 生效时间-国家时区
     */
    @Schema(name = "effectiveTime", description = "生效时间-国家时区")
    @TimeZoneIgnore
    private LocalDateTime effectiveTime;

    /**
     * 生效时间-UTC时区
     */
    @Schema(name = "effectiveTimeUtc", description = "生效时间-UTC时区")
    @TimeZoneIgnore
    private LocalDateTime effectiveTimeUtc;

    /**
     * 生效时间对应时区-页面时区
     */
    @Schema(name = "effectiveTimeCurrentLocale", description = "生效时间对应时区-页面时区")
    @TimeZoneIgnore
    private LocalDateTime effectiveTimeCurrentLocale;

    /**
     * 附加关税生效状态字典值, 字典: psku_customs_info_additional_duty_status
     */
    @Schema(name = "status", description = "附加关税生效状态字典值, 字典: psku_customs_info_additional_duty_status")
    @Trans(type = TransType.DICTIONARY, key = "psku_customs_info_additional_duty_status", ref = "statusName")
    private Integer status;

    /**
     * 附加关税生效状态
     */
    @Schema(name = "statusName", description = "附加关税生效状态")
    private String statusName;

    /**
     * 创建人 ID
     */
    @Schema(name = "createBy", description = "创建人 ID")
    private Integer createBy;

    /**
     * 创建人名称
     */
    @Schema(name = "createByName", description = "创建人名称")
    private String createByName;

    /**
     * 创建人工号
     */
    @Schema(name = "createByCode", description = "创建人工号")
    private String createByCode;

    /**
     * 创建时间
     */
    @Schema(name = "createTime", description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人 ID
     */
    @Schema(name = "updateBy", description = "更新人 ID")
    private Integer updateBy;

    /**
     * 更新人名称
     */
    @Schema(name = "updateByName", description = "更新人名称")
    private String updateByName;

    /**
     * 更新人工号
     */
    @Schema(name = "updateByCode", description = "更新人工号")
    private String updateByCode;

    /**
     * 更新时间
     */
    @Schema(name = "updateTime", description = "更新时间")
    private LocalDateTime updateTime;

    public void updateEffectiveTimeLocal() {
        ZoneId zoneId = Optional.ofNullable(TimeZoneContextHolder.get()).orElse(ZoneId.systemDefault());
        this.effectiveTimeCurrentLocale = Optional.ofNullable(this.effectiveTimeUtc)
                .map(t -> t.atZone(ZoneOffset.UTC))
                .map(t -> t.withZoneSameInstant(zoneId))
                .map(ZonedDateTime::toLocalDateTime)
                .orElse(null);
    }
}

