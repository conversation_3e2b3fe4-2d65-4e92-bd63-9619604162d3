package com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.command.converter;

import com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.command.*;
import com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.command.validator.ShippingFeeFormulaIdContainer;
import com.renpho.erp.tms.domain.shippingfeeformula.*;
import com.renpho.erp.tms.infrastructure.persistence.shippingfeeformula.converter.ShippingFeeFormulaArgConverter;
import com.renpho.erp.tms.infrastructure.persistence.shippingfeeformula.converter.ShippingFeeFormulaConfigConverter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {ShippingFeeFormulaArgConverter.class, ShippingFeeFormulaConfigConverter.class})
public interface ShippingFeeFormulaConfigCommandConverter {

    ShippingFeeFormulaArg toCommand(AddShippingFeeFormulaArgCmd cmd);

    ShippingFeeFormulaArg toCommand(UpdateShippingFeeFormulaArgCmd cmd);

    @Mapping(target = "id", source = "id")
    ShippingFeeFormulaArgId toCommand(ShippingFeeFormulaIdContainer id);

    @Mapping(target = "id", source = "id")
    ShippingFeeFormulaItem toCommand(ShippingFeeFormulaItemCmd cmd);

    ShippingFeeFormulaConfig toCommand(UpdateShippingFeeFormulaConfigItemCmd cmd, ShippingFeeFormulaConfigType type);

    default List<ShippingFeeFormulaConfig> toCommands(UpdateShippingFeeFormulaConfigCmd cmd) {
        ShippingFeeFormulaConfig fcl = toCommand(cmd.getFCL(), ShippingFeeFormulaConfigType.FCL);
        fcl.toScript();

        ShippingFeeFormulaConfig lcl = toCommand(cmd.getLCL(), ShippingFeeFormulaConfigType.LCL);
        lcl.toScript();

        return List.of(fcl, lcl);
    }
}