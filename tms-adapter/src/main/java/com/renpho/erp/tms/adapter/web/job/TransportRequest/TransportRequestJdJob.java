package com.renpho.erp.tms.adapter.web.job.TransportRequest;

import com.renpho.erp.tms.application.transportrequest.job.jd.TransportRequestJdAddService;
import com.renpho.erp.tms.application.transportrequest.job.jd.TransportRequestJdDeliveryService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * TR-目的仓为JD的定时器.
 * <AUTHOR>
 * @since 2025.07.25
 */
@SuppressWarnings("unused")
@Slf4j
@Component
@RequiredArgsConstructor
public class TransportRequestJdJob {

    private final TransportRequestJdAddService transportOrderJdService;
    private final TransportRequestJdDeliveryService transportOrderJdDeliveryService;

    /**
     * TR-目的仓为JD的推送任务生成-Add.
     * <br/>执行频率：
     */
    @XxlJob("createInboundJdAdd")
    public void createInboundJdTaskAdd() throws Exception {
        transportOrderJdService.createInboundJdTask();
    }

    /**
     * 执行: TR-目的仓为JD的入库单任务-Add.
     * <br/>执行频率：
     * @param trNoList tr单号集合，用于异常重试的参数触发机制
     */
    @XxlJob("doingInboundJdAdd")
    public void doingInboundJdAdd(List<String> trNoList) throws Exception {
        transportOrderJdService.doingInboundJd(trNoList);
    }

    /**
     * 执行: TR-目的仓为JD的箱唛任务-Add.
     * <br/>执行频率：
     * @param trNoList tr单号集合，用于异常重试的参数触发机制
     */
    @XxlJob("doingPalletJdAdd")
    public void doingPalletJdAdd(List<String> trNoList) throws Exception {
        transportOrderJdService.doPalletJd(trNoList);
    }

    /**
     * TR-目的仓为JD的推送任务生成-Delivery.
     * <br/>执行频率：
     */
    @XxlJob("createInboundJdDelivery")
    public void createInboundJdTaskDelivery() throws Exception {
        transportOrderJdDeliveryService.createInboundTask();
    }

    /**
     * 执行: TR-目的仓为JD的入库单任务-Delivery.
     * <br/>执行频率：
     * @param trNoList tr单号集合，用于异常重试的参数触发机制
     */
    @XxlJob("doingInboundJdDelivery")
    public void doingInboundJdDelivery(List<String> trNoList) throws Exception {
        transportOrderJdDeliveryService.doingInbound(trNoList);
    }

}
