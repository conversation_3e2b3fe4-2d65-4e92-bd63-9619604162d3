package com.renpho.erp.tms.adapter.web.controller.basic.config.port.info.cmd;

import com.renpho.erp.tms.domain.common.LanguageEnum;
import com.renpho.erp.tms.domain.exception.BusinessException;
import lombok.Data;

import java.util.List;

/**
 * 港口配置-更新参数-集合
 *
 * <AUTHOR>
 * @since 2025/6/12
 */
@Data
public class PortInfoUpdateListCmd {

    /**
     * 国家/地区
     */
    private String countryCode;

    /**
     * 港口配置-更新参数-集合
     */
    private List<PortInfoUpdateCmd> portInfoUpdateList;

    /**
     * 校验方法，根据语言类型返回不同的错误信息
     * @param language 语言类型
     */
    public void validate(LanguageEnum language) {
        if (isBlank(countryCode)) {
            //throwValidationError("国家/地区编码不能为空", "Country code is required", language);
            throw new BusinessException("PORT_INFO_COUNTRY_CODE_NULL_ERROR");
        } else {
            countryCode = countryCode.trim();
        }

        if (portInfoUpdateList == null || portInfoUpdateList.isEmpty()) {
            //throwValidationError("港口配置不能为空", "Port configuration list must not be empty", language);
            throw new BusinessException("PORT_INFO_CONFIG_NULL_ERROR");
        }

        for (PortInfoUpdateCmd cmd : portInfoUpdateList) {
            cmd.validate(language);
        }
    }

//    private void throwValidationError(String cnMsg, String enMsg, LanguageEnum language) {
//        String message = (language == LanguageEnum.China) ? cnMsg : enMsg;
//        throw new IllegalArgumentException(message); // 可替换为业务异常
//    }

    private boolean isBlank(String s) {
        return s == null || s.trim().isEmpty();
    }

}
