package com.renpho.erp.tms.adapter.web.controller.transportorder.cmd;

import com.renpho.erp.tms.application.transportorder.ExportDimension;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2025/6/19
 */
@Getter
@Setter
public class TransportOrderExportPackingListCmd {
    @NotNull
    private Integer toId;

    /**
     * 导出维度，TO:TO单维度，TRACKING_NO:运单号维度
     */
    private ExportDimension dimension;

    /**
     * 是否按货主维度导出，true:是，false:否
     */
    private Boolean groupByOwner;

}
