package com.renpho.erp.tms.adapter.web.controller.firstleg.transittime.command;

import com.renpho.erp.tms.adapter.web.controller.firstleg.mode.command.validator.CountryRegionExist;
import com.renpho.erp.tms.adapter.web.controller.firstleg.mode.command.validator.FirstLegModeExist;
import com.renpho.karma.dto.Command;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/22
 */
@Getter
@Setter
public class FirstLegModeTransitTimeAddCmd extends Command {
    /**
     * 头程方式ID
     */
    @NotNull
    @FirstLegModeExist
    private Integer modeId;

    /**
     * 国家(地区)编码
     */
    @NotNull
    @CountryRegionExist
    private String countryCode;

    /**
     * 状态 0：禁用 1：启用
     */
    @NotNull(message = "validation.status.required")
    private Integer status;

    /**
     * 备注
     */
    @Size(max = 100, message = "validation.remark.maxLength")
    private String remark;

    /**
     * 时效列表
     */
    @NotEmpty
    private List<@Valid FirstLegCountryRegionTransitTimeCmd> transitTimes;

}
