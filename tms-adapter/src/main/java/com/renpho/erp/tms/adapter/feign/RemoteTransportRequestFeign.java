package com.renpho.erp.tms.adapter.feign;

import com.renpho.erp.security.annotation.Inner;
import com.renpho.erp.tms.adapter.web.controller.transportrequest.cmd.converter.TransportRequestCmdConverter;
import com.renpho.erp.tms.adapter.web.controller.transportrequest.vo.converter.TransportRequestVOConverter;
import com.renpho.erp.tms.application.transportrequest.TransportRequestQueryService;
import com.renpho.erp.tms.client.transportrequest.RemoteTransportRequestService;
import com.renpho.erp.tms.client.transportrequest.request.ListTrQuery;
import com.renpho.erp.tms.client.transportrequest.request.TrNoQuery;
import com.renpho.erp.tms.client.transportrequest.response.TransportRequestClientVo;
import com.renpho.erp.tms.domain.exception.BusinessException;
import com.renpho.erp.tms.domain.transportrequest.TransportRequest;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestListQuery;
import com.renpho.karma.dto.R;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * TR 单 feign
 *
 * <AUTHOR>
 * @since 2025/7/23
 */
@RequiredArgsConstructor
@RestController
@ShenyuSpringCloudClient("/transport/request/**")
public class RemoteTransportRequestFeign implements RemoteTransportRequestService {
    private final TransportRequestQueryService transportRequestQueryService;
    private final TransportRequestVOConverter transportRequestVOConverter;
    private final TransportRequestCmdConverter transportRequestCmdConverter;

    @Inner
    @Override
    public R<TransportRequestClientVo> findByTrNo(@Valid @RequestBody TrNoQuery query) {
        String trNo = query.getTrNo();
        TransportRequest domain = transportRequestQueryService.findByNo(trNo).orElseThrow(() -> new BusinessException("DATA_DOES_NOT_EXIST_PLACEHOLDER", "trNo=" + trNo));
        TransportRequestClientVo vo = transportRequestVOConverter.toClientVo(domain);
        return R.success(vo);
    }

    @Override
    public R<List<TransportRequestClientVo>> findByNos(ListTrQuery query) {
        TransportRequestListQuery condition = transportRequestCmdConverter.toQueryPdDomain(query);
        List<TransportRequest> domains = transportRequestQueryService.findByNos(condition);
        List<TransportRequestClientVo> vos = transportRequestVOConverter.toClientVos(domains);
        return R.success(vos);
    }


}
