package com.renpho.erp.tms.adapter.web.controller.customsinfo.command;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 批量转换时区
 *
 * <AUTHOR>
 * @since 2025/4/24
 */
@Getter
@Setter
public class CountryTimeZoneQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 4969643710838385224L;

    /**
     * 国家地区编码
     */
    @NotBlank
    private String countryCode;

    /**
     * 用户当前使用的国家地区编码
     */
    @NotBlank
    @Pattern(regexp = "^UTC[+|-]\\d{1,2}:\\d{2}$")
    private String currentCountryTimeZone;

    /**
     * 待计算页面时间和生效状态的附加关税
     */
    @NotEmpty
    @Valid
    private List<@Valid @NotNull AdditionalDutyTimeCmd> additionalDuties;

    public String getCurrentCountryTimeZone() {
        if (StringUtils.defaultIfBlank(this.currentCountryTimeZone, StringUtils.EMPTY).matches("^UTC[+|-]\\d:\\d{2}$")) {
            char c = this.currentCountryTimeZone.charAt(3);
            return this.currentCountryTimeZone.replace(String.valueOf(c), c + "0");
        }
        return this.currentCountryTimeZone;
    }
}
