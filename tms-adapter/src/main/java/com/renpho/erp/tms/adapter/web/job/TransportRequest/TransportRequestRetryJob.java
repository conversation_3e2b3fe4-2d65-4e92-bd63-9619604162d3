package com.renpho.erp.tms.adapter.web.job.TransportRequest;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.renpho.erp.pms.model.shipmentplan.model.ShipmentPlanOrderDTO;
import com.renpho.erp.tms.adapter.stream.transportrequest.handler.TransportRequestCmdHandler;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * TR 单重试 Job
 *
 * <AUTHOR>
 * @since 2025/7/25
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TransportRequestRetryJob {

    private final ObjectMapper mapper;
    private final DataSource dataSource;
    private TransportRequestCmdHandler transportRequestCmdHandler;

    @XxlJob("retryAddTr")
    public void retryAddTr() throws Exception {
        String param = XxlJobHelper.getJobParam();
        log.info("TR 单重试 Job -> 添加 TR 单, 参数: [{}]", param);
        try {
            List<Integer> ids = mapper.readValue(param, new TypeReference<>() {});
            List<Integer> msgIds = CollectionUtils.emptyIfNull(ids).stream()
                    .filter(Objects::nonNull)
                    .toList();
            if (CollectionUtils.isEmpty(msgIds)) {
                log.info("IMS 对接 Job -> 执行离港任务, 未传入 TO 单 ID, 本次不执行");
                return;
            }

            List<ShipmentPlanOrderDTO> list = getMsgs(msgIds);

            for (ShipmentPlanOrderDTO cmd : list) {
                transportRequestCmdHandler.add(cmd);
            }
        } catch (JsonProcessingException e) {
            log.error("IMS 对接 Job -> 执行离港任务, 参数解析失败: [{}]", param, e);
            throw e;
        } catch (Exception e) {
            log.error("IMS 对接 Job -> 执行离港任务异常", e);
            throw e;
        } finally {
            log.info("IMS 对接 Job -> 执行离港任务结束");
        }
    }

    private List<ShipmentPlanOrderDTO> getMsgs(List<Integer> msgIds) throws SQLException, JsonProcessingException {
        List<ShipmentPlanOrderDTO> list = new ArrayList<>();
        try (Connection connection = dataSource.getConnection()) {
            try (PreparedStatement ps = connection.prepareStatement("SELECT message_body FROM erp_message_consumer WHERE message_id IN (?)")) {
                String ids = String.join(",", msgIds.stream().map(String::valueOf).toList());
                try (ResultSet rs = ps.executeQuery(ids)) {
                    while (rs.next()) {
                        String body = rs.getString("message_body");
                        log.info("TR 单重试 Job -> 添加 TR 单, 消息体: [{}]", body);
                        list.add(mapper.readValue(body, ShipmentPlanOrderDTO.class));
                    }
                }
            }
        }
        return list;
    }
}
