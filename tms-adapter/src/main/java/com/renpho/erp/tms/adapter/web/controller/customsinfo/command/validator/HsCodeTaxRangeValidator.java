package com.renpho.erp.tms.adapter.web.controller.customsinfo.command.validator;

import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.container.HsCodeTaxRangeContainer;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

import java.lang.annotation.Annotation;
import java.util.Optional;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @since 2025/4/25
 */
public abstract class HsCodeTaxRangeValidator<A extends Annotation, T> implements ConstraintValidator<A, T> {

    protected Predicate<T> predicate;

    @Override
    public boolean isValid(T value, ConstraintValidatorContext context) {
        return predicate.test(value);
    }

    public static class IsHsCodeTaxRangeExist extends HsCodeTaxRangeValidator<HsCodeTaxRangeExist, HsCodeTaxRangeContainer> {
        @Override
        public void initialize(HsCodeTaxRangeExist constraintAnnotation) {
            predicate = cmd -> Optional.ofNullable(cmd)
                    .filter(c -> !StringUtils.isAllBlank(c.getHsCodeAddTax(), c.getHsCodeReducedTax()))
                    .isPresent();
        }
    }
}
