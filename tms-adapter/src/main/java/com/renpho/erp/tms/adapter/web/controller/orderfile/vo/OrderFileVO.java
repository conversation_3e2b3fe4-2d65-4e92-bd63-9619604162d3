package com.renpho.erp.tms.adapter.web.controller.orderfile.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/11
 */
@Data
public class OrderFileVO implements VO{

    @JsonIgnore
    private Integer id;

    /**
     * 文件类型：对应字典为TMS_TO_FILE_TYPE
     * FREIGHT_QUOTE-货代报价单；
     * BILL_OF_LADING-提单；
     * INSURANCE_POLICY-保单；
     * POD-POD；
     * CUSTOMS_DECLARATION-报关单；
     * PACKING_LIST-箱单；INVOICE-发票；CUSTOMS_TAX_RECEIPT-海关税单；
     * CERTIFICATE_OF_ORIGIN-产地证；
     * OTHER-其他文件
     */
    @Trans(type = TransType.DICTIONARY, key = "TMS_TO_FILE_TYPE", ref = "fileTypeName")
    private String fileType;


    /**
     * 文件类型名称
     */
    private String fileTypeName;
    /**
     * 文件ID
     */
    private List<OrderFileInfoVO> fileInfos;
}
