package com.renpho.erp.tms.adapter.web.controller.transportorder.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.renpho.erp.tms.adapter.web.controller.transportrequest.vo.TransportRequestDetailVO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/1
 */
@Getter
@Setter
public class TransportOrderForwarderQuoteConfirmVo implements Serializable, VO {

    @Serial
    private static final long serialVersionUID = -5524277152325567195L;

    /**
     * TO单ID
     */
    private Integer id;

    /**
     * TO单号
     */
    private String toNo;

    /**
     * 总数量
     */
    private Integer totalQty;

    /**
     * 总箱数
     */
    private Integer totalBoxQty;

    /**
     * 总毛重
     */
    private BigDecimal totalGrossWeight;

    /**
     * 总体积
     */
    private BigDecimal totalVolume;

    /**
     * 计费重
     */
    private BigDecimal chargeWeight;

    /**
     * 计费重类型字典, 字典类型: charge_weight_type
     */
    @Trans(type = TransType.DICTIONARY, key = "charge_weight_type", ref = "chargeWeightTypeName")
    private String chargeWeightType;

    /**
     * 计费重类型
     */
    private String chargeWeightTypeName;

    /**
     * TR单集合
     */
    private List<TransportRequestDetailVO> trs;

    /**
     * 是否置灰
     */
    private Boolean isSelectable;

    /**
     * 选中包税还是不包税，为 null 未选中
     */
    private Boolean isIncludeTax;

    /**
     * 是否二次审批
     */
    private Boolean isResubmit;

    /**
     * 选中货代(物流供应商) ID
     */
    private Integer forwarderId;

    /**
     * 货代集合
     */
    private List<TransportOrderForwarderVo> forwarders;

}
