package com.renpho.erp.tms.adapter.web.controller.transportrequest.cmd;

import com.renpho.erp.tms.adapter.web.controller.transportrequest.cmd.container.TrIdsContainer;
import jakarta.annotation.Generated;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.io.Serializable;
import java.util.Collection;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/5/19
 */
@lombok.Getter
@lombok.Setter
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-05-19T18:06:01.197175700+08:00[Asia/Shanghai]", comments = "Generator version: 7.12.0")
public class IdsCmd extends com.renpho.karma.dto.Query implements TrIdsContainer<Collection<Integer>>, Serializable {

    @Serial
    private static final long serialVersionUID = -6608734910143042478L;

    @NotEmpty
    @Valid
    private Set<@Valid @NotNull Integer> ids;

}

