package com.renpho.erp.tms.adapter.web.controller.transportrequest.vo.converter;

import com.renpho.erp.tms.adapter.web.controller.transportrequest.vo.TransportRequestDetailVO;
import com.renpho.erp.tms.adapter.web.controller.transportrequest.vo.TransportRequestExportVO;
import com.renpho.erp.tms.adapter.web.controller.transportrequest.vo.TransportRequestVO;
import com.renpho.erp.tms.client.transportrequest.response.TrStatus;
import com.renpho.erp.tms.client.transportrequest.response.TransportRequestClientVo;
import com.renpho.erp.tms.client.transportrequest.response.TransportRequestProductVo;
import com.renpho.erp.tms.domain.common.HazardousProsEnum;
import com.renpho.erp.tms.domain.transportrequest.TransportRequest;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestItem;
import com.renpho.erp.tms.domain.warehouse.WarehouseAddress;
import com.renpho.erp.tms.infrastructure.common.converter.MultiLanguageConverter;
import com.renpho.erp.tms.infrastructure.common.converter.TransportOrderStatusConverter;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.po.converter.TransportRequestConverter;
import com.renpho.erp.tms.infrastructure.remote.product.converter.ProductConverter;
import com.renpho.erp.tms.infrastructure.remote.user.converter.OperatorConverter;
import com.renpho.karma.dto.Paging;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE,
        componentModel = MappingConstants.ComponentModel.SPRING,
        imports = {BigDecimal.class, RoundingMode.class, Optional.class},
        uses = {ProductConverter.class,
                TransportRequestConverter.class,
                MultiLanguageConverter.class,
                OperatorConverter.class,
                TransportOrderStatusConverter.class})
public interface TransportRequestVOConverter {

    @Mapping(target = "id", source = "id.id")
    @Mapping(target = "psku", source = "product.psku")
    @Mapping(target = "hazardousPros", source = "item.hazardousPros", qualifiedByName = "mapToHazardousPros")
    @Mapping(target = "firstLegModeId", source = "firstLegMode.id.id")
    @Mapping(target = "firstLegModeName", source = "firstLegMode.name")
    @Mapping(target = "salesChannelId", source = "salesChannel.id.id")
    @Mapping(target = "salesChannelName", source = "salesChannel.channelName")
    @Mapping(target = "ownerId", source = "owner.id.id")
    @Mapping(target = "ownerName", source = "owner.names")
    @Mapping(target = "tradeTermsName", source = "tradeTerms")
    @Mapping(target = "shippingPortName", source = "shippingPort")
    @Mapping(target = "destCountry", source = "destCountry.fullName")
    @Mapping(target = "destWarehouseId", source = "destWarehouse.id.id")
    @Mapping(target = "destWarehouseName", source = "destWarehouse.languages")
    @Mapping(target = "destWarehouseCode", source = "destWarehouseCode")
    @Mapping(target = "purchaseSupplierId", source = "purchaseSupplier.id.id")
    @Mapping(target = "purchaseSupplierName", source = "purchaseSupplier.names")
    @Mapping(target = "purchaseSupplierCode", source = "purchaseSupplier.supplierCode")
    @Mapping(target = "purchaseSupplierShortName", source = "purchaseSupplier.shortName")
    @Mapping(target = "shipStatus", source = "shipStatus.value")
    @Mapping(target = "totalQty", source = "quantity")
    @Mapping(target = "totalBoxQty", source = "boxQty", qualifiedByName = "mapToInteger")
    @Mapping(target = "totalVolume", source = "totalBoxVolume")
    @Mapping(target = "createBy", source = "created.operatorId.id")
    @Mapping(target = "creator", source = "created", qualifiedByName = "concatOperatorInfo")
    @Mapping(target = "createTime", source = "created.operateTime")
    @Mapping(target = "updateBy", source = "updated.operatorId.id")
    @Mapping(target = "updater", source = "updated", qualifiedByName = "concatOperatorInfo")
    @Mapping(target = "updateTime", source = "updated.operateTime")
    TransportRequestVO toPageListVO(TransportRequest domain);

    List<TransportRequestVO> toListVOs(Collection<TransportRequest> domains);

    Paging<TransportRequestVO> toPageListVOs(Paging<TransportRequest> domains);

    @Named("mapToInteger")
    default Integer mapToInteger(BigDecimal totalBoxQty) {
        if (totalBoxQty == null) {
            return 0;
        }
        return totalBoxQty.setScale(0, RoundingMode.HALF_UP).intValue();
    }

    @Named("mapToHazardousPros")
    default List<String> mapToHazardousPros(String hazardousPros) {
        return HazardousProsEnum.getNameByVals(Arrays.stream(StringUtils.defaultIfBlank(hazardousPros, StringUtils.EMPTY).split(",")).toList());
    }

    @Named("mapToHazardousProString")
    default String mapToHazardousProString(String hazardousPros) {
        return String.join(",", mapToHazardousPros(hazardousPros));
    }


    @Mapping(target = "id", source = "id.id")
    @Mapping(target = "psku", source = "product.psku")
    @Mapping(target = "hazardousPros", source = "item.hazardousPros", qualifiedByName = "mapToHazardousProString")
    @Mapping(target = "nameEn", source = "item.product.names", qualifiedByName = "findEnName")
    @Mapping(target = "firstLegModeName", source = "firstLegMode.name")
    @Mapping(target = "salesChannelName", source = "salesChannel.channelName")
    @Mapping(target = "ownerName", source = "owner.names")
    @Mapping(target = "destCountry", source = "destCountry.fullName")
    @Mapping(target = "destWarehouseCode", source = "destWarehouseCode")
    @Mapping(target = "purchaseSupplierName", source = "purchaseSupplier.names")
    @Mapping(target = "totalQty", source = "quantity")
    @Mapping(target = "totalBoxQty", source = "boxQty", qualifiedByName = "mapToInteger")
    @Mapping(target = "plannedShipStartDate", source = "plannedShipStartDate")
    @Mapping(target = "plannedShipEndDate", source = "plannedShipEndDate")
    @Mapping(target = "updateTime", source = "updated.operateTime")
    @Mapping(target = "destination", source = "destWarehouse.fullAddress")
    TransportRequestExportVO toTransportRequestExportVo(TransportRequest domain);

    List<TransportRequestExportVO> toTransportRequestExportVos(Collection<TransportRequest> domains);

    default String mapToDestination(WarehouseAddress addr) {
        Optional<WarehouseAddress> optional = Optional.ofNullable(addr);
        if (optional.isEmpty()) {
            return "";
        }
        return Stream.of(optional.map(WarehouseAddress::getAddress1).filter(StringUtils::isNotBlank),
                        optional.map(WarehouseAddress::getAddress2).filter(StringUtils::isNotBlank),
                        optional.map(WarehouseAddress::getArea).filter(StringUtils::isNotBlank),
                        optional.map(WarehouseAddress::getCity).filter(StringUtils::isNotBlank),
                        optional.map(WarehouseAddress::getProvince).filter(StringUtils::isNotBlank),
                        optional.map(WarehouseAddress::getZipCode).filter(StringUtils::isNotBlank),
                        optional.map(WarehouseAddress::getCountryCode).filter(StringUtils::isNotBlank),
                        optional.map(WarehouseAddress::getContactNumber).filter(StringUtils::isNotBlank),
                        optional.map(WarehouseAddress::getContactName).filter(StringUtils::isNotBlank))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.joining(", "));
    }

    @Mapping(target = "id", source = "id.id")
    @Mapping(target = "firstLegModeId", source = "firstLegMode.id.id")
    @Mapping(target = "firstLegModeName", source = "firstLegMode.name")
    @Mapping(target = "salesChannelId", source = "salesChannel.id.id")
    @Mapping(target = "salesChannelName", source = "salesChannel.channelName")
    @Mapping(target = "salesChannelCode", source = "salesChannel.channelCode")
    @Mapping(target = "storeId", source = "store.id.id")
    @Mapping(target = "storeName", source = "store.name")
    @Mapping(target = "ownerId", source = "owner.id.id")
    @Mapping(target = "ownerName", source = "owner.names")
    @Mapping(target = "ownerCode", source = "owner.shortName")
    @Mapping(target = "destCountry", source = "destCountry.fullName")
    @Mapping(target = "destWarehouseId", source = "destWarehouse.id.id")
    @Mapping(target = "destWarehouseName", source = "destWarehouse.languages")
    @Mapping(target = "destWarehouseCode", source = "destWarehouseCode")
    @Mapping(target = "destination", source = "destWarehouse.fullAddress")
    @Mapping(target = "tradeTermsName", source = "tradeTerms")
    @Mapping(target = "shippingPortName", source = "shippingPort")
    @Mapping(target = "shippingWarehouseId", source = "shippingWarehouse.id.id")
    @Mapping(target = "shippingWarehouseName", source = "shippingWarehouse.languages")
    @Mapping(target = "shippingWarehouseCode", source = "shippingWarehouse.code")
    @Mapping(target = "purchaseSupplierId", source = "purchaseSupplier.id.id")
    @Mapping(target = "purchaseSupplierName", source = "purchaseSupplier.names")
    @Mapping(target = "purchaseSupplierCode", source = "purchaseSupplier.supplierCode")
    @Mapping(target = "purchaseSupplierShortName", source = "purchaseSupplier.shortName")
    @Mapping(target = "salesStaffCode", source = "salesStaff.operatorCode")
    @Mapping(target = "salesStaffId", source = "salesStaff.operatorId.id")
    @Mapping(target = "salesStaffName", source = "salesStaff", qualifiedByName = "concatOperatorInfo")
    @Mapping(target = "planningStaffCode", source = "planningStaff.operatorCode")
    @Mapping(target = "planningStaffId", source = "planningStaff.operatorId.id")
    @Mapping(target = "planningStaffName", source = "planningStaff", qualifiedByName = "concatOperatorInfo")
    @Mapping(target = "purchaseStaffCode", source = "purchaseStaff.operatorCode")
    @Mapping(target = "purchaseStaffId", source = "purchaseStaff.operatorId.id")
    @Mapping(target = "purchaseStaffName", source = "purchaseStaff", qualifiedByName = "concatOperatorInfo")
    @Mapping(target = "estimatedTaxCurrencyCode", source = "estimatedTaxCurrency.id.code")
    @Mapping(target = "estimatedFreightCurrencyCode", source = "estimatedFreightCurrency.id.code")
    @Mapping(target = "product", source = "item")
    @Mapping(target = "product.nameCn", source = "item.product.names", qualifiedByName = "findCnName")
    @Mapping(target = "product.nameEn", source = "item.product.names", qualifiedByName = "findEnName")
    @Mapping(target = "product.hazardousPros", source = "item.hazardousPros", qualifiedByName = "mapToHazardousPros")
    @Mapping(target = "product.picture", source = "product.picture")
    @Mapping(target = "product.totalVolume", source = "totalBoxVolume")
    @Mapping(target = "product.totalGrossWeight", source = "totalGrossWeight")
    @Mapping(target = "product.totalNetWeight", source = "totalNetWeight")
    @Mapping(target = "product.totalQty", source = "quantity")
    @Mapping(target = "product.totalBoxes", source = "boxQty", qualifiedByName = "toTotalBoxes", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_DEFAULT)
    TransportRequestDetailVO toDetailVO(TransportRequest transportRequest);

    @Named("toTotalBoxes")
    default Integer toTotalBoxes(BigDecimal boxQty) {
        return Optional.ofNullable(boxQty)
                .map(b -> b.setScale(0, RoundingMode.UP))
                .orElse(BigDecimal.ZERO)
                .intValue();
    }

    @Mapping(target = "productId", source = "product.id.id")
    @Mapping(target = "nameCn", source = "product.names", qualifiedByName = "findCnName")
    @Mapping(target = "nameEn", source = "product.names", qualifiedByName = "findEnName")
    @Mapping(target = "model", source = "product.modelName")
    @Mapping(target = "brand", source = "product.brandName")
    @Mapping(target = "psku", source = "product.psku")
    @Mapping(target = "fnSku", source = "product.fnSku")
//    @Mapping(target = "totalQty", source = "quantity")
//    @Mapping(target = "totalBoxes", source = "boxQty")
    @Mapping(target = "productLength", source = "product.lengthMetric")
    @Mapping(target = "productWidth", source = "product.widthMetric")
    @Mapping(target = "productHeight", source = "product.heightMetric")
    @Mapping(target = "packagingLength", source = "product.packagingLengthMetric")
    @Mapping(target = "packagingWidth", source = "product.packagingWidthMetric")
    @Mapping(target = "packagingHeight", source = "product.packagingHeightMetric")
    @Mapping(target = "boxLengthMetric", source = "product.activeBoxSpec.boxLengthMetric")
    @Mapping(target = "boxWidthMetric", source = "product.activeBoxSpec.boxWidthMetric")
    @Mapping(target = "boxHeightMetric", source = "product.activeBoxSpec.boxHeightMetric")
    @Mapping(target = "weight", source = "product.weightMetric")
    @Mapping(target = "grossWeight", source = "product.grossWeightMetric")
    @Mapping(target = "boxGrossWeight", source = "product.activeBoxSpec.grossWeightPerBoxMetric")
    @Mapping(target = "hazardousPros", source = "hazardousPros", qualifiedByName = "mapToHazardousPros")
    TransportRequestProductVo toTransportRequestProductVo(TransportRequestItem item);

    @InheritConfiguration(name = "toDetailVO")
    @Mapping(target = "destCountry", source = "destCountry.fullName")
    @BeanMapping(qualifiedByName = "updateCombineStatus")
    TransportRequestClientVo toClientVo(TransportRequest domain);

    List<TransportRequestClientVo> toClientVos(Collection<TransportRequest> domain);

    @Named("updateCombineStatus")
    @AfterMapping
    default void updateCombineStatus(TransportRequest domain, @MappingTarget TransportRequestClientVo vo) {
        switch (domain.getStatus()) {
            case CANCEL -> vo.setTrStatus(TrStatus.CANCEL);
            case PENDING -> vo.setTrStatus(TrStatus.PENDING);
            case PENDING_CONSOLIDATION -> vo.setTrStatus(TrStatus.CONSOLIDATION);
            case CONSOLIDATION -> {
                switch (domain.getShipStatus()) {
                    case DEFAULT -> vo.setTrStatus(TrStatus.CONSOLIDATION_FINISH);
                    case QUOTING -> vo.setTrStatus(TrStatus.QUOTING);
                    case BOOKING_IN_PROGRESS -> vo.setTrStatus(TrStatus.BOOKING_IN_PROGRESS);
                    case BOOKED -> vo.setTrStatus(TrStatus.BOOKED);
                    case FACTORY_SHIPPED -> vo.setTrStatus(TrStatus.FACTORY_SHIPPED);
                    case HANDED_OVER_TO_CARRIER -> vo.setTrStatus(TrStatus.HANDED_OVER_TO_CARRIER);
                    case DEPARTED -> vo.setTrStatus(TrStatus.DEPARTED);
                    case ARRIVED_AT_PORT -> vo.setTrStatus(TrStatus.ARRIVED_AT_PORT);
                    case OUT_FOR_DELIVERY -> vo.setTrStatus(TrStatus.OUT_FOR_DELIVERY);
                    case DELIVERED -> vo.setTrStatus(TrStatus.DELIVERED);
                    case COMPLETED -> vo.setTrStatus(TrStatus.COMPLETED);
                    case VOIDED -> vo.setTrStatus(TrStatus.VOIDED);
                    case DELIVERED_PART -> vo.setTrStatus(TrStatus.DELIVERED_PART);
                }
            }
        }
    }

}
