package com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.validator;

import com.renpho.erp.tms.adapter.web.controller.command.validator.IdExist;
import com.renpho.erp.tms.adapter.web.controller.command.validator.IdsExist;
import com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.container.TransportOrderIdContainer;
import com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.container.TransportOrderIdsContainer;
import com.renpho.erp.tms.application.transportorder.TransportOrderQueryService;
import com.renpho.erp.tms.domain.transportorder.TransportOrder;
import com.renpho.erp.tms.domain.transportorder.TransportOrderId;
import com.renpho.erp.tms.domain.transportorder.TransportOrderQuery;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.collections4.CollectionUtils;

import java.lang.annotation.Annotation;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/6/13
 */
public abstract class TransportOrderValidator<A extends Annotation, T> implements ConstraintValidator<A, T> {

    protected Predicate<T> predicate;

    @Resource
    protected TransportOrderQueryService transportOrderQueryService;

    @Override
    public boolean isValid(T value, ConstraintValidatorContext context) {
        return transportOrderQueryService == null || predicate.test(value);
    }

    public static class IsIdsExist extends TransportOrderValidator<IdsExist, TransportOrderIdsContainer<Collection<Integer>>> {

        @Override
        public void initialize(IdsExist constraintAnnotation) {
            predicate = cmd -> {
                Optional<TransportOrderIdsContainer<Collection<Integer>>> optional = Optional.ofNullable(cmd);
                Set<TransportOrderId> toIds = optional
                        .map(TransportOrderIdsContainer::getIds)
                        .filter(CollectionUtils::isNotEmpty)
                        .stream()
                        .flatMap(Collection::stream)
                        .filter(Objects::nonNull)
                        .map(TransportOrderId::new)
                        .collect(Collectors.toSet());
                if (CollectionUtils.isEmpty(toIds)) {
                    return false;
                }
                Map<TransportOrderId, TransportOrder> tos = transportOrderQueryService.findByIds(toIds);
                if (tos.keySet().containsAll(toIds)) {
                    List<TransportOrder> list = tos.keySet().stream().map(id -> {
                        TransportOrderQuery query = new TransportOrderQuery();
                        query.setToId(id.id());
                        return query;
                    }).map(transportOrderQueryService::detail).toList();
                    optional.ifPresent(c -> c.setTos(list));
                    return true;
                } else {
                    return false;
                }
            };
        }
    }

    public static class IsIdExist extends TransportOrderValidator<IdExist, TransportOrderIdContainer<Integer>> {

        @Override
        public void initialize(IdExist constraintAnnotation) {
            predicate = cmd -> {
                Optional<TransportOrderIdContainer<Integer>> optional = Optional.ofNullable(cmd);
                Optional<Integer> toId = optional.map(TransportOrderIdContainer::getId);
                if (toId.isEmpty()) {
                    return false;
                }
                Optional<TransportOrder> to = toId.map(id -> {
                    TransportOrderQuery query = new TransportOrderQuery();
                    query.setToId(id);
                    return query;
                }).map(transportOrderQueryService::detail);
                to.ifPresent(t -> optional.ifPresent(c -> c.setTo(t)));
                return to.isPresent();
            };
        }
    }
}
