package com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.command;

import com.renpho.erp.tms.domain.shippingfeeformula.FormulaArgType;
import jakarta.validation.Valid;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/6/12
 */
@Getter
@Setter
public class UpdateShippingFeeFormulaConfigItemCmd extends ShippingFeeFormulaConfigIdCmd implements Serializable {

    @Serial
    private static final long serialVersionUID = 1281700418474387325L;

    /**
     * 公式返回值名称
     */
    private String returnName;

    /**
     * 公式 JSON
     */
    @Valid
    @NotNull
    private List<@Valid @NotNull ShippingFeeFormulaItemCmd> formulaJson;

    @AssertTrue(message = "{error.formula_compile}")
    @SuppressWarnings("unused")
    public boolean isContainsOperator() {
        if (CollectionUtils.isEmpty(formulaJson)) {
            return true;
        }
        return formulaJson.stream()
                .filter(Objects::nonNull)
                .map(ShippingFeeFormulaItemCmd::getType)
                .filter(Objects::nonNull)
                .anyMatch(FormulaArgType.OPERATOR::equals);
    }

    @AssertTrue(message = "{error.formula_compile} 公式不能以运算符开头")
    @SuppressWarnings("unused")
    public boolean isEmptyOrNotBeginWithOperator() {
        if (CollectionUtils.isEmpty(formulaJson)) {
            return true;
        }
        ShippingFeeFormulaItemCmd begin = formulaJson.get(0);
        return FormulaArgType.OPERATOR != begin.getType() || begin.getValue().equals("(");
    }

}
