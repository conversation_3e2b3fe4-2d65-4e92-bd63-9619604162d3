package com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.command;

import com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.command.validator.ShippingFeeFormulaItemArgNameContainer;
import com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.command.validator.ShippingFeeFormulaItemNameContainer;
import com.renpho.karma.dto.Command;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/6/11
 */
@Getter
@Setter
public class AddShippingFeeFormulaArgCmd extends Command implements ShippingFeeFormulaItemNameContainer, ShippingFeeFormulaItemArgNameContainer, Serializable {

    @Serial
    private static final long serialVersionUID = 4456141887046675894L;

    /**
     * 计费项参数名
     */
    @NotBlank
    private String argName;

    /**
     * 计费项名称，如基础运费、报关费
     */
    @NotBlank
    private String name;

    /**
     * 单位，字典: UNIT_OF_SHIPPING_FEE_ITEM
     */
    @NotBlank
    private String unit;
}
