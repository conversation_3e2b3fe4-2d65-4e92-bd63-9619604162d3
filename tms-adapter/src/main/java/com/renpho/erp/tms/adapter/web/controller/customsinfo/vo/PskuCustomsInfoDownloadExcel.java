package com.renpho.erp.tms.adapter.web.controller.customsinfo.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/4/27
 */
@Getter
@Setter
public class PskuCustomsInfoDownloadExcel implements VO, Serializable {

    @Serial
    private static final long serialVersionUID = 7953238646597547652L;

    @ExcelIgnore
    private Integer id;

    @ExcelProperty(value = "PSKU", index = 0)
    private String psku;

    @ExcelProperty(value = "excel.country_code", index = 1)
    private String countryCode;

    @ExcelProperty(value = "excel.hs_code_added_tax", index = 2)
    private String hsCodeAddTax;

    @ExcelProperty(value = "excel.hs_code_reduced_tax", index = 3)
    private String hsCodeReducedTax;

    @ExcelProperty(value = "excel.currency_code", index = 4)
    private String currencyCode;

    @ExcelProperty(value = "excel.others", index = 5)
    private BigDecimal others;

    @Trans(type = TransType.DICTIONARY, key = "psku_customs_info_status", ref = "statusName")
    @ExcelIgnore
    private Integer status;

    @ExcelProperty(value = "excel.status", index = 6)
    private String statusName;

    @ExcelProperty(value = "excel.base_duty_rate", index = 7)
    private BigDecimal baseDutyRate;

    @ExcelProperty(value = "excel.gst", index = 8)
    private BigDecimal gst;

    @ExcelProperty(value = "excel.vat", index = 9)
    private BigDecimal vat;

    @ExcelProperty(value = "excel.additional_duty_name", index = 10)
    private String name;

    @ExcelProperty(value = "excel.additional_duty_rate", index = 11)
    private BigDecimal dutyRate;

    @ExcelProperty(value = "excel.additional_duty_effect_time", index = 12)
    private LocalDateTime effectiveTime;

    @ExcelProperty(value = "excel.additional_duty_effect_time_local", index = 13)
    private LocalDateTime effectiveTimePage;

    @Trans(type = TransType.DICTIONARY, key = "psku_customs_info_additional_duty_status", ref = "additionalDutyStatusName")
    @ExcelIgnore
    private Integer additionalDutyStatus;

    @ExcelProperty(value = "excel.additional_duty_effect_status", index = 14)
    private String additionalDutyStatusName;

    @ExcelProperty(value = "excel.remark", index = 15)
    private String remark;
}
