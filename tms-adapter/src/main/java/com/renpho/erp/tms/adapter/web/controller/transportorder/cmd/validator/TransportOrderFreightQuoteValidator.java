package com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.validator;

import com.renpho.erp.tms.adapter.web.controller.command.validator.IdExist;
import com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.container.TransportOrderFreightQuoteIdContainer;
import com.renpho.erp.tms.domain.transportorderforwarderapproval.TransportOrderFreightQuote;
import com.renpho.erp.tms.domain.transportorderforwarderapproval.TransportOrderFreightQuoteId;
import com.renpho.erp.tms.domain.transportorderforwarderapproval.TransportOrderFreightQuoteLookup;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.lang.annotation.Annotation;
import java.util.Optional;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @since 2025/6/13
 */
public abstract class TransportOrderFreightQuoteValidator<A extends Annotation, T> implements ConstraintValidator<A, T> {

    protected Predicate<T> predicate;

    @Resource
    protected TransportOrderFreightQuoteLookup transportOrderFreightQuoteLookup;

    @Override
    public boolean isValid(T value, ConstraintValidatorContext context) {
        return transportOrderFreightQuoteLookup == null || predicate.test(value);
    }

    public static class IsIdExist extends TransportOrderFreightQuoteValidator<IdExist, TransportOrderFreightQuoteIdContainer> {
        @Override
        public void initialize(IdExist constraintAnnotation) {
            predicate = cmd -> {
                Optional<TransportOrderFreightQuoteIdContainer> optional = Optional.ofNullable(cmd);
                Optional<TransportOrderFreightQuote> domain = optional.map(TransportOrderFreightQuoteIdContainer::getId)
                        .map(TransportOrderFreightQuoteId::new)
                        .flatMap(transportOrderFreightQuoteLookup::findById);
                domain.ifPresent(c -> optional.ifPresent(d -> d.setTransportOrderFreightQuote(c)));
                return domain.isPresent();
            };
        }
    }
}
