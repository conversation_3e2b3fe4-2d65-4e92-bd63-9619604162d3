package com.renpho.erp.tms.adapter.web.controller.command.validator;

import com.renpho.erp.tms.adapter.web.controller.command.container.StoreIdContainer;
import com.renpho.erp.tms.domain.store.StoreId;
import com.renpho.erp.tms.infrastructure.common.converter.BooleanConverter;
import com.renpho.erp.tms.infrastructure.remote.store.repository.StoreLookup;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.lang.annotation.Annotation;
import java.util.Optional;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @since 2025/3/18
 */
public abstract class StoreValidator<A extends Annotation, T> implements ConstraintValidator<A, T> {

    protected Predicate<T> predicate;

    @Resource
    protected StoreLookup storeLookup;

    @Resource
    protected BooleanConverter booleanConverter;

    @Override
    public boolean isValid(T value, ConstraintValidatorContext context) {
        return storeLookup == null || predicate.test(value);
    }

    public static class StoreIdExist extends StoreValidator<StoreExist, StoreIdContainer> {

        @Override
        public void initialize(StoreExist constraintAnnotation) {
            predicate = cmd -> Optional.ofNullable(cmd)
                    .map(StoreIdContainer::getStoreId)
                    .map(StoreId::new)
                    .flatMap(storeLookup::findById)
                    .filter(c -> booleanConverter.toBoolean(c.getStatus()))
                    .isPresent();
        }

    }

}
