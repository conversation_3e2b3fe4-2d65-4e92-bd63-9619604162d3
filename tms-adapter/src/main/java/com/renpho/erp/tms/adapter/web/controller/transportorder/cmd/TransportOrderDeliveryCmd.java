package com.renpho.erp.tms.adapter.web.controller.transportorder.cmd;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2025/6/12
 */
@Getter
@Setter
public class TransportOrderDeliveryCmd {
    /**
     * to单Id
     */
    @NotNull
    private Integer toId;

    /**
     * TR单号
     */
    @NotNull
    private Integer trId;

    /**
     * 实际派送时间
     */
    @NotNull
    private LocalDate actualDeliveryTime;

}
