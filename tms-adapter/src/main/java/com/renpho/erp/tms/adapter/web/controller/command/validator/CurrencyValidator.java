package com.renpho.erp.tms.adapter.web.controller.command.validator;

import com.renpho.erp.tms.adapter.web.controller.command.container.CurrencyCodeAndCountryCodeContainer;
import com.renpho.erp.tms.domain.currency.Currency;
import com.renpho.erp.tms.infrastructure.common.converter.BooleanConverter;
import com.renpho.erp.tms.infrastructure.remote.currency.repository.CurrencyLookup;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

import java.lang.annotation.Annotation;
import java.util.Optional;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @since 2025/4/25
 */
public abstract class CurrencyValidator<A extends Annotation, T> implements ConstraintValidator<A, T> {

    protected Predicate<T> predicate;

    @Resource
    protected CurrencyLookup currencyLookup;

    @Resource
    protected BooleanConverter booleanConverter;

    @Override
    public boolean isValid(T value, ConstraintValidatorContext context) {
        return currencyLookup == null || predicate.test(value);
    }

    public static class IsCurrencyExist extends CurrencyValidator<CurrencyExist, CurrencyCodeAndCountryCodeContainer> {

        @Override
        public void initialize(CurrencyExist constraintAnnotation) {
            predicate = cmd -> {
                Optional<CurrencyCodeAndCountryCodeContainer> optional = Optional.ofNullable(cmd);
                Optional<String> currencyCode = optional.map(CurrencyCodeAndCountryCodeContainer::getCurrencyCode).filter(StringUtils::isNotBlank);
                if (optional.map(CurrencyCodeAndCountryCodeContainer::getCountryCode)
                        .filter(c -> StringUtils.equals(c, "CN"))
                            .isPresent() && currencyCode.isEmpty()) {
                    return true;
                }
                Optional<Currency> currency = currencyCode.flatMap(currencyLookup::findByCode).filter(c -> booleanConverter.toBoolean(c.getStatus()));
                if (currency.isEmpty()) {
                    return false;
                }
                cmd.setCurrency(currency.get());
                return true;
            };
        }
    }
}
