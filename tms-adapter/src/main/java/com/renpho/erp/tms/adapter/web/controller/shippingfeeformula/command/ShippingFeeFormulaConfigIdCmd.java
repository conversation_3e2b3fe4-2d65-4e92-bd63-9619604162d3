package com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.command;

import com.renpho.erp.tms.adapter.web.controller.command.validator.IdExist;
import com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.command.validator.ShippingFeeFormulaIdContainer;
import com.renpho.karma.dto.Command;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2025/6/12
 */
@Getter
@Setter
@IdExist
public class ShippingFeeFormulaConfigIdCmd extends Command implements ShippingFeeFormulaIdContainer {

    @Serial
    private static final long serialVersionUID = -9093834148699817123L;

    /**
     * 运费公式 ID
     */
    @NotNull
    private Integer id;
}
