package com.renpho.erp.tms.adapter.web.controller.orderfile;

import com.renpho.erp.tms.adapter.web.controller.command.group.CreateGroup;
import com.renpho.erp.tms.adapter.web.controller.command.group.OtherGroup;
import com.renpho.erp.tms.adapter.web.controller.command.group.QueryGroup;
import com.renpho.erp.tms.adapter.web.controller.orderfile.cmd.OrderFileCmd;
import com.renpho.erp.tms.adapter.web.controller.orderfile.cmd.OrderFileInfoCmd;
import com.renpho.erp.tms.adapter.web.controller.orderfile.cmd.converter.OrderFileCmdConverter;
import com.renpho.erp.tms.adapter.web.controller.orderfile.vo.OrderFileInfoVO;
import com.renpho.erp.tms.adapter.web.controller.orderfile.vo.OrderFileVO;
import com.renpho.erp.tms.adapter.web.controller.orderfile.vo.converter.OrderFileVOConverter;
import com.renpho.erp.tms.application.orderfile.OrderFileService;
import com.renpho.erp.tms.domain.file.FileId;
import com.renpho.erp.tms.domain.orderfile.BusinessTypeEnum;
import com.renpho.erp.tms.domain.orderfile.OrderFile;
import com.renpho.erp.tms.domain.orderfile.OrderFileInfo;
import com.renpho.erp.tms.domain.transportorder.TransportOrderId;
import com.renpho.karma.dto.R;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 附件接口.
 *
 * <AUTHOR> Zheng
 * @since 2025-05-29
 */
@Validated
@RestController
@Tag(name = "order file", description = "文件接口")
@RequestMapping("/order/file")
@ShenyuSpringCloudClient("/order/file/**")
@RequiredArgsConstructor
public class OrderFileController {

    private final OrderFileCmdConverter orderFileCmdConverter;
    private final OrderFileVOConverter orderFileVoConverter;

    private final OrderFileService orderFileService;

    /**
     * 根据业务单据id查询所有附件
     * <p>
     * 按文件类型分组
     *
     * @param cmd 查询条件
     * @return 文件
     */
    @PostMapping("/groupList")
    public R<List<OrderFileVO>> groupList(@Validated({QueryGroup.class}) @RequestBody OrderFileCmd cmd) {
        List<OrderFile> orderFileList = orderFileService.allTypeList(new TransportOrderId(cmd.getOrderId()), BusinessTypeEnum.valueOf(cmd.getBusinessType()));
        return R.success(orderFileVoConverter.groupByFileType(orderFileList));
    }

    /**
     * 新增附件
     *
     * @param cmd 附件信息
     * @return true/false
     */
    @PostMapping("/add")
    public R<Boolean> add(@Validated(value = {CreateGroup.class}) @RequestBody OrderFileCmd cmd) {
        OrderFile orderFile = orderFileCmdConverter.toDomain(cmd);

        return R.success(orderFileService.add(orderFile));
    }

    /**
     * 识别海关税单号
     *
     * @param cmd 文件信息
     * @return 海关税单号
     */
    @PostMapping("/recognize/customsTaxReceiptNo")
    public R<OrderFileInfoVO> recognizeCustomsTaxReceipt(@Validated(value = {OtherGroup.class}) @RequestBody OrderFileInfoCmd cmd) {
        OrderFileInfo result = orderFileService.recognizeCustomsTaxReceipt(orderFileCmdConverter.toInfoDomain(cmd));
        return R.success(orderFileVoConverter.toInfoVo(result));
    }


    /**
     * 删除附件
     *
     * @param fileId 文件id
     * @return true/false
     */
    @DeleteMapping("/delete")
    public R<Boolean> delete(@Valid @NotBlank @RequestParam String fileId) {
        FileId id = new FileId(fileId);
        return R.success(orderFileService.delete(id));
    }

}
