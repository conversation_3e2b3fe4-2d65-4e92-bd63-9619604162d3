package com.renpho.erp.tms.adapter.web.controller.orderfile.vo.converter;

import com.renpho.erp.tms.adapter.web.controller.orderfile.vo.OrderFileInfoVO;
import com.renpho.erp.tms.adapter.web.controller.orderfile.vo.OrderFileVO;
import com.renpho.erp.tms.domain.file.FileInfo;
import com.renpho.erp.tms.domain.orderfile.FileTypeEnum;
import com.renpho.erp.tms.domain.orderfile.OrderFile;
import com.renpho.erp.tms.domain.orderfile.OrderFileInfo;
import org.mapstruct.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/6/11
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE,
        componentModel = MappingConstants.ComponentModel.SPRING)
public interface OrderFileVOConverter {

    @Named("groupByFileType")
    default List<OrderFileVO> groupByFileType(List<OrderFile> files) {
        if (files == null || files.isEmpty()) {
            return Collections.emptyList();
        }
        List<OrderFileVO> orderFileVoList = files.stream()
                .collect(Collectors.groupingBy(OrderFile::getFileType))
                .entrySet().stream()
                .map(entry -> {
                    OrderFileVO vo = new OrderFileVO();
                    vo.setFileType(entry.getKey().getCode());

                    List<OrderFileInfoVO> fileInfos = entry.getValue().stream()
                            .map(file -> {
                                FileInfo info = file.getFileInfo();
                                if (info == null) return null;
                                OrderFileInfoVO infoVO = new OrderFileInfoVO();
                                infoVO.setFileId(info.getId().id());
                                infoVO.setFilename(info.getFilename());
                                infoVO.setOriginalFilename(info.getOriginalFilename());
                                infoVO.setFileUrl(info.getPresignedUrl());
                                infoVO.setUploadTime(file.getCreated().getOperateTime());
                                return infoVO;
                            })
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());

                    vo.setFileInfos(fileInfos);
                    return vo;
                })
                .collect(Collectors.toList());
        // 按照枚举排序
        FileTypeEnum[] enumValues = FileTypeEnum.values();
        Map<String, Integer> fileTypeOrderMap = new HashMap<>(enumValues.length);
        for (int i = 0; i < enumValues.length; i++) {
            fileTypeOrderMap.put(enumValues[i].name(), i);
        }
        orderFileVoList.sort(Comparator.comparingInt(vo ->
                fileTypeOrderMap.getOrDefault(vo.getFileType(), Integer.MAX_VALUE)));
        return orderFileVoList;
    }

    @Mapping(target = "fileId", source = "fileId.id")
    OrderFileInfoVO toInfoVo(OrderFileInfo info);

    List<OrderFileInfoVO> toInfoVo(List<OrderFileInfo> list);
}
