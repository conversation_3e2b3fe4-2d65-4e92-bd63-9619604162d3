package com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * VO for {@link com.renpho.erp.tms.domain.shippingfeeformula.ShippingFeeFormulaArg}
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShippingFeeFormulaArgVo implements VO, Serializable {

    @Serial
    private static final long serialVersionUID = -4522893898499797716L;

    private Integer id;

    /**
     * 计费项参数名称
     */
    private String name;

    /**
     * 计费项名称，如基础运费、报关费
     */
    private String argName;

    /**
     * 单位，字典: UNIT_OF_CHARGE_ITEM
     */
    @Trans(type = TransType.DICTIONARY, key = "UNIT_OF_CHARGE_ITEM", ref = "unitName")
    private String unit;

    /**
     * 单位
     */
    private String unitName;

}