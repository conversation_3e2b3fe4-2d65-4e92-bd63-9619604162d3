package com.renpho.erp.tms.adapter.web.controller.command.validator;

import com.renpho.erp.tms.adapter.web.controller.command.container.FileIdsContainer;
import com.renpho.erp.tms.infrastructure.remote.file.RemoteFileFeign;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.annotation.Annotation;
import java.util.Collection;
import java.util.Optional;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @since 2025/5/28
 */
public abstract class FileValidator<A extends Annotation, T> implements ConstraintValidator<A, T> {

    protected Predicate<T> predicate;

    @Resource
    protected RemoteFileFeign remoteFileFeign;

    @Override
    public boolean isValid(T value, ConstraintValidatorContext context) {
        return remoteFileFeign == null || predicate.test(value);
    }

    public static class IsFileExist extends FileValidator<FileAlreadyUpload, FileIdsContainer<Collection<String>, String>> {

        @Override
        public void initialize(FileAlreadyUpload constraintAnnotation) {
            predicate = cmd -> Optional.ofNullable(cmd)
                    .map(FileIdsContainer::getFileIds)
                    .filter(CollectionUtils::isNotEmpty)
                    .filter(ids -> StringUtils.isNoneBlank(ids.toArray(ids.toArray(new String[0]))))
                    .map(remoteFileFeign::getFileInfo)
                    .filter(MapUtils::isNotEmpty)
                    .isPresent();
        }
    }

}
