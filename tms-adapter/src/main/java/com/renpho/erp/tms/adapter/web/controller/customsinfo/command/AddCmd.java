package com.renpho.erp.tms.adapter.web.controller.customsinfo.command;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.renpho.erp.tms.adapter.web.controller.command.container.CurrencyCodeAndCountryCodeContainer;
import com.renpho.erp.tms.adapter.web.controller.command.container.ProductIdContainer;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.container.CountryCodeAndItemsContainer;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.container.HsCodeTaxRangeContainer;
import com.renpho.erp.tms.domain.currency.Currency;
import com.renpho.erp.tms.domain.product.Product;
import com.renpho.erp.tms.domain.regiontimezone.CountryTimeZone;
import com.renpho.erp.tms.domain.site.CountryRegion;
import com.renpho.karma.dto.Command;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Generated;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;


/**
 * <AUTHOR>
 * @since 2025/4/22
 */
@Getter
@Setter
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-04-22T12:09:54.*********+08:00[Asia/Shanghai]", comments = "Generator version: 7.12.0")
public class AddCmd extends Command implements Serializable, ProductIdContainer, CurrencyCodeAndCountryCodeContainer, HsCodeTaxRangeContainer, CountryCodeAndItemsContainer<List<AdditionalDutyCmd>, AdditionalDutyCmd> {

    @Serial
    private static final long serialVersionUID = 7434043117002833176L;

    /**
     * 产品 ID
     */
    @Schema(name = "productId", description = "产品 ID")
    @NotNull(message = "PSKU {jakarta.validation.constraints.NotBlank.message}")
    private Integer productId;

    @JsonIgnore
    private Product product;

    /**
     * 国家编码
     */
    @Schema(name = "countryCode", description = "国家编码")
    @NotBlank(message = "{excel.country_code} {jakarta.validation.constraints.NotBlank.message}")
    private String countryCode;

    @JsonIgnore
    private CountryRegion countryRegion;

    @JsonIgnore
    private CountryTimeZone countryTimeZone;

    /**
     * 海关编码-加税, 数字或者数字+“.”, 与减税海关编码校验二者至少有一个必填
     */
    @Schema(name = "hsCodeAddTax", description = "海关编码-加税, 数字或者数字+“.”, 与减税海关编码校验二者至少有一个必填")
    @Size(max = 20, message = "{excel.hs_code_added_tax} {jakarta.validation.constraints.Size.message}")
    private String hsCodeAddTax;

    /**
     * 海关编码-减税, 数字或者数字+“.”, 与减税海关编码校验二者至少有一个必填
     */
    @Schema(name = "hsCodeReducedTax", description = "海关编码-减税, 数字或者数字+“.”, 与减税海关编码校验二者至少有一个必填")
    @Size(max = 20, message = "{excel.hs_code_reduced_tax} {jakarta.validation.constraints.Size.message}")
    private String hsCodeReducedTax;

    /**
     * 币种, 国家为 CN 的非必填, 非 CN 的必填, 默认 USD
     */
    @Schema(name = "currencyCode", description = "币种, 国家为 CN 的非必填, 非 CN 的必填, 默认 USD")
    private String currencyCode;

    @JsonIgnore
    private Currency currency;

    /**
     * 其他, 国家为 CN 的非必填, 非 CN 的必填, 默认 100
     */
    @Schema(name = "others", description = "其他, 国家为 CN 的非必填, 非 CN 的必填, 默认 100")
    @Valid
    @Positive(message = "{excel.others} {jakarta.validation.constraints.Positive.message}")
    private BigDecimal others;

    /**
     * 状态, 字典: status_dict
     */
    @Schema(name = "status", description = "状态, 字典: status_dict")
    @NotNull(message = "{excel.status} {jakarta.validation.constraints.NotBlank.message}")
    private Integer status;

    /**
     * 基本税率, 国家为 CN 的非必填, 非 CN 的必填, 默认 100
     */
    @Schema(name = "baseDutyRate", description = "基本税率, 国家为 CN 的非必填, 非 CN 的必填, 默认 100")
    @Valid
    @PositiveOrZero(message = "{excel.base_duty_rate} {jakarta.validation.constraints.PositiveOrZero.message}")
    private BigDecimal baseDutyRate;

    /**
     * GST（商品服务税）, 国家为 CN 的非必填, 非 CN 的必填, 默认 100
     */
    @Schema(name = "gst", description = "GST（商品服务税）, 国家为 CN 的非必填, 非 CN 的必填, 默认 100")
    @Valid
    @PositiveOrZero(message = "GST {jakarta.validation.constraints.PositiveOrZero.message}")
    private BigDecimal gst;

    /**
     * VAT（增值税）, 国家为 CN 的非必填, 非 CN 的必填, 默认 100
     */
    @Schema(name = "vat", description = "VAT（增值税）, 国家为 CN 的非必填, 非 CN 的必填, 默认 100")
    @Valid
    @PositiveOrZero(message = "VAT {jakarta.validation.constraints.PositiveOrZero.message}")
    private BigDecimal vat;

    /**
     * 备注, 限制200个字符
     */
    @Schema(name = "remark", description = "备注, 限制200个字符")
    @Size(max = 200, message = "{excel.remark} {jakarta.validation.constraints.Size.message}")
    private String remark;

    /**
     * 附加关税
     */
    @Valid
    @Schema(name = "additionalDuties", description = "附加关税")
    private List<@Valid AdditionalDutyCmd> additionalDuties;

    /**
     * 附加关税
     */
    @Override
    @JsonIgnore
    @Schema(name = "additionalDuties", description = "附加关税", hidden = true)
    public List<AdditionalDutyCmd> getItems() {
        return this.additionalDuties;
    }

    @AssertTrue(message = "{error.base_duty_rate_not_null_except_cn}")
    @SuppressWarnings("unused")
    public boolean isBaseDutyRateValid() {
        return StringUtils.equals("CN", this.countryCode) || this.baseDutyRate != null;
    }

    @AssertTrue(message = "{error.others_not_null_except_cn}")
    @SuppressWarnings("unused")
    public boolean isOthersValid() {
        return StringUtils.equals("CN", this.countryCode) || this.others != null;
    }

    @AssertTrue(message = "{error.gst_not_null_except_cn}")
    @SuppressWarnings("unused")
    public boolean isGstValid() {
        return StringUtils.equals("CN", this.countryCode) || this.gst != null;
    }

    @AssertTrue(message = "{error.vat_not_null_except_cn}")
    @SuppressWarnings("unused")
    public boolean isVatValid() {
        return StringUtils.equals("CN", this.countryCode) || this.vat != null;
    }

    @AssertTrue(message = "{error.at_least_1_hs_code}")
    @SuppressWarnings("unused")
    public boolean isContainsAtLeastOneHsCode() {
        return !StringUtils.isAllBlank(this.hsCodeAddTax, this.hsCodeReducedTax);
    }

    @AssertTrue(message = "{excel.hs_code_added_tax} {error.hs_code_pattern}")
    @SuppressWarnings("unused")
    public boolean isHsCodeAddTaxValid() {
        Optional<String> optional = Optional.ofNullable(hsCodeAddTax).filter(StringUtils::isNotBlank);
        if (optional.isEmpty()) {
            return true;
        }
        return optional.filter(s -> s.matches("^[0-9]+(\\.[0-9]+)*$")).isPresent();
    }

    @AssertTrue(message = "{excel.hs_code_reduced_tax} {error.hs_code_pattern}")
    @SuppressWarnings("unused")
    public boolean isHsCodeReducedTaxValid() {
        Optional<String> optional = Optional.ofNullable(hsCodeReducedTax).filter(StringUtils::isNotBlank);
        if (optional.isEmpty()) {
            return true;
        }
        return optional.filter(s -> s.matches("^[0-9]+(\\.[0-9]+)*$")).isPresent();
    }

}
