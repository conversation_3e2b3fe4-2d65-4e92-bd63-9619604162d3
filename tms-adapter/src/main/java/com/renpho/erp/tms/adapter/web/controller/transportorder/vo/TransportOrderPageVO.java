package com.renpho.erp.tms.adapter.web.controller.transportorder.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.renpho.erp.tms.adapter.web.controller.transportrequest.vo.TransportRequestTrackingVO;
import com.renpho.erp.tms.domain.transportorder.dto.TransportOrderFileCheckListResponse;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/10
 */
@Data
public class TransportOrderPageVO implements VO, Serializable {

    /**
     * TO单ID
     */
    private Integer id;

    /**
     * TO单号
     */
    private String toNo;

    /**
     * TO单状态
     */
    @Trans(type = TransType.DICTIONARY, key = "TO_STATUS", ref = "statusName")
    private Integer status;

    /**
     * TO单状态名称
     */
    private String statusName;


    /**
     * 质检结果
     */
    @Trans(type = TransType.DICTIONARY, key = "quality_task_result", ref = "qcResultName")
    private Integer qcResult;

    /**
     * 质检结果名称
     */
    private String qcResultName;

    private List<TrQcResultVO> qcResults;

    /**
     * 审批信息
     */
    private List<TransportOrderApprovalVO> approvalVOS;


    /**
     * 货代公司ID，取SRM的物流供应商的启用的物流商id
     */
    private Integer forwarderCompanyId;

    /**
     * 货代公司Code，取SRM的物流供应商的启用的物流商code
     */
    private String forwarderCompanyCode;

    /**
     * 货代公司
     */
    private String forwarderCompany;

    /**
     * 运单号
     */
    private String trackingNo;

    /**
     * 剩余运单数量
     */
    private Integer remainingCnt;

    /**
     * 运单信息（TR）
     */
    private List<TransportRequestTrackingVO> trackingItems;

    /**
     * 审批状态，字典类型：TO_APPROVAL_STATUS。取值如下：不需审批、审批中、审批通过、审批不通过【NO_APPROVAL_NEEDED  、AUDITING、APPROVED、REJECTED】
     */
    private String approvalStatus;


    /**
     * 头程方式 ID
     */
    private String firstLegModeId;

    /**
     * 头程方式
     */
    private String firstLegModeName;

    /**
     * 柜号
     */
    private String containerNo;

    /**
     * 服务商类型字典值, 字典: logistics_type
     */
    @Trans(type = TransType.DICTIONARY, key = "logistics_type", ref = "carrierTypeName")
    private String carrierType;

    /**
     * 服务商类型
     */
    private String carrierTypeName;

    /**
     * 业务类型
     */
    private String businessType;


    /**
     * 发运与入库字典值, 字典: ship_warehouse_type
     */
    private List<ShipWarehouseMethodVO> shipWarehouseMethods;

    /**
     * 起运港字典值, 字典: trade_terms_ship_to
     */
    private String shippingPort;

    /**
     * 起运港
     */
    private String shippingPortName;

    /**
     * 目的国/地区  国家(地区)
     */
    private String destCountry;

    /**
     * 总数量
     */
    private Integer totalQty;

    /**
     * 总箱数
     */
    private BigDecimal totalBoxQty;

    /**
     * 总毛重 kg
     */
    private BigDecimal totalGrossWeight;

    /**
     * 总体积 m³
     */
    private BigDecimal totalVolume;

    /**
     * 预计离港时间
     */
    private LocalDate estimatedDepartureDate;

    /**
     * 预计到港时间
     */
    private LocalDate estimatedArrivalDate;

    /**
     * 批注
     */
    private String comment;


    /**
     * 创建人 ID
     */
    private Integer createBy;

    /**
     * 创建人名称
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人 ID
     */
    private Integer updateBy;

    /**
     * 更新人名称
     */
    private String updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 异常提醒信息
     */
    private TransportOrderFileCheckListResponse checkReminder;

    @Getter
    @Setter
    public static class TrQcResultVO implements VO {
        @JsonIgnore
        private Integer id;
        /**
         * TR单号
         */
        private String trNo;

        /**
         * 质检结果
         */
        @Trans(type = TransType.DICTIONARY, key = "quality_task_result", ref = "qcResultName")
        private Integer qcResult;

        /**
         * 质检结果名称
         */
        private String qcResultName;

        /**
         * QC单ID
         */
        private Integer qcId;
    }
}
