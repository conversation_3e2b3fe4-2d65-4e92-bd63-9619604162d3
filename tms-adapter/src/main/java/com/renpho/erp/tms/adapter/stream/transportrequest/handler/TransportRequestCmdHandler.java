package com.renpho.erp.tms.adapter.stream.transportrequest.handler;

import com.renpho.erp.log.repository.annotation.LogRecord;
import com.renpho.erp.log.repository.support.LogRecordContextHolder;
import com.renpho.erp.pms.model.shipmentplan.model.ShipmentPlanOrderDTO;
import com.renpho.erp.qms.task.QualityControllerTaskDTO;
import com.renpho.erp.tms.adapter.stream.transportrequest.CancelShipmentOrderProducer;
import com.renpho.erp.tms.adapter.stream.transportrequest.cmd.AddTransportRequestCmd;
import com.renpho.erp.tms.adapter.stream.transportrequest.cmd.converter.TransportRequestCmdStreamConverter;
import com.renpho.erp.tms.adapter.web.controller.transportrequest.vo.TransportRequestDetailVO;
import com.renpho.erp.tms.adapter.web.controller.transportrequest.vo.converter.TransportRequestVOConverter;
import com.renpho.erp.tms.application.firstleg.FirstLegModeService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.application.transportrequest.TransportRequestQueryService;
import com.renpho.erp.tms.application.transportrequest.TransportRequestService;
import com.renpho.erp.tms.application.transportrequest.stream.TransportRequestProducer;
import com.renpho.erp.tms.domain.exception.DingTalkWarning;
import com.renpho.erp.tms.domain.firstleg.mode.FirstLegMode;
import com.renpho.erp.tms.domain.inbound.WarehouseType;
import com.renpho.erp.tms.domain.owner.Owner;
import com.renpho.erp.tms.domain.product.Product;
import com.renpho.erp.tms.domain.saleschannel.SalesChannel;
import com.renpho.erp.tms.domain.saleschannel.SalesChannelConstant;
import com.renpho.erp.tms.domain.site.CountryRegion;
import com.renpho.erp.tms.domain.store.Store;
import com.renpho.erp.tms.domain.supplier.PurchaseSupplier;
import com.renpho.erp.tms.domain.transportrequest.QcResult;
import com.renpho.erp.tms.domain.transportrequest.TransportRequest;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestId;
import com.renpho.erp.tms.domain.transportrequest.UpdateTransportRequestCmd;
import com.renpho.erp.tms.domain.warehouse.Warehouse;
import com.renpho.erp.tms.domain.warehouse.WarehouseAddress;
import com.renpho.erp.tms.domain.warehouse.WarehouseId;
import com.renpho.erp.tms.infrastructure.common.oplog.LogModule;
import com.renpho.erp.tms.infrastructure.errorhandler.SendDingTalkWhenError;
import com.renpho.erp.tms.infrastructure.remote.countryregion.repository.CountryRegionLookup;
import com.renpho.erp.tms.infrastructure.remote.owner.repository.OwnerLookup;
import com.renpho.erp.tms.infrastructure.remote.product.repository.ProductLookup;
import com.renpho.erp.tms.infrastructure.remote.purchasesupplier.PurchaseSupplierLookup;
import com.renpho.erp.tms.infrastructure.remote.saleschannel.repository.SalesChannelLookup;
import com.renpho.erp.tms.infrastructure.remote.store.repository.StoreLookup;
import com.renpho.erp.tms.infrastructure.remote.warehouse.repository.WarehouseLookup;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Path;
import jakarta.validation.Validator;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jmolecules.architecture.cqrs.CommandHandler;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/27
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TransportRequestCmdHandler {

    private final Validator validator;
    private final TransportRequestCmdStreamConverter transportRequestCmdStreamConverter;
    private final TransportRequestQueryService transportRequestQueryService;
    private final TransportRequestService transportRequestService;
    private final FirstLegModeService firstLegModeService;
    private final StoreLookup storeLookup;
    private final SalesChannelLookup salesChannelLookup;
    private final PurchaseSupplierLookup purchaseSupplierLookup;
    private final OwnerLookup ownerLookup;
    private final WarehouseLookup warehouseLookup;
    private final CountryRegionLookup countryRegionLookup;
    private final ProductLookup productLookup;
    private final TransportRequestVOConverter transportRequestVOConverter;
    private final TransportRequestProducer transportRequestProducer;
    private final PushTaskService pushTaskService;
    private final CancelShipmentOrderProducer cancelShipmentOrderProducer;

    @CommandHandler
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(module = LogModule.TRANSPORT_REQUEST, type = LogModule.CommonDesc.INSERT_OPERATOR, desc = LogModule.CommonDesc.INSERT_DESC)
    @SendDingTalkWhenError
    public void add(ShipmentPlanOrderDTO pd) {
        // PD确认
        AddTransportRequestCmd cmd = transportRequestCmdStreamConverter.toAddCmd(pd);
        // 入参校验
        Set<ConstraintViolation<AddTransportRequestCmd>> violations = validator.validate(cmd);
        if (CollectionUtils.isNotEmpty(violations)) {
            pushTaskService.createTrFailed(cmd, "PD单号: [%s] 生成 TR 单失败".formatted(cmd.getPdNo()));
            ConstraintViolationException e = new ConstraintViolationException(violations.stream().map(v -> Optional.ofNullable(v.getPropertyPath()).map(Path::toString).orElse(StringUtils.EMPTY) + v.getMessage()).collect(Collectors.joining(", ")), violations);
            throw new DingTalkWarning(e, "INVALID_REQUEST_CONTENT_PLACEHOLDER", "pdNo=" + cmd.getPdNo());
        }
        Optional<TransportRequest> exist = transportRequestQueryService.findByPdNo(cmd.getPdNo());
        if (exist.isPresent()) {
            pushTaskService.createTrFailed(cmd, "PD单号: [%s] 已经生成过 TR 单: [%s]".formatted(cmd.getPdNo(), exist.get().getTrNo()));
            throw new DingTalkWarning("data.exist", "pdNo=" + cmd.getPdNo());
        }
        validateStore(cmd);
        validateSalesChannel(cmd);
        validateProduct(cmd);
        validatePurchaseSupplier(cmd);
        validateOwner(cmd);
        validateShippingWarehouse(cmd);
        validateCountryRegion(cmd);
        validateDestWarehouse(cmd);
        validateDestWarehouseCode(cmd);
        // TODO 根据 PD 单头程方式查询
        Optional<FirstLegMode> firstLegMode = firstLegModeService.findAll(new FirstLegMode(null)).stream().findFirst();
        if (firstLegMode.isEmpty()) {
            String errMsg = "PD单号: [%s] 生成 TR 单失败, 没有可用的头程方式".formatted(cmd.getPdNo());
            pushTaskService.createTrFailed(cmd, errMsg);
            throw new DingTalkWarning("error.first-leg-mode.non-available.placeholder", errMsg);
        }
        cmd.setFirstLegMode(firstLegMode.get());
        // 校验通过, 准备写入数据库
        TransportRequest command = transportRequestCmdStreamConverter.toDomain(cmd);
        TransportRequestId trId;
        try {
            trId = transportRequestService.add(command);
        } catch (Exception e) {
            String errMsg = "PD单号: [%s] 生成 TR 单失败, 写入数据库失败".formatted(cmd.getPdNo());
            pushTaskService.createTrFailed(cmd, errMsg);
            throw new DingTalkWarning(e, "save.is.fail.placeholder", errMsg);
        }
        TransportRequest domain = transportRequestQueryService.findDetailById(trId);
        pushTaskService.init(domain);
        transportRequestProducer.sendTrNo(domain);

        TransportRequestDetailVO vo = transportRequestVOConverter.toDetailVO(domain);
        LogRecordContextHolder.putRecordData(String.valueOf(vo.getId()), null, vo);
    }

    /**
     * 校验目的仓code是否存在
     *
     * <AUTHOR>
     * @since 2025/5/29
     */
    private void validateDestWarehouseCode(AddTransportRequestCmd cmd) {
        Optional<WarehouseAddress> warehouseAddress = warehouseLookup.findWarehouseAddress(cmd.getDestWarehouseId(), cmd.getDestWarehouseCode());
        if (warehouseAddress.isEmpty()) {
            pushTaskService.createTrFailed(cmd, "PD单号: [%s] 生成 TR 单失败, 目的仓地址不存在, 目的仓 id: [%s], 目的仓code: [%s]".formatted(cmd.getPdNo(), cmd.getDestWarehouseId(), cmd.getDestWarehouseCode()));
            throw new DingTalkWarning("INVALID_REQUEST_CONTENT_PLACEHOLDER", "DestWarehouseId=" + cmd.getDestWarehouseId() + "DestWarehouseCode=" + cmd.getDestWarehouseCode());
        }
        cmd.getDestWarehouse().setFullAddress(warehouseAddress.get());
    }

    /**
     * 校验目的仓是否存在
     *
     * <AUTHOR>
     * @since 2025/5/29
     */
    private void validateDestWarehouse(AddTransportRequestCmd cmd) {
        Optional<Warehouse> optional = warehouseLookup.findById(cmd.getDestWarehouseId())
                // 目的仓状态异常
                .filter(w -> w.getStatus() == 1)
                // 目的仓审核状态异常
                .filter(w -> w.getAuditStatus() == 2);
        if (optional.isEmpty()) {
            pushTaskService.createTrFailed(cmd, "PD单号: [%s] 生成 TR 单失败, 目的仓不存在或未启用或未审核通过".formatted(cmd.getPdNo()));
            throw new DingTalkWarning("dest-warehouse.not.exist.placeholder", "DestWarehouseId=" + cmd.getDestWarehouseId());
        }
        Warehouse warehouse = optional.get();
        // 平台仓 shipmentId、referenceId 和箱唛必填
        if (Objects.equals(warehouse.getType(), WarehouseType.PLATFORM.getDictItem())) {
            if (StringUtils.isAnyBlank(cmd.getShipmentId(), cmd.getReferenceId()) || CollectionUtils.isEmpty(cmd.getCartonLabelFileIds())) {
                pushTaskService.createTrFailed(cmd, "PD单号: [%s] 生成 TR 单失败, 目的仓为平台仓时, shipmentId和referenceId和箱唛不能为空, 目的仓 id: [%s], 目的仓code: [%s]".formatted(cmd.getPdNo(), cmd.getDestWarehouseId(), cmd.getDestWarehouseCode()));
                throw new DingTalkWarning("error.tr.both-shipment-id-and-carton-label-id-not-null-when-platform-warehouse");
            }
        }
        cmd.setDestWarehouse(warehouse);
    }

    /**
     * 校验目的国是否存在
     *
     * <AUTHOR>
     * @since 2025/5/29
     */
    private void validateCountryRegion(AddTransportRequestCmd cmd) {
        Optional<CountryRegion> optional = countryRegionLookup.findByCode(cmd.getDestCountryCode()).filter(c -> c.getStatus() == 1);
        if (optional.isEmpty()) {
            pushTaskService.createTrFailed(cmd, "PD单号: [%s] 生成 TR 单失败, 目的国不存在或未启用".formatted(cmd.getPdNo()));
            throw new DingTalkWarning("country-region.not.exist.placeholder", "DestCountryCode=" + cmd.getDestCountryCode());
        }
        CountryRegion countryRegion = optional.get();
        cmd.setDestCountry(countryRegion);
    }

    /**
     * 校验发货仓是否存在
     *
     * <AUTHOR>
     * @since 2025/5/29
     */
    private void validateShippingWarehouse(AddTransportRequestCmd cmd) {
        Optional<Warehouse> hasShippingWarehouse = Optional.ofNullable(cmd.getShippingWarehouseId()).flatMap(warehouseLookup::findById);
        if (hasShippingWarehouse.isEmpty()) {
            Optional<Warehouse> optional = warehouseLookup.findByName("港口仓")
                    .stream()
                    .filter(w -> w.getStatus() == 1)
                    .filter(w -> w.getAuditStatus() == 2)
                    .findFirst();
            if (optional.isEmpty()) {
                pushTaskService.createTrFailed(cmd, "PD单号: [%s] 生成 TR 单失败, 发货仓ID: [%s] 或港口仓不存在".formatted(cmd.getPdNo(), Optional.ofNullable(cmd.getShippingWarehouseId()).map(WarehouseId::id).map(String::valueOf).orElse(StringUtils.EMPTY)));
                throw new DingTalkWarning("shipping-warehouse.not.exist.placeholder", "ShippingWarehouseId=" + cmd.getShippingWarehouseId());
            }
            Warehouse shippingWarehouse = optional.get();
            cmd.setShippingWarehouse(shippingWarehouse);
        } else {
            cmd.setShippingWarehouse(hasShippingWarehouse.get());
        }
    }

    /**
     * 校验平台
     *
     * <AUTHOR>
     * @since 2025/5/29
     */
    private void validateSalesChannel(AddTransportRequestCmd cmd) {
/*
        // 校验店铺关联平台与参数中平台是否相同
        if (!Objects.equals(store.getSalesChannelId(), cmd.getSalesChannelId())) {
            throw new DingTalkWarning("INVALID_REQUEST_CONTENT_PLACEHOLDER", "salesChannelId=" + cmd.getSalesChannelId());
        }
*/
        Optional<SalesChannel> optional = salesChannelLookup.findById(cmd.getSalesChannelId()).filter(sc -> sc.getStatus() == 1);
        if (optional.isEmpty()) {
            pushTaskService.createTrFailed(cmd, "PD单号: [%s] 生成 TR 单失败, 平台不存在".formatted(cmd.getPdNo()));
            throw new DingTalkWarning("sales_channel.not.exist.placeholder", "salesChannelId=" + cmd.getSalesChannelId());
        }
        SalesChannel salesChannel = optional.get();
        cmd.setSalesChannel(salesChannel);
/*
        // 当平台为 Amazon 时, referenceId 必填
        if (salesChannel.getChannelCode().equals("AMZ")) {
            if (StringUtils.isBlank(cmd.getReferenceId())) {
                throw new DingTalkWarning("error.tr.reference-id-not-null-when-amazon", "salesChannel.code=AMZ, referenceId is blank");
            }
        }
*/
    }

    /**
     * 校验店铺是否存在
     *
     * <AUTHOR>
     * @since 2025/5/29
     */
    private void validateStore(AddTransportRequestCmd cmd) {
        Optional<Store> optional = storeLookup.findById(cmd.getStoreId())
                .flatMap(storeLookup::findStoreCompany)
                .filter(s -> s.getStatus() == 1);
        if (optional.isEmpty()) {
            pushTaskService.createTrFailed(cmd, "PD单号: [%s] 生成 TR 单失败, 店铺不存在".formatted(cmd.getPdNo()));
            throw new DingTalkWarning("store.not.exist.placeholder", "storeId=" + cmd.getStoreId());
        }
        Store store = optional.get();
        cmd.setStore(store);
        cmd.setOwner(store.getActiveOwner());
    }

    /**
     * 校验产品是否存在
     *
     * <AUTHOR>
     * @since 2025/5/29
     */
    private void validateProduct(AddTransportRequestCmd cmd) {
        Optional<Product> hasProduct = productLookup.findById(cmd.getProductId());
        if (hasProduct.isEmpty()) {
            pushTaskService.createTrFailed(cmd, "PD单号: [%s] 生成 TR 单失败, 产品不存在".formatted(cmd.getPdNo()));
            throw new DingTalkWarning("INVALID_REQUEST_CONTENT_PLACEHOLDER", "productId=" + cmd.getProductId());
        }
        Product product = hasProduct.get();
        cmd.getProduct().setNames(product.getNames());
        // 使用 PD 单的总采购数量和装箱数量重新计算总箱数
        cmd.setBoxQty(new BigDecimal(cmd.getQuantity()).divide(new BigDecimal(cmd.getQuantityPerBox()), 0, RoundingMode.UP).intValue());
/*
        cmd.setProductId(product.getId());
        cmd.setProduct(product);
        // 校验产品价目信息是否包含参数中产品价目
        if (product.getEnabledProductPrice().getItems().stream().map(ProductPriceItem::getInclusiveTaxPrice).noneMatch(cmd.getPurchaseCost()::equals)) {
            throw new DingTalkWarning("INVALID_REQUEST_CONTENT_PLACEHOLDER", "purchaseCost=" + cmd.getPurchaseCost());
        }
        if (!StringUtils.equals(product.getEnabledProductPrice().getCurrencyType(), cmd.getPurchaseCostCurrencyCode())) {
            throw new DingTalkWarning("INVALID_REQUEST_CONTENT_PLACEHOLDER", "purchaseCostCurrencyCode=" + cmd.getPurchaseCostCurrencyCode());
        }
        // 校验产品箱规是否包含参数中箱规
        if (product.getBoxSpecs().stream().noneMatch(b ->
                // 外箱尺寸-高
                Objects.equals(b.getBoxHeightMetric(), cmd.getBoxHeightMetric())
                // 外箱尺寸-长
                && Objects.equals(b.getBoxLengthMetric(), cmd.getBoxLengthMetric())
                // 外箱尺寸-宽
                && Objects.equals(b.getBoxWidthMetric(), cmd.getBoxWidthMetric())
                // 装箱数量
                && Objects.equals(b.getNumberOfUnitsPerBox(), cmd.getQuantityPerBox())
                // 装箱毛重
                && Objects.equals(b.getGrossWeightPerBoxMetric(), cmd.getBoxGrossWeight())
        )) {
            throw new DingTalkWarning("INVALID_REQUEST_CONTENT_PLACEHOLDER", "箱规=(QuantityPerBox=" + cmd.getQuantityPerBox() + ", BoxHeightMetric=" + cmd.getBoxHeightMetric() + ", BoxLengthMetric=" + cmd.getBoxLengthMetric() + ", BoxWidthMetric=" + cmd.getBoxWidthMetric() + ", BoxGrossWeight=" + cmd.getBoxGrossWeight());
        }
*/
    }

    /**
     * 采购公司主体是否存在
     *
     * <AUTHOR>
     * @since 2025/5/29
     */
    private void validateOwner(AddTransportRequestCmd cmd) {
        Optional<Owner> optional = ownerLookup.findById(cmd.getPurchaserOwnerId());
        if (optional.isEmpty()) {
            pushTaskService.createTrFailed(cmd, "PD单号: [%s] 生成 TR 单失败, 采购公司主体不存在".formatted(cmd.getPdNo()));
            throw new DingTalkWarning("INVALID_REQUEST_CONTENT_PLACEHOLDER", "PurchaserOwnerId=" + cmd.getPurchaserOwnerId());
        }
        Owner owner = optional.get();
        cmd.setPurchaserOwner(owner);
    }

    /**
     * 校验供应商是否存在
     *
     * <AUTHOR>
     * @since 2025/5/29
     */
    private void validatePurchaseSupplier(AddTransportRequestCmd cmd) {
        Optional<PurchaseSupplier> optional = purchaseSupplierLookup.findById(cmd.getPurchaseSupplierId());
        if (optional.isEmpty()) {
            pushTaskService.createTrFailed(cmd, "PD单号: [%s] 生成 TR 单失败, 供应商不存在".formatted(cmd.getPdNo()));
            throw new DingTalkWarning("purchase-supplier.not.exist", "PurchaseSupplierId=" + cmd.getPurchaseSupplierId());
        }
        PurchaseSupplier purchaseSupplier = optional.get();
        cmd.setPurchaseSupplier(purchaseSupplier);
    }

    /**
     * PD更新
     *
     * <AUTHOR>
     * @since 2025/5/29
     */
    @CommandHandler
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(module = LogModule.TRANSPORT_REQUEST, type = LogModule.CommonDesc.UPLOAD_OPERATOR, desc = LogModule.CommonDesc.UPLOAD_DESC)
    @SendDingTalkWhenError
    public void update(ShipmentPlanOrderDTO pd) {
        UpdateTransportRequestCmd cmd = transportRequestCmdStreamConverter.toUpdateCmd(pd);
        Set<ConstraintViolation<UpdateTransportRequestCmd>> violations = validator.validate(cmd);
        if (CollectionUtils.isNotEmpty(violations)) {
            ConstraintViolationException e = new ConstraintViolationException(violations.stream().map(ConstraintViolation::getMessage).collect(Collectors.joining(", ")), violations);
            throw new DingTalkWarning(e, "INVALID_REQUEST_CONTENT_PLACEHOLDER", "pdNo=" + cmd.getPdNo());
        }
        log.info("PD 更新 TR 单: pdNo=[{}]", cmd.getPdNo());
        TransportRequest domain = transportRequestQueryService.findByPdNo(cmd.getPdNo()).orElseThrow(() -> new DingTalkWarning("DATA_DOES_NOT_EXIST", "pdNo=" + cmd.getPdNo()));
        TransportRequest exist = transportRequestQueryService.findDetailById(domain.getId());
        TransportRequest command = transportRequestCmdStreamConverter.updateDomain(cmd, domain);
        TransportRequestId trId = transportRequestService.updateByPd(command);
        TransportRequestDetailVO before = transportRequestVOConverter.toDetailVO(exist);
        TransportRequestDetailVO after = transportRequestVOConverter.toDetailVO(transportRequestQueryService.findDetailById(trId));
        LogRecordContextHolder.putRecordData(String.valueOf(after.getId()), before, after);
    }

    /**
     * PD取消
     *
     * <AUTHOR>
     * @since 2025/5/29
     */
    @CommandHandler
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(module = LogModule.TRANSPORT_REQUEST, type = LogModule.CommonDesc.CANCEL_OPERATOR, desc = LogModule.CommonDesc.CANCEL_DESC)
    @SendDingTalkWhenError
    public void cancel(ShipmentPlanOrderDTO pd) {
        String pdNo = pd.getPdNo();
        if (StringUtils.isBlank(pdNo)) {
            throw new DingTalkWarning("INVALID_REQUEST_CONTENT_PLACEHOLDER", "pdNo is blank");
        }
        log.info("PD 取消 TR 单: pdNo=[{}]", pdNo);
        TransportRequest domain = transportRequestQueryService.findByPdNo(pdNo).orElseThrow(() -> new DingTalkWarning("DATA_DOES_NOT_EXIST_PLACEHOLDER", "pdNo=" + pdNo));
        TransportRequest exist = transportRequestQueryService.findDetailById(domain.getId());
        TransportRequestId trId = transportRequestService.cancelByPd(domain.getId());
        TransportRequestDetailVO before = transportRequestVOConverter.toDetailVO(exist);
        TransportRequestDetailVO after = transportRequestVOConverter.toDetailVO(transportRequestQueryService.findDetailById(trId));
        LogRecordContextHolder.putRecordData(String.valueOf(after.getId()), before, after);
        log.info("PD 取消 TR 单成功: pdNo=[{}]", pdNo);

        //取消入库单，异步
        if (StringUtil.isNotBlank(exist.getShipmentId())) {
            boolean isAmazon = SalesChannelConstant.AMAZON.equalsIgnoreCase(exist.getSalesChannel().getChannelName());
            cancelShipmentOrderProducer.cancelShipmentOrder(exist.getTrId(),
                    exist.getShipmentId(),
                    exist.findWarehouseProviderType(),
                    isAmazon);
        }

    }

    @CommandHandler
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(module = LogModule.TRANSPORT_REQUEST, type = LogModule.CommonDesc.UPLOAD_OPERATOR, desc = LogModule.CommonDesc.UPLOAD_DESC)
    @SendDingTalkWhenError
    public void updateQcResult(QualityControllerTaskDTO qc, QcResult result) {
        String pdNo = qc.getPdNo();
        log.info("PD 更新 TR 单质检结果: pdNo=[{}]", pdNo);
        if (StringUtils.isBlank(pdNo)) {
            throw new DingTalkWarning("INVALID_REQUEST_CONTENT_PLACEHOLDER", "pdNo is blank");
        }
        TransportRequest domain = transportRequestQueryService.findByPdNo(pdNo).orElseThrow(() -> new DingTalkWarning("DATA_DOES_NOT_EXIST_PLACEHOLDER", "pdNo=" + pdNo));
        TransportRequest exist = transportRequestQueryService.findDetailById(domain.getId());
        transportRequestCmdStreamConverter.updateQcResult(domain, qc.getId(), qc.getQcNo(), result);
        TransportRequestId trId = transportRequestService.updateQcResult(domain);
        TransportRequestDetailVO before = transportRequestVOConverter.toDetailVO(exist);
        TransportRequestDetailVO after = transportRequestVOConverter.toDetailVO(transportRequestQueryService.findDetailById(trId));
        LogRecordContextHolder.putRecordData(String.valueOf(after.getId()), before, after);
        log.info("PD 更新 TR 单质检结果成功: pdNo=[{}]", pdNo);
    }
}
