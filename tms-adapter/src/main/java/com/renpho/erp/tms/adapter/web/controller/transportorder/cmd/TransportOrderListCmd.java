package com.renpho.erp.tms.adapter.web.controller.transportorder.cmd;

import com.renpho.karma.dto.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/6/10
 */
@Getter
@Setter
public class TransportOrderListCmd extends PageQuery{

    /**
     * TO单号
     */
    @Schema(name = "toNo", description = "TO单号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String toNo;

    /**
     * psku
     */
    @Schema(name = "psku", description = "psku", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String psku;
    /**
     * 创建人
     */
    @Schema(name = "createdBy", description = "创建人", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Set<Integer> createdBy;

    /**
     * 国家地区编码数组
     */
    @Schema(name = "countryCodes", description = "国家地区编码数组", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Set<String> countryCodes;

    /**
     * 头程方式ID数组
     */
    @Schema(name = "firstLegModes", description = "头程方式ID数组", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Set<Integer> firstLegModes;

    /**
     * 柜号
     */
    @Schema(name = "containerNo", description = "柜号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String containerNo;

    /**
     * 运单号
     */
    private String trackingNo;

    /**
     * 目的仓 ID 数组, IMS 仓库主数据
     */
    @Schema(name = "destWarehouseIds", description = "目的仓 ID 数组, IMS 仓库主数据", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Set<String> destWarehouseIds;

    /**
     * 起运港数组, 字典: trade_terms_ship_to
     */
    @Schema(name = "shippingPorts", description = "起运港数组, 字典: trade_terms_ship_to", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Set<String> shippingPorts;

    /**
     * 服务商类型, 字典: logistics_type
     */
    @Schema(name = "carrierType", description = "服务商类型, 字典: logistics_type", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String carrierType;

    /**
     * 发运与入库, 字典: ship_warehouse_type
     */
    @Schema(name = "shipReceiveType", description = "发运与入库, 字典: ship_warehouse_type", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String shipReceiveType;

    /**
     * 平台 ID, MDM 销售渠道
     */
    @Schema(name = "salesChannelId", description = "平台 ID, MDM 销售渠道", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer salesChannelId;

    /**
     * 货主 ID, MDM 公司主体
     */
    @Schema(name = "ownerId", description = "货主 ID, MDM 公司主体", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer ownerId;

    /**
     * 预计离港时间（开始）
     */
    private LocalDate estimatedDepartureStartDate;

    /**
     * 预计离港时间（结束）
     */
    private LocalDate estimatedDepartureEndDate;

    /**
     * 预计到港时间（开始）
     */
    private LocalDate estimatedArrivalStartDate;

    /**
     * 预计到港时间（结束）
     */
    private LocalDate estimatedArrivalEndDate;

    /**
     * 业务类型, 字典: business_type
     */
    @Schema(name = "businessType", description = "业务类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String businessType;

    /**
     * 状态
     */
    @Schema(name = "status", description = "状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer status;

    /**
     * 出口查验 false:否 true:是，未查验时，exportInspection和importInspection都传false;
     */
    private Boolean exportInspection;

    /**
     * 进口查验 false:否 true:是，未查验时，exportInspection和importInspection都传false;
     */
    private Boolean importInspection;

    /**
     * 采购供应商CODE
     */
    private String purchaseSupplierCode;

    /**
     * 订舱号
     */
    private String bookingNo;

    /**
     * 批注
     */
    private String comment;

    /**
     * 头程类型
     */
    private String firstLegType;

    /**
     * 审批状态：不需审批、审批中、审批通过、审批不通过【No Approval Needed  、Auditing、Approved、Rejected】
     */
    private String approvalStatus;

    /**
     * TR单号
     */
    @Schema(name = "trNo", description = "TR单号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String trNo;

    /**
     * PO单号
     */
    @Schema(name = "poNo", description = "PO单号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String poNo;

    /**
     * PD单号
     */
    @Schema(name = "pdNo", description = "PD单号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pdNo;

    /**
     * 文件是否齐全
     */
    private Boolean docComplete;
}
