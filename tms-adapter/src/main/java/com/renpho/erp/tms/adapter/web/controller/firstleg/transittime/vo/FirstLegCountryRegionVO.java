package com.renpho.erp.tms.adapter.web.controller.firstleg.transittime.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/4/23
 */
@Data
public class FirstLegCountryRegionVO implements VO {
    private  Integer id;

    /**
     * 头程类型ID
     */
    private Integer modeId;

    /**
     * 头程方式
     */
    private String firstLegMode;

    /**
     * 国家(地区)编码
     */
    private String countryCode;

    /**
     * 国家(地区)
     */
    private String country;

    /**
     * 备注
     */
    private String remark;

    /**
     * 区域
     */
    private String area;

    /**
     * 最小时效(天)
     */
    private Integer minTransitTime;

    /**
     * 最大时效(天)
     */
    private Integer maxTransitTime;

    @Trans(type = TransType.DICTIONARY, key = "status_dict", ref = "statusName")
    private Integer status;

    /**
     * 状态
     */
    private String statusName;


    /**
     * 创建人 ID
     */
    private Integer createBy;

    /**
     * 创建人名称
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人 ID
     */
    private Integer updateBy;

    /**
     * 更新人名称
     */
    private String updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
