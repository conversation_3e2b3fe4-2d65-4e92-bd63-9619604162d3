package com.renpho.erp.tms.adapter.web.controller.transportorder.cmd;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.renpho.erp.tms.adapter.web.controller.command.container.LogisticsSupplierIdContainer;
import com.renpho.erp.tms.domain.supplier.LogisticsSupplier;
import com.renpho.erp.tms.domain.transportorderforwarderapproval.ShippingPriceType;
import com.renpho.karma.dto.Command;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/6/13
 */
@Getter
@Setter
public class ForwarderWithShippingFeeCmd extends Command implements LogisticsSupplierIdContainer {

    @Serial
    private static final long serialVersionUID = -2780493268437941535L;

    /**
     * 货代公司 ID(物流供应商 ID)
     */
    @NotNull
    private Integer logisticsSupplierId;

    @JsonIgnore
    private LogisticsSupplier logisticsSupplier;

    /**
     * 是否包含税
     */
    private String isIncludeTax;

    /**
     * 运费类型: 单价/总价
     */
    @NotNull
    private ShippingPriceType priceType;

    /**
     * 总价金额, 币种 CNY, 当 priceType = TOTAL 时必填
     */
    private BigDecimal totalAmount;

    /**
     * 运费公式返回值名称
     */
    private String returnName;

    /**
     * 运费公式 id, 当 priceType = UNIT 时必填
     */
    private Integer formulaId;

    /**
     * 运费公式变动项参数, 当 priceType = UNIT 时必填
     */
    private List<ArgCmd> args;

    /**
     * 批注
     */
    private String comment;

    @AssertTrue(message = "当运费类型为总价时总价金额不能为空")
    @SuppressWarnings("unused")
    public boolean isTotalAmountValid() {
        return priceType != ShippingPriceType.TOTAL || totalAmount != null;
    }

    @AssertTrue(message = "当运费类型为单价时运费公式不能为空")
    @SuppressWarnings("unused")
    public boolean isFormulaValid() {
        return priceType != ShippingPriceType.UNIT || formulaId != null;
    }

    @AssertTrue(message = "当运费类型为单价时运费公式价格不能为空")
    @SuppressWarnings("unused")
    public boolean isPricesValid() {
        return priceType != ShippingPriceType.UNIT || (CollectionUtils.isNotEmpty(args) && args.stream().allMatch(Objects::nonNull));
    }

    @AssertTrue(message = "当运费类型为单价时运费公式变动项不能为空")
    @SuppressWarnings("unused")
    public boolean isArgsValid() {
        return priceType != ShippingPriceType.UNIT || (CollectionUtils.isNotEmpty(args) && args.stream().allMatch(Objects::nonNull) && args.stream().allMatch(a -> a.getValue() != null));
    }

}
