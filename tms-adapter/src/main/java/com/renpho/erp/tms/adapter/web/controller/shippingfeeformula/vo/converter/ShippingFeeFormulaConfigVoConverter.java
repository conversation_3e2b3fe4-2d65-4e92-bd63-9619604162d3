package com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.vo.converter;

import com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.vo.ShippingFeeFormulaArgVo;
import com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.vo.ShippingFeeFormulaConfigVo;
import com.renpho.erp.tms.domain.shippingfeeformula.ShippingFeeFormulaArg;
import com.renpho.erp.tms.domain.shippingfeeformula.ShippingFeeFormulaConfig;
import com.renpho.erp.tms.infrastructure.persistence.shippingfeeformula.converter.ShippingFeeFormulaArgConverter;
import com.renpho.erp.tms.infrastructure.persistence.shippingfeeformula.converter.ShippingFeeFormulaConfigConverter;
import org.mapstruct.*;

import java.util.Collection;
import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {ShippingFeeFormulaArgConverter.class, ShippingFeeFormulaConfigConverter.class})
public interface ShippingFeeFormulaConfigVoConverter {

    @Mapping(source = "id", target = "id")
    ShippingFeeFormulaArgVo toArgVo(ShippingFeeFormulaArg domain);

    List<ShippingFeeFormulaArgVo> toArgVos(Collection<ShippingFeeFormulaArg> domains);

    ShippingFeeFormulaConfigVo toConfigVo(ShippingFeeFormulaConfig domain);

    @IterableMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    List<ShippingFeeFormulaConfigVo> toConfigVos(Collection<ShippingFeeFormulaConfig> domains);
}