package com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.command;

import com.renpho.erp.tms.domain.shippingfeeformula.FormulaArgType;
import com.renpho.karma.dto.Command;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/6/12
 */
@Getter
@Setter
public class ShippingFeeFormulaItemCmd extends Command implements Serializable {

    @Serial
    private static final long serialVersionUID = -2085696977389283542L;

    /**
     * ID, 仅费用公式参数项有此字段
     */
    private Integer id;

    /**
     * 显示名
     */
    @NotBlank
    private String name;

    /**
     * 实际值, 根据 {@link  #type} 不同有不同取值:
     * <ul>
     *   <li>计费项(OPERATOR): 对应计费项占位符(argName)</li>
     *   <li>运算符(ITEM):  '+'、'-'、'(' 等实际运算符,</li>
     *   <li>币种(CURRENCY): 'CNY'、'USD'等币种 code</li>
     *   <li>值(VALUE): 值占位符, 如 'amount1'、 'amount2'</li>
     * </ul>
     */
    private String value;

    @NotBlank
    private String argName;

    /**
     * 控件类型
     */
    private String nodeType;

    /**
     * 类型
     */
    @NotNull
    private FormulaArgType type;

    @AssertTrue(message = "{jakarta.validation.constraints.NotBlank.message}")
    @SuppressWarnings("unused")
    public boolean isValueValid() {
        return type != FormulaArgType.CURRENCY || StringUtils.isNotBlank(value);
    }
}
