package com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.command;

import com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.command.validator.ShippingFeeFormulaIdContainer;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2025/6/11
 */
@Getter
@Setter
public class UpdateShippingFeeFormulaArgCmd extends AddShippingFeeFormulaArgCmd implements ShippingFeeFormulaIdContainer {

    @Serial
    private static final long serialVersionUID = -5431862275558053096L;

    @NotNull
    private Integer id;
}
