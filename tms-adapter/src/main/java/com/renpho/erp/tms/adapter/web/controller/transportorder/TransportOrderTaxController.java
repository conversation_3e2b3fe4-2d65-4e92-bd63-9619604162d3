package com.renpho.erp.tms.adapter.web.controller.transportorder;

import com.renpho.erp.tms.infrastructure.script.CompiledScriptCache;
import com.renpho.karma.dto.R;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * TO单货代审批接口.
 *
 * <AUTHOR>
 * @since 2025/6/13
 */
@RestController
@RequestMapping("/transport/order/tax")
@ShenyuSpringCloudClient("/transport/order/tax/**")
@RequiredArgsConstructor
@Tag(name = "transport order tax", description = "TO单预估税费接口")
public class TransportOrderTaxController {

    private final CompiledScriptCache compiledScriptCache;

    @PostMapping("/cache/evict")
    public R<String> evict() {
        compiledScriptCache.evictAll();
        return R.success();
    }
}
