package com.renpho.erp.tms.adapter.web.controller.transportorder.cmd;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/6/12
 */
@Getter
@Setter
public class TransportOrderBookingCmd {

    /**
     * TO单ID
     */
    @NotNull
    private Integer toId;

    /**
     * 货代公司ID，取SRM的物流供应商的启用的物流商id
     */
    @NotNull
    private Integer forwarderCompanyId;

    /**
     * 货代公司Code，取SRM的物流供应商的启用的物流商code
     */
    @NotBlank
    private String forwarderCompanyCode;

    /**
     * 订舱号
     */
    @NotBlank
    @Size(max = 30, message = "error.to.booking-no-length-exceeded")
    private String bookingNo;

    /**
     * 预估运费
     */
    @NotNull
    private BigDecimal estimatedFreight;

    /**
     * 运费币种，如USD
     */
    @NotBlank
    private String freightCurrency;

    /**
     * 是否我司进口商，0=否，1=是
     */
    @NotNull
    private Boolean isImporter;

    /**
     * 装柜类型，字典类型：LOADING_TYPE。整柜、散货、自拼柜【FCL、LCL、Self】
     */
    @NotBlank
    private String loadingType;

    /**
     * 批注
     */
    @Size(max = 200, message = "error.to.comment-length-exceeded")
    private String comment;
}
