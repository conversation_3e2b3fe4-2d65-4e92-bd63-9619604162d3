package com.renpho.erp.tms.adapter.web.controller.customsinfo.command;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.container.HsCodeTaxRangeContainer;
import jakarta.validation.constraints.*;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddressList;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/4/25
 */
@Getter
@Setter
public class PskuCustomsInfoUploadExcel implements HsCodeTaxRangeContainer, Serializable {

    @Serial
    private static final long serialVersionUID = 5097657838362709713L;

    @ExcelProperty(value = "PSKU", index = 0)
    @NotBlank(message = "PSKU 不能为空; ")
    private String psku;

    @ExcelProperty(value = "excel.country_code", index = 1)
    @NotBlank(message = "国家地区不能为空; ")
    private String countryCode;

    @ExcelProperty(value = "excel.hs_code_added_tax", index = 2)
    @Pattern(regexp = "^\\d+(\\.\\d+)*$", message = "海关编码(加税)请填写数字或者数字+“.”; ")
    @Size(max = 20, message = "海关编码(加税)不能超过 20 个字符; ")
    private String hsCodeAddTax;

    @ExcelProperty(value = "excel.hs_code_reduced_tax", index = 3)
    @Pattern(regexp = "^\\d+(\\.\\d+)*$", message = "海关编码(减税)请填写数字或者数字+“.”; ")
    @Size(max = 20, message = "海关编码(减税)不能超过 20 个字符; ")
    private String hsCodeReducedTax;

    @ExcelProperty(value = "excel.currency_code", index = 4)
    private String currencyCode;

    @ExcelProperty(value = "excel.others", index = 5)
    @Positive(message = "其他(%)必须大于 0; ")
    @Pattern(regexp = "^(?:0|[1-9]\\d*)(?:\\.\\d{1,2})?$", message = "其他(%)必须是数字, 允许最多 2 位小数; ")
    private String others;

    @ExcelProperty(value = "excel.status", index = 6)
    @NotBlank(message = "状态不能为空; ")
    @Pattern(regexp = "启用|禁用", message = "状态不正确; ")
    private String status;

    @ExcelProperty(value = "excel.base_duty_rate", index = 7)
    @PositiveOrZero(message = "基本税率(%)必须大于等于 0; ")
    @Pattern(regexp = "^(?:0|[1-9]\\d*)(?:\\.\\d{1,2})?$", message = "基本税率(%)必须是数字, 允许最多 2 位小数; ")
    private String baseDutyRate;

    @ExcelProperty(value = "excel.gst", index = 8)
    @PositiveOrZero(message = "GST 必须大于等于 0; ")
    @Pattern(regexp = "^(?:0|[1-9]\\d*)(?:\\.\\d{1,2})?$", message = "GST 必须是数字, 允许最多 2 位小数; ")
    private String gst;

    @ExcelProperty(value = "excel.vat", index = 9)
    @PositiveOrZero(message = "VAT 必须大于等于 0; ")
    @Pattern(regexp = "^(?:0|[1-9]\\d*)(?:\\.\\d{1,2})?$", message = "VAT 必须是数字, 允许最多 2 位小数; ")
    private String vat;

    @ExcelProperty(value = "excel.additional_duty_name", index = 10)
    @Size(max = 30, message = "附加关税税种名不能超过 30 个字符; ")
    private String additionalDutyName;

    @ExcelProperty(value = "excel.additional_duty_rate", index = 11)
    @PositiveOrZero(message = "附加关税税率(%)必须大于等于 0; ")
    @Pattern(regexp = "^(?:0|[1-9]\\d*)(?:\\.\\d{1,2})?$", message = "附加关税税率(%)必须是数字, 允许最多 2 位小数; ")
    private String additionalDutyRate;

    @ExcelProperty(value = "excel.additional_duty_effect_time", index = 12)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private String additionalDutyEffectiveTime;

    @ExcelProperty(value = "excel.remark", index = 13)
    @Size(max = 200, message = "备注不能超过 200 个字符; ")
    private String remark;

    @AssertTrue(message = "国家非 CN 时基本税率不能为空; ")
    @SuppressWarnings("unused")
    public boolean isBaseDutyRateValid() {
        return StringUtils.equals("CN", this.countryCode) || this.baseDutyRate != null;
    }

    @AssertTrue(message = "加税和减税海关编码至少必填一个; ")
    @SuppressWarnings("unused")
    public boolean isContainsAtLeastOneHsCode() {
        return !StringUtils.isAllBlank(this.hsCodeAddTax, this.hsCodeReducedTax);
    }

    @AssertTrue(message = "国家非 CN 时其他(%)不能为空; ")
    @SuppressWarnings("unused")
    public boolean isOthersValid() {
        return StringUtils.equals("CN", this.countryCode) || this.others != null;
    }

    @AssertTrue(message = "国家非 CN 时 GST 不能为空; ")
    @SuppressWarnings("unused")
    public boolean isGstValid() {
        return StringUtils.equals("CN", this.countryCode) || this.gst != null;
    }

    @AssertTrue(message = "国家非 CN 时 VAT 不能为空; ")
    @SuppressWarnings("unused")
    public boolean isVatValid() {
        return StringUtils.equals("CN", this.countryCode) || this.vat != null;
    }

    @AssertTrue(message = "国家非 CN 时 币种不能为空; ")
    @SuppressWarnings("unused")
    public boolean isCurrencyValid() {
        return StringUtils.equals("CN", this.countryCode) || this.currencyCode != null;
    }

    @AssertTrue(message = "附加关税的税种、税率、生效时间必须都填写或者都不填写; ")
    @SuppressWarnings("unused")
    public boolean isAdditionalDutyValid() {
        return (StringUtils.isNotBlank(this.additionalDutyName) && this.additionalDutyRate != null && this.additionalDutyEffectiveTime != null)
               || (StringUtils.isBlank(this.additionalDutyName) && this.additionalDutyRate == null && this.additionalDutyEffectiveTime == null);
    }

    public static class StatusDropdownSheetWriteHandler implements SheetWriteHandler {

        // 下拉数据
        private final String[] dropdownOptions = {"启用", "禁用"};

        @Override
        public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            // 关键点：通过 writeSheetHolder.getSheet() 获取到 POI sheet
            Sheet sheet = writeSheetHolder.getSheet();
            DataValidationHelper helper = sheet.getDataValidationHelper();
            DataValidationConstraint constraint = helper.createExplicitListConstraint(dropdownOptions);
            // 想设置下拉的列号
            int columnIndex = 6;
            CellRangeAddressList addressList = new CellRangeAddressList(1, 10, columnIndex, columnIndex);
            DataValidation validation = helper.createValidation(constraint, addressList);
            validation.setSuppressDropDownArrow(true);
            validation.setShowErrorBox(true);
            sheet.addValidationData(validation);
        }

    }
}
