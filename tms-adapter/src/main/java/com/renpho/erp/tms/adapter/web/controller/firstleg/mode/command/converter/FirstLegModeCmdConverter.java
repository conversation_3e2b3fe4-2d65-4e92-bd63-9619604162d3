package com.renpho.erp.tms.adapter.web.controller.firstleg.mode.command.converter;

import com.renpho.erp.tms.adapter.web.controller.firstleg.mode.command.*;
import com.renpho.erp.tms.domain.firstleg.mode.FirstLegMode;
import com.renpho.erp.tms.domain.firstleg.mode.FirstLegModeId;
import com.renpho.erp.tms.infrastructure.common.converter.StatusConverter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import java.util.Collection;

/**
 * <AUTHOR>
 * @since 2025/4/21
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE,
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {StatusConverter.class})
public interface FirstLegModeCmdConverter {

    FirstLegMode toDomain(FirstLegModeAddCmd cmd);

    @Mapping(target = "id.id", source = "id")
    FirstLegMode toDomain(FirstLegModeEditCmd cmd);

    @Mapping(target = "nameCn", source = "name")
    @Mapping(target = "nameEn", source = "name")
    FirstLegMode toDomain(FirstLegModePageCmd cmd);

    @Mapping(target = "nameCn", source = "name")
    @Mapping(target = "nameEn", source = "name")
    FirstLegMode toDomain(FirstLegModeExportCmd cmd);

    @Mapping(target = "id.id", source = "id")
    @Mapping(target = "nameEn", source = "name")
    @Mapping(target = "nameCn", source = "name")
    FirstLegMode toDomain(FirstLegModeRemoteRequestCmd cmd);

    FirstLegModeId toId(Integer id);

    Collection<FirstLegModeId> toIds(Collection<Integer> ids);

}
