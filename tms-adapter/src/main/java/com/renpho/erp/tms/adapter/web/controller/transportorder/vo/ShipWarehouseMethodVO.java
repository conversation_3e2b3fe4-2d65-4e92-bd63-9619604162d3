package com.renpho.erp.tms.adapter.web.controller.transportorder.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/6/11
 */
@Data
public class ShipWarehouseMethodVO implements VO {
    @JsonIgnore
    private Integer id;

    /**
     * 发运与入库字典值, 字典: ship_warehouse_type
     */
    @Trans(type = TransType.DICTIONARY, key = "ship_warehouse_type", ref = "shipWarehouseMethodName")
    private String shipWarehouseMethod;

    /**
     * 发运与入库
     */
    private String shipWarehouseMethodName;
}
