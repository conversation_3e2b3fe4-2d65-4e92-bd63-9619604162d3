package com.renpho.erp.tms.adapter.web.controller.firstleg.mode.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/4/21
 */
@Data
public class FirstLegModeExcelExportVO implements VO {
    @ExcelProperty(value = "export.first-leg.id", index = 0)
    private Integer id;

    /**
     * 中文名称
     */
    @ExcelProperty(value = "export.first-leg.nameCn", index = 1)
    private String nameCn;

    /**
     * 英文名称
     */
    @ExcelProperty(value = "export.first-leg.nameEn", index = 2)
    private String nameEn;

    /**
     * 备注
     */
    @ExcelProperty(value = "export.first-leg.remark", index = 3)
    private String remark;

    @Trans(type = TransType.DICTIONARY, key = "status_dict", ref = "statusName")
    @ExcelIgnore
    private Integer status;

    /**
     * 状态
     */
    @ExcelProperty(value = "export.first-leg.status", index = 4)
    private String statusName;


    /**
     * 创建人 ID
     */
    @ExcelIgnore
    private Integer createBy;

    /**
     * 创建人名称
     */
    @ExcelProperty(value = "export.first-leg.creator", index = 5)
    private String creator;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "export.first-leg.createTime", index = 6)
    private LocalDateTime createTime;

    /**
     * 更新人 ID
     */
    @ExcelIgnore
    private Integer updateBy;

    /**
     * 更新人名称
     */
    @ExcelProperty(value = "export.first-leg.updater", index = 7)
    private String updater;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "export.first-leg.updateTime", index = 8)
    private LocalDateTime updateTime;
}
