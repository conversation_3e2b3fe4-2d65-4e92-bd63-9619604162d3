package com.renpho.erp.tms.adapter.web.controller.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Generated;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/4/22
 */
@Getter
@Setter
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-04-22T12:09:54.179410400+08:00[Asia/Shanghai]", comments = "Generator version: 7.12.0")
public class UploadResult implements Serializable {

    @Serial
    private static final long serialVersionUID = -6762900732849435238L;

    /**
     * 上传结果, 字典: status_dict
     */
    @Schema(name = "result", description = "上传结果, 字典: status_dict")
    private Integer result;

    /**
     * 错误文件 ID
     */
    @Schema(name = "key", description = "错误文件 ID")
    private String key;


    public static UploadResult success() {
        UploadResult result = new UploadResult();
        result.setResult(1);
        return result;
    }

    public static UploadResult error(String key) {
        UploadResult result = new UploadResult();
        result.setResult(0);
        result.setKey(key);
        return result;
    }
}

