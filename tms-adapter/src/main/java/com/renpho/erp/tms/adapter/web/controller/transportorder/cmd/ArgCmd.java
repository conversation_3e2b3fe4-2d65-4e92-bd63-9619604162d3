package com.renpho.erp.tms.adapter.web.controller.transportorder.cmd;

import com.renpho.karma.dto.Command;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/6/13
 */
@Getter
@Setter
public class ArgCmd extends Command {

    @Serial
    private static final long serialVersionUID = 4290287706243785908L;

    @NotBlank
    private String name;

    @NotNull(message = "公式中变动项 {jakarta.validation.constraints.NotBlank.message}")
    @Positive(message = "公式中变动项 {error.arg-value-must-be-positive}")
    private BigDecimal value;

}
