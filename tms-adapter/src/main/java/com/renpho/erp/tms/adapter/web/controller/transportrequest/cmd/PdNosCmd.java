package com.renpho.erp.tms.adapter.web.controller.transportrequest.cmd;

import jakarta.annotation.Generated;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * PdNosCmd
 */
@lombok.Getter
@lombok.Setter
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-05-19T18:06:01.197175700+08:00[Asia/Shanghai]", comments = "Generator version: 7.12.0")
public class PdNosCmd implements Serializable {


    @Serial
    private static final long serialVersionUID = 2520204918136843885L;

    @NotEmpty
    @Valid
    private List<@Valid @NotNull String> poNos;

}

