package com.renpho.erp.tms.adapter.web.controller.transportorder.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.renpho.erp.tms.adapter.web.controller.transportrequest.vo.TransportRequestVO;
import com.renpho.erp.tms.domain.transportorderforwarderapproval.ChargeWeightType;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * TO单-货代报价 VO
 *
 * <AUTHOR>
 * @since 2025/6/17
 */
@Getter
@Setter
public class TransportOrderForwarderQuoteVo implements VO, Serializable {

    @Serial
    private static final long serialVersionUID = -3414005147211289224L;

    /**
     * TO单ID
     */
    private Integer id;

    /**
     * TO单号
     */
    private String toNo;

    /**
     * 货代(物流供应商) ID
     */
    private Integer forwarderId;

    /**
     * 货代(物流供应商)编码
     */
    private String forwarderCode;

    /**
     * 货代(物流供应商)名称
     */
    private String forwarderName;

    /**
     * 货代(物流供应商)简称
     */
    private String forwarderShortName;

    /**
     * 是否包税
     */
    private Boolean isIncludeTax;

    /**
     * 批注
     */
    private String comment;

    /**
     * 不包税批注
     */
    private String commentNoTax;

    /**
     * 包税批注
     */
    private String commentWithTax;

    /**
     * 总数量
     */
    private Integer totalQty;

    /**
     * 总箱数
     */
    private Integer totalBoxQty;

    /**
     * 总毛重
     */
    private BigDecimal totalGrossWeight;

    /**
     * 总体积
     */
    private BigDecimal totalVolume;

    /**
     * 计费重
     */
    private BigDecimal chargeWeight;

    /**
     * 计费重类型字典, 字典类型: charge_weight_type
     */
    @Trans(type = TransType.ENUM, key = "charge_weight_type", ref = "chargeWeightTypeName")
    private ChargeWeightType chargeWeightType;

    /**
     * 计费重类型
     */
    private String chargeWeightTypeName;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 不包税金额
     */
    private BigDecimal amountNoTax;

    /**
     * 包税金额
     */
    private BigDecimal amountWithTax;

    /**
     * 是否选中
     */
    private Boolean status;

    /**
     * TR单集合
     */
    private List<TransportRequestVO> trs;

}
