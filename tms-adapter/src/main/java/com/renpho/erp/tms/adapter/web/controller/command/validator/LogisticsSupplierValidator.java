package com.renpho.erp.tms.adapter.web.controller.command.validator;

import com.renpho.erp.tms.adapter.web.controller.command.container.LogisticsSupplierIdContainer;
import com.renpho.erp.tms.domain.supplier.LogisticsSupplier;
import com.renpho.erp.tms.infrastructure.remote.srm.supplier.repository.LogisticsSupplierLookup;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.lang.annotation.Annotation;
import java.util.Optional;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @since 2025/6/17
 */
public abstract class LogisticsSupplierValidator<A extends Annotation, T> implements ConstraintValidator<A, T> {

    protected Predicate<T> predicate;

    @Resource
    protected LogisticsSupplierLookup logisticsSupplierLookup;

    @Override
    public boolean isValid(T value, ConstraintValidatorContext context) {
        return logisticsSupplierLookup == null || predicate.test(value);
    }

    public static class IsLogisticsSupplierIdExist extends LogisticsSupplierValidator<IsLogisticsSupplierExist, LogisticsSupplierIdContainer> {
        @Override
        public void initialize(IsLogisticsSupplierExist constraintAnnotation) {
            predicate = cmd -> {
                Optional<LogisticsSupplierIdContainer> optional = Optional.ofNullable(cmd);
                Optional<LogisticsSupplier> logisticsSupplier = optional
                        .map(LogisticsSupplierIdContainer::getLogisticsSupplierId)
                        .map(LogisticsSupplier.LogisticsSupplierId::new)
                        .flatMap(logisticsSupplierLookup::findById);
                optional.ifPresent(c -> c.setLogisticsSupplier(logisticsSupplier.orElse(null)));
                return logisticsSupplier.isPresent();
            };
        }
    }

}
