package com.renpho.erp.tms.adapter.web.controller.customsinfo.command;

import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.container.PskuCustomInfoIdContainer;
import jakarta.annotation.Generated;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/4/22
 */
@Getter
@Setter
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-04-22T12:09:54.179410400+08:00[Asia/Shanghai]", comments = "Generator version: 7.12.0")
public class UpdateCmd extends AddCmd implements PskuCustomInfoIdContainer, Serializable {

    @Serial
    private static final long serialVersionUID = 1989269505781173060L;

    @NotNull
    private Integer id;

}

