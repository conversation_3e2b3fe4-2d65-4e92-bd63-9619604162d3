package com.renpho.erp.tms.adapter.web.controller.transportorder.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.renpho.erp.tms.adapter.web.controller.orderfile.vo.OrderFileVO;
import com.renpho.erp.tms.adapter.web.controller.transportrequest.vo.TransportRequestDetailTOVO;
import com.renpho.erp.tms.domain.transportorderforwarderapproval.ChargeWeightType;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/10
 */
@Data
public class TransportOrderDeatilVO implements VO, Serializable {

    @Serial
    private static final long serialVersionUID = -7741876717531447841L;

    /**
     * TO单ID
     */
    private Integer id;

    /**
     * TO单号
     */
    private String toNo;

    /**
     * TO单状态
     */
    @Trans(type = TransType.DICTIONARY, key = "TO_STATUS", ref = "statusName")
    private Integer status;

    /**
     * TO单状态名称
     */
    private String statusName;

    /**
     * 总数量
     */
    private Integer totalQty;

    /**
     * 总箱数
     */
    private BigDecimal totalBoxQty;

    /**
     * 总毛重 kg
     */
    private BigDecimal totalGrossWeight;

    /**
     * 总净重 kg
     */
    private BigDecimal totalNetWeight;

    /**
     * 总体积 m³
     */
    private BigDecimal totalVolume;

    /**
     * 预估税费
     */
    private BigDecimal estimatedTax;

    /**
     * 预估税费币种
     */
    private String estimatedTaxCurrencyCode;

    /**
     * 货代公司ID，取SRM的物流供应商的启用的物流商id
     */
    private Integer forwarderCompanyId;

    /**
     * 货代公司Code，取SRM的物流供应商的启用的物流商code
     */
    private String forwarderCompanyCode;

    /**
     * 货代公司
     */
    private String forwarderCompany;

    /**
     * 订舱号
     */
    private String bookingNo;

    /**
     * 装柜类型，字典类型：LOADING_TYPE。整柜、散货、自拼柜【FCL、LCL、Self】
     */
    @Trans(type = TransType.DICTIONARY, key = "LOADING_TYPE", ref = "loadingTypeName")
    private String loadingType;

    private String loadingTypeName;

    /**
     * 预估运费
     */
    private BigDecimal estimatedFreight;

    /**
     * 运费币种，如USD
     */
    private String freightCurrency;

    /**
     * 目的港，取港口配置的启用的港口EN名称
     */
    private String destPort;

    /**
     * 中转港，取港口配置的启用的港口EN名称
     */
    private String transPort;

    /**
     * 预计离港时间
     */
    private LocalDate estimatedDepartureDate;

    /**
     * 预计到港时间
     */
    private LocalDate estimatedArrivalDate;

    /**
     * 是否保险，0=未投保，1=已投保
     */
    private Boolean isInsured;

    /**
     * 柜型，字典类型：CONTAINER_TYPE。取值如下：20GP、40GP、40HQ、45HQ
     */
    @Trans(type = TransType.DICTIONARY, key = "CONTAINER_TYPE", ref = "containerModelName")
    private String containerModel;
    private String containerModelName;

    /**
     * 柜号
     */
    private String containerNo;

    /**
     * 是否打托，0=否，1=是
     */
    private Boolean isPalletized;

    /**
     * 托盘数
     */
    private Integer palletQty;

    /**
     * 是否我司进口商，0=否，1=是
     */
    private Boolean isImporter;

    /**
     * 审批状态，字典类型：TO_APPROVAL_STATUS。取值如下：不需审批、审批中、审批通过、审批不通过【NO_APPROVAL_NEEDED  、AUDITING、APPROVED、REJECTED】
     */
    private String approvalStatus;

    /**
     * 运单信息（TR）
     */
    private List<TransportRequestDetailTOVO> trItems;

    /**
     * 文件信息
     */
    private List<OrderFileVO> files;

    /**
     * 批注列表
     */
    private List<TransportOrderCommentVO> comments;

    /**
     * 状态历史列表
     */
    private List<TransportOrderStatusHistoryVO> statusChangeTimeRecords;


    /**
     * 计费重
     */
    private BigDecimal chargeWeight;

    /**
     * 计费重类型字典，字典类型：charge_weight_type
     */
    @Trans(type = TransType.DICTIONARY, key = "charge_weight_type", ref = "chargeWeightTypeName")
    private ChargeWeightType chargeWeightType;

    /**
     * 计费重类型: 体积重/重量重
     */
    private String chargeWeightTypeName;

}

