package com.renpho.erp.tms.adapter.web.controller.transportrequest.vo;

import com.renpho.erp.tms.domain.transportorder.TransportMode;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2025/6/11
 */
@Data
public class TransportRequestViewVO {
    /**
     * TR单Id
     */
    private Integer id;

    /**
     * TR单号
     */
    private String trNo;

    /**
     * 运单号
     */
    private String trackingNo;

    /**
     * TO单号
     */
    private String toNo;

    /**
     * TO单ID
     */
    private Integer toId;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 装柜类型，字典类型：LOADING_TYPE。整柜、散货、自拼柜【FCL、LCL、Self】
     */
    private String loadingType;

    /**
     * 头程方式类型标签：SEA_TRANSPORT=海运、AIR_TRANSPORT=空运、RAILWAY=铁路、LAND_TRANSPORT=陆运
     */
    private TransportMode firstLegModeLabel;

    /**
     * 交货时间
     */
    private LocalDate deliveryTime;

    /**
     * 实际离港时间
     */
    private LocalDate actualDepartureTime;

    /**
     * 实际到港时间
     */
    private LocalDate actualArrivalTime;

    /**
     * 实际派送时间
     */
    private LocalDate actualDeliveryTime;

}
