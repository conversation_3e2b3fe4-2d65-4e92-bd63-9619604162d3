package com.renpho.erp.tms.adapter.web.controller.transportorder.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 运费公式计算结果 VO
 *
 * <AUTHOR>
 * @since 2025/6/20
 */
@Getter
@Setter
public class ShippingFeeFormulaCalculatorResultVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 2659490394307300792L;

    /**
     * TO单ID
     */
    private Integer toId;

    /**
     * TO单号
     */
    private String toNo;

    /**
     * 包税价
     */
    private BigDecimal amount;

    /**
     * 不包税价
     */
    private BigDecimal amountNoTax;

}
