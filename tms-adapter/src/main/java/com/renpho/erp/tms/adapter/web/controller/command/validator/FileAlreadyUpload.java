package com.renpho.erp.tms.adapter.web.controller.command.validator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * <AUTHOR>
 * @since 2025/5/28
 */
@Documented
@Constraint(validatedBy = {FileValidator.IsFileExist.class})
@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
@Retention(RUNTIME)
public @interface FileAlreadyUpload {

    String message() default "{file.not.exist}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};


}
