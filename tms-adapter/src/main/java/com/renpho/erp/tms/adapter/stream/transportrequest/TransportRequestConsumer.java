package com.renpho.erp.tms.adapter.stream.transportrequest;

import com.alibaba.fastjson.JSON;
import com.renpho.erp.pms.model.common.model.PmsStreamConstants;
import com.renpho.erp.pms.model.shipmentplan.model.ShipmentPlanOrderDTO;
import com.renpho.erp.qms.common.QmsStreamConstants;
import com.renpho.erp.qms.task.QualityControllerTaskDTO;
import com.renpho.erp.stream.StreamCommonConstants;
import com.renpho.erp.tms.adapter.stream.transportrequest.handler.TransportRequestCmdHandler;
import com.renpho.erp.tms.domain.exception.BusinessException;
import com.renpho.erp.tms.domain.transportrequest.QcResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;

import java.util.Optional;
import java.util.function.Consumer;

/**
 * TR消费者
 *
 * <AUTHOR> Zheng
 * @since 2025/5/22
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class TransportRequestConsumer {

    /**
     * PD类型TR操作
     *
     * @return message
     */
    @Bean
    public Consumer<Message<String>> transportRequestPdOperateConsumer(TransportRequestCmdHandler transportRequestCmdHandler) {
        return msg -> {
            try {
                String tag = Optional.ofNullable(msg.getHeaders().get(StreamCommonConstants.HEADER_TAGS))
                        .map(String::valueOf)
                        .filter(StringUtils::isNotBlank)
                        .orElseThrow(() -> new BusinessException("error.tr.tag-not-found"));
                log.info("接收到PMS-PD单: tag=[{}], body=[{}]", tag, msg);
                String payload = msg.getPayload();
                switch (tag) {
                    case PmsStreamConstants.PD_CONFIRM ->
                            transportRequestCmdHandler.add(JSON.parseObject(payload, ShipmentPlanOrderDTO.class));
                    case PmsStreamConstants.PD_UPDATE ->
                            transportRequestCmdHandler.update(JSON.parseObject(payload, ShipmentPlanOrderDTO.class));
                    case PmsStreamConstants.PD_CANCEL ->
                            transportRequestCmdHandler.cancel(JSON.parseObject(payload, ShipmentPlanOrderDTO.class));
                    case QmsStreamConstants.PD_QC_PENDING ->
                            transportRequestCmdHandler.updateQcResult(JSON.parseObject(payload, QualityControllerTaskDTO.class), QcResult.PENDING);
                    case QmsStreamConstants.PD_QC_QUALIFIED ->
                            transportRequestCmdHandler.updateQcResult(JSON.parseObject(payload, QualityControllerTaskDTO.class), QcResult.PASSED);
                    case QmsStreamConstants.PD_QC_UNQUALIFIED ->
                            transportRequestCmdHandler.updateQcResult(JSON.parseObject(payload, QualityControllerTaskDTO.class), QcResult.FAILED);
                    default -> log.error("未知Tags信息");
                }

            } catch (Exception e) {
                log.error("requisitionPoolPoOperateConsumer 消费消息失败", e);
                throw new RuntimeException(e.getMessage(), e);
            }
        };
    }

}
