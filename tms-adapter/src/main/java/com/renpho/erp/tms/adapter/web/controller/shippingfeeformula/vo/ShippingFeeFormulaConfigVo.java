package com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.vo;

import com.fhs.core.trans.vo.VO;
import com.renpho.erp.tms.domain.shippingfeeformula.ShippingFeeFormulaItem;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * VO for {@link com.renpho.erp.tms.domain.shippingfeeformula.ShippingFeeFormulaConfig}
 */
@Getter
@Setter
public class ShippingFeeFormulaConfigVo implements VO, Serializable {
    @Serial
    private static final long serialVersionUID = 711549169777086823L;

    private Integer id;

    @Schema(name = "formulaJson", description = "公式")
    private List<ShippingFeeFormulaItem> formulaJson;

    @Schema(name = "returnName", description = "公式返回值名称")
    private String returnName;

    /**
     * 创建人 ID
     */
    @Schema(name = "createBy", description = "创建人 ID")
    private Integer createBy;

    /**
     * 创建人名称
     */
    @Schema(name = "createByName", description = "创建人名称")
    private String createByName;

    /**
     * 创建人工号
     */
    @Schema(name = "createByCode", description = "创建人工号")
    private String createByCode;

    /**
     * 创建时间
     */
    @Schema(name = "createTime", description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人 ID
     */
    @Schema(name = "updateBy", description = "更新人 ID")
    private Integer updateBy;

    /**
     * 更新人名称
     */
    @Schema(name = "updateByName", description = "更新人名称")
    private String updateByName;

    /**
     * 更新人工号
     */
    @Schema(name = "updateByCode", description = "更新人工号")
    private String updateByCode;

    /**
     * 更新时间
     */
    @Schema(name = "updateTime", description = "更新时间")
    private LocalDateTime updateTime;

}