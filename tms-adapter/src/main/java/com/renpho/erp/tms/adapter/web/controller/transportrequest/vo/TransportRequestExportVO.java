package com.renpho.erp.tms.adapter.web.controller.transportrequest.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.github.houbb.sensitive.annotation.Sensitive;
import com.renpho.erp.data.sensitive.SensitiveDynamicCondition;
import com.renpho.erp.data.sensitive.SensitiveDynamicStrategy;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class TransportRequestExportVO implements VO {
    @ExcelIgnore
    private Integer id;

    /**
     * TR单号
     */
    @ExcelProperty(value = "export.tr.tr-no", index = 0)
    private String trNo;

    /**
     * PO单号
     */
    @ExcelProperty(value = "export.tr.po-no", index = 1)
    private String poNo;

    /**
     * SKU
     */
    @ExcelProperty(value = "export.tr.psku", index = 2)
    private String psku;

    /**
     * 产品名称-英文
     */
    @ExcelProperty(value = "export.tr.name-en", index = 3)
    private String nameEn;

    /**
     * 总数量
     */
    @ExcelProperty(value = "export.tr.total-qty", index = 4)
    private Integer totalQty;

    /**
     * 总箱数
     */
    @ExcelProperty(value = "export.tr.total-box-qty", index = 5)
    private Integer totalBoxQty;

    /**
     * 总毛重 kg
     */
    @ExcelProperty(value = "export.tr.total-gross-weight", index = 6)
    private BigDecimal totalGrossWeight;

    /**
     * 总净重 kg
     */
    @ExcelProperty(value = "export.tr.total-net-weight", index = 7)
    private BigDecimal totalNetWeight;

    /**
     * 总体积 m³
     */
    @ExcelProperty(value = "export.tr.total-volume", index = 8)
    private BigDecimal totalVolume;

    /**
     * 敏感属性
     */
    @ExcelProperty(value = "export.tr.hazardous-pros", index = 9)
    private String hazardousPros;

    /**
     * 目的国/地区
     */
    @ExcelProperty(value = "export.tr.dest-country", index = 10)
    private String destCountry;

    /**
     * 目的仓编码
     */
    @ExcelProperty(value = "export.tr.dest-warehouse-code", index = 11)
    private String destWarehouseCode;

    /**
     * 目的地
     */
    @ExcelProperty(value = "export.tr.destination", index = 12)
    private String destination;

    /**
     * 采购供应商名称
     */
    @ExcelProperty(value = "export.tr.purchase-supplier-name", index = 13)
    @Sensitive(permission = "SUPPLIER:NAME", condition = SensitiveDynamicCondition.class,
            strategy = SensitiveDynamicStrategy.class)
    private String purchaseSupplierName;

//    @Trans(type = TransType.DICTIONARY, key = "trade_terms", ref = "tradeTermsName")
    @ExcelProperty(value = "export.tr.trade-terms-name", index = 14)
    private String tradeTerms;

    /**
     * 贸易条款
     */
    @ExcelIgnore
    private String tradeTermsName;

    /**
     * 平台名称
     */
    @ExcelProperty(value = "export.tr.sales-channel-name", index = 15)
    private String salesChannelName;

    /**
     * 货主名称
     */
    @ExcelProperty(value = "export.tr.owner-name", index = 16)
    private String ownerName;

//    @Trans(type = TransType.DICTIONARY, key = "trade_terms_ship_to", ref = "shippingPortName")
    @ExcelProperty(value = "export.tr.shipping-port-name", index = 17)
    private String shippingPort;

    /**
     * 起运港
     */
    @ExcelIgnore
    private String shippingPortName;

    @ExcelIgnore
    @Trans(type = TransType.DICTIONARY, key = "logistics_type", ref = "carrierTypeName")
    private String carrierType;

    /**
     * 服务商类型
     */
    @ExcelProperty(value = "export.tr.carrier-type-name", index = 18)
    private String carrierTypeName;

    /**
     * 头程方式
     */
    @ExcelProperty(value = "export.tr.first-leg-mode-name", index = 19)
    private String firstLegModeName;

    /**
     * ShipmentId
     */
    @ExcelProperty(value = "export.tr.shipment-id", index = 20)
    private String shipmentId;

    /**
     * ReferenceId
     */
    @ExcelProperty(value = "export.tr.reference-id", index = 21)
    private String referenceId;

    /**
     * 计划出货日期-开始
     */
    @ExcelProperty(value = "export.tr.planned-ship-start-date", index = 22)
    private LocalDate plannedShipStartDate;

    /**
     * 计划出货日期-结束
     */
    @ExcelProperty(value = "export.tr.planned-ship-end-date", index = 23)
    private LocalDate plannedShipEndDate;

    @ExcelIgnore
    @Trans(type = TransType.DICTIONARY, key = "quality_task_result", ref = "qcResultName")
    private Integer qcResult;

    /**
     * 质检结果名称
     */
    @ExcelProperty(value = "export.tr.qc-result-name", index = 24)
    private String qcResultName;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "export.tr.update-time", index = 25)
    private LocalDateTime updateTime;

    /**
     * 交付单号（PD单号）
     */
    @ExcelProperty(value = "export.tr.pd-no", index = 26)
    private String pdNo;

    /**
     * TO单号
     */
    @ExcelProperty(value = "export.tr.to-no", index = 27)
    private String toNo;
}
