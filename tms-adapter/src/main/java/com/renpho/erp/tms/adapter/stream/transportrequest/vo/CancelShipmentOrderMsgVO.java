package com.renpho.erp.tms.adapter.stream.transportrequest.vo;

import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2025/7/19
 */
@Getter
@Setter
@Builder
public class CancelShipmentOrderMsgVO {
    private String shipmentId;
    private Integer trId;
    private WarehouseProviderType warehouseProviderType;
    private Boolean isAmazonTr;
}
