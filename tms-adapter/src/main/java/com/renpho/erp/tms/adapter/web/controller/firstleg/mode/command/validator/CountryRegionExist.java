package com.renpho.erp.tms.adapter.web.controller.firstleg.mode.command.validator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * <AUTHOR>
 * @since 2025/4/23
 */
@Target({ METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE })
@Retention(RUNTIME)
@Constraint(validatedBy = {CountryRegionValidator.class})
@Documented
public @interface CountryRegionExist {
    String message() default "必须填写系统存在的国家二字简码; ";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
