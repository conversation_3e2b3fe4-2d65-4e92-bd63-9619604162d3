package com.renpho.erp.tms.adapter.web.controller.transportrequest.cmd;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 运输计划-tr单-签收.
 * <AUTHOR>
 * @since 2025/7/22
 */
@Data
public class SignInDoCmd {

    /**
     * 单据ID、tr单ID
     */
    @NotNull(message = "{error.tr.sign.id-required}")
    private Integer id;

    /**
     * TR单号
     */
    @NotNull(message = "{error.tr.sign.tr-no-required}")
    private String trNo;

    /**
     * 产品SKU
     */
    @NotNull(message = "{error.tr.sign.psku-required}")
    private String psku;

    /**
     * FNSKU编码
     */
    @NotNull(message = "{error.tr.sign.fn-sku-required}")
    private String fnSku;

    /**
     * 签收数量(大于0)
     */
    @Min(value = 1, message = "{error.tr.sign.received-quantity-min}")
    @NotNull(message = "{error.tr.sign.received-quantity-required}")
    private Integer receivedQuantity;

    /**
     * 签收时间
     */
    @NotNull(message = "{error.tr.sign.received-time-required}")
    private LocalDateTime receivedTime;

    /**
     * 备注信息
     */
    private String comment;

}
