package com.renpho.erp.tms.adapter.stream.transportorder;

import com.alibaba.fastjson.JSON;
import com.renpho.erp.bpm.api.dto.ProcessResultDto;
import com.renpho.erp.tms.domain.transportorder.TransportOrderApprovalStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;

import java.util.function.Consumer;

/**
 * TO单货代审批回调
 *
 * <AUTHOR>
 * @since 2025/6/23
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class TransportOrderFreightQuoteStream {

    @Bean
    public Consumer<Message<ProcessResultDto>> transportOrderFreightQuoteConsumer(TransportOrderFreightQuoteCmdHandler transportOrderFreightQuoteCmdHandler) {
        return msg -> {
            ProcessResultDto payload = msg.getPayload();
            String json = JSON.toJSONString(payload);
            log.info("收到TO单货代审批回调消息, msg header: [{}],json：[{}] ", msg.getHeaders(), json);
            try {
                TransportOrderApprovalStatusEnum result = TransportOrderApprovalStatusEnum.fromValue(payload.getResult());
                if (TransportOrderApprovalStatusEnum.APPROVED.equals(result)) {
                    transportOrderFreightQuoteCmdHandler.finish(payload);
                } else if (TransportOrderApprovalStatusEnum.REJECTED.equals(result) || TransportOrderApprovalStatusEnum.VOID.equals(result)) {
                    transportOrderFreightQuoteCmdHandler.reject(payload);
                } else if (TransportOrderApprovalStatusEnum.CANCEL.equals(result)) {
                    transportOrderFreightQuoteCmdHandler.cancel(payload);
                } else {
                    transportOrderFreightQuoteCmdHandler.updateApprovalResult(payload);
                }
            } catch (InterruptedException e) {
                throw new RuntimeException("TO单货代审批回调监听器休眠被中断: payload: " + json, e);
            }
        };
    }

}
