package com.renpho.erp.tms.adapter.web.controller.orderfile.cmd;

import com.renpho.erp.tms.adapter.web.controller.command.group.CreateGroup;
import com.renpho.erp.tms.adapter.web.controller.command.group.OtherGroup;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 附件信息
 *
 * <AUTHOR>
 * @date 2025/6/17
 */
@Data
public class OrderFileInfoCmd implements Serializable {

    @Serial
    private static final long serialVersionUID = 6403180874424651350L;

    /**
     * 文件id
     */
    @NotBlank(message = "fileId.is.not.null", groups = {CreateGroup.class, OtherGroup.class})
    private String fileId;

    /**
     * 文件名称
     */
    private String filename;

    /**
     * 海关税单号
     */
    private String customsNo;

}
