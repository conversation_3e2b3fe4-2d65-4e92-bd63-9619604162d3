package com.renpho.erp.tms.adapter.web.controller.customsinfo.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.renpho.erp.tms.adapter.web.controller.vo.serializer.BigDecimalScale2RoundingSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

/**
 * 按生效状态统计附加关税合计税率
 *
 * <AUTHOR>
 * @since 2025/4/24
 */
@Getter
@Setter
@NoArgsConstructor
public class PskuCustomsAdditionalDutySumUpVO implements VO, Serializable {

    @Serial
    private static final long serialVersionUID = -8358507089842857559L;

    /**
     * @deprecated
     */
    @JsonIgnore
    @Schema(hidden = true)
    @Deprecated
    private String id;

    /**
     * 附加关税生效状态字典值, 字典: psku_customs_info_additional_duty_status
     */
    @Trans(type = TransType.DICTIONARY, key = "psku_customs_info_additional_duty_status", ref = "effectStatusName")
    private Integer effectStatus;

    /**
     * 附加关税生效状态
     */
    private String effectStatusName;

    /**
     * 附加关税合计税率
     */
    @JsonSerialize(using = BigDecimalScale2RoundingSerializer.class)
    private BigDecimal totalDutyRate;

    public PskuCustomsAdditionalDutySumUpVO(Integer status) {
        this.effectStatus = status;
    }

    public void add(PskuCustomsAdditionalDutyVO vo) {
        Optional<PskuCustomsAdditionalDutyVO> optional = Optional.ofNullable(vo);
        if (optional.map(PskuCustomsAdditionalDutyVO::getStatus).filter(s -> Objects.equals(this.effectStatus, s)).isEmpty()) {
            return;
        }
        this.totalDutyRate = Optional.ofNullable(this.totalDutyRate)
                .orElse(BigDecimal.ZERO)
                .add(optional.map(PskuCustomsAdditionalDutyVO::getDutyRate).orElse(BigDecimal.ZERO));
    }

}
