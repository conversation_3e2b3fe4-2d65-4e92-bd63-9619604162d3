package com.renpho.erp.tms.adapter.web.controller.transportrequest.cmd;

import com.renpho.karma.dto.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Generated;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Set;

/**
 * 分页查询参数
 *
 * <AUTHOR>
 * @since 2025/5/19
 */
@lombok.Getter
@lombok.Setter
@Schema(name = "ListQuery", description = "分页查询参数")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-05-19T18:06:01.197175700+08:00[Asia/Shanghai]", comments = "Generator version: 7.12.0")
public class ListQuery extends PageQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 2658088360091072592L;

    /**
     * TR单号
     */
    @Schema(name = "trNo", description = "TR单号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String trNo;

    /**
     * TO单号
     */
    @Schema(name = "toNo", description = "TO单号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String toNo;

    /**
     * psku
     */
    @Schema(name = "psku", description = "psku", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String psku;

    /**
     * 产品 ID
     */
    @Schema(name = "productId", description = "产品 ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer productId;

    /**
     * 产品 ID 数组
     */
    @Schema(name = "productIds", description = "产品 ID 数组", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Set<Integer> productIds;

    /**
     * PO单号
     */
    @Schema(name = "poNo", description = "PO单号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String poNo;

    /**
     * 国家地区编码数组
     */
    @Schema(name = "countryCodes", description = "国家地区编码数组", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Set<String> countryCodes;

    /**
     * 头程方式ID数组
     */
    @Schema(name = "firstLegModes", description = "头程方式ID数组", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Set<Integer> firstLegModes;

    /**
     * 起运港数组, 字典: trade_terms_ship_to
     */
    @Schema(name = "shippingPorts", description = "起运港数组, 字典: trade_terms_ship_to", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Set<String> shippingPorts;

    /**
     * 平台 ID, MDM 销售渠道
     */
    @Schema(name = "salesChannelId", description = "平台 ID, MDM 销售渠道", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer salesChannelId;

    /**
     * 货主 ID, MDM 公司主体
     */
    @Schema(name = "ownerId", description = "货主 ID, MDM 公司主体", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer ownerId;

    /**
     * 服务商类型, 字典: logistics_type
     */
    @Schema(name = "carrierType", description = "服务商类型, 字典: logistics_type", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String carrierType;

    /**
     * 目的仓 ID 数组, IMS 仓库主数据
     */
    @Schema(name = "destWarehouseIds", description = "目的仓 ID 数组, IMS 仓库主数据", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Set<String> destWarehouseIds;

    /**
     * 目的仓编码数组, IMS 仓库主数据(需过滤平台仓) + IMS 平台仓库地址
     */
    @Schema(name = "destWarehouseCodes", description = "目的仓编码数组, IMS 仓库主数据(需过滤平台仓) + IMS 平台仓库地址", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Set<String> destWarehouseCodes;

    /**
     * 发运与入库, 字典: ship_warehouse_type
     */
    @Schema(name = "shipReceiveType", description = "发运与入库, 字典: ship_warehouse_type", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String shipReceiveType;

    /**
     * 计划出货日期
     */
    @Schema(name = "plannedShipStartDate", description = "计划出货日期", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDate plannedShipStartDate;


    /**
     * 计划出货日期
     */
    @Schema(name = "plannedShipEndDate", description = "计划出货日期", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDate plannedShipEndDate;

    /**
     * 工厂交期
     */
    @Schema(name = "latestDeliveryDate", description = "工厂交期", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDate latestDeliveryDate;

    /**
     * 业务类型, 字典: business_type
     */
    @Schema(name = "businessType", description = "业务类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String businessType;

    /**
     * 状态
     */
    @Schema(name = "status", description = "状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer status;

}

