package com.renpho.erp.tms.adapter.web.controller.transportorder.cmd;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.renpho.erp.tms.adapter.web.controller.command.container.ProcessInstanceIdContainer;
import com.renpho.erp.tms.adapter.web.controller.command.validator.IdExist;
import com.renpho.erp.tms.adapter.web.controller.command.validator.IsLogisticsSupplierExist;
import com.renpho.erp.tms.domain.processinstance.ProcessInstance;
import com.renpho.karma.dto.Command;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/24
 */
@Getter
@Setter
public class TransportOrderForwarderApprovalIdConfirmCmd extends Command implements ProcessInstanceIdContainer {

    @Serial
    private static final long serialVersionUID = -3742575842318852093L;

    /**
     * 审批 ID
     */
    @NotBlank
    private String processInstanceId;

    @JsonIgnore
    private ProcessInstance processInstance;

    @NotEmpty
    @Valid
    private List<@NotNull @Valid @IsLogisticsSupplierExist @IdExist TransportOrderForwarderApprovalSelectedConfirmCmd> selects;

}
