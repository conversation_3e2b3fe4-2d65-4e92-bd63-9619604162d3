package com.renpho.erp.tms.adapter.web.controller.command.validator;

import com.renpho.erp.tms.adapter.web.controller.command.container.ProcessInstanceIdContainer;
import com.renpho.erp.tms.domain.processinstance.ProcessInstance;
import com.renpho.erp.tms.infrastructure.remote.processinstance.converter.ProcessInstanceConverter;
import com.renpho.erp.tms.infrastructure.remote.processinstance.feign.RemoteProcessInstanceFeign;
import com.renpho.karma.dto.R;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

import java.lang.annotation.Annotation;
import java.util.Optional;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @since 2025/6/18
 */
public abstract class ProcessInstanceValidator<A extends Annotation, T> implements ConstraintValidator<A, T> {

    protected Predicate<T> predicate;

    @Resource
    protected RemoteProcessInstanceFeign remoteProcessInstanceFeign;

    @Resource
    protected ProcessInstanceConverter processInstanceConverter;

    @Override
    public boolean isValid(T value, ConstraintValidatorContext context) {
        return remoteProcessInstanceFeign == null || predicate.test(value);
    }

    public static class IsIdExist extends ProcessInstanceValidator<IdExist, ProcessInstanceIdContainer> {
        @Override
        public void initialize(IdExist constraintAnnotation) {
            predicate = cmd -> {
                Optional<ProcessInstanceIdContainer> optional = Optional.ofNullable(cmd);
                Optional<ProcessInstance> processInstance = optional.map(ProcessInstanceIdContainer::getProcessInstanceId)
                        .filter(StringUtils::isNotBlank)
                        .map(remoteProcessInstanceFeign::getProcessInstance)
                        .map(R::getData)
                        .map(processInstanceConverter::toDomain);
                optional.ifPresent(c -> c.setProcessInstance(processInstance.orElse(null)));
                return processInstance.isPresent();
            };
        }
    }
}
