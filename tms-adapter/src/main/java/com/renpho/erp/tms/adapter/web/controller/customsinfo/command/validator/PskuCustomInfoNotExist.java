package com.renpho.erp.tms.adapter.web.controller.customsinfo.command.validator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.ElementType.CONSTRUCTOR;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE_USE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * <AUTHOR>
 * @since 2025/5/9
 */
@Documented
@Constraint(validatedBy = {PskuCustomsInfoValidator.IsPskuCustomInfoNotExist.class})
@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
@Retention(RUNTIME)
public @interface PskuCustomInfoNotExist {

    String message() default "{error.psku_customs_info_already_exist}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
