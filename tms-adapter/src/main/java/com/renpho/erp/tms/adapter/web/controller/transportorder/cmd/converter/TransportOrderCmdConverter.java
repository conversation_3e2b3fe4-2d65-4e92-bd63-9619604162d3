package com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.converter;

import com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.*;
import com.renpho.erp.tms.domain.transportorder.TransportOrder;
import com.renpho.erp.tms.domain.transportorder.TransportOrderId;
import com.renpho.erp.tms.domain.transportorder.TransportOrderQuery;
import com.renpho.erp.tms.domain.transportrequest.TransportRequest;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestId;
import com.renpho.erp.tms.infrastructure.persistence.firstleg.mode.po.converter.FirstLegModeConverter;
import com.renpho.erp.tms.infrastructure.remote.owner.converter.OwnerConverter;
import com.renpho.erp.tms.infrastructure.remote.saleschannel.converter.SalesChannelConverter;
import com.renpho.erp.tms.infrastructure.remote.store.converter.StoreConverter;
import com.renpho.erp.tms.infrastructure.remote.warehouse.converter.WarehouseConverter;
import org.mapstruct.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/6/10
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE,
        componentModel = MappingConstants.ComponentModel.SPRING
        , uses = {FirstLegModeConverter.class, WarehouseConverter.class, SalesChannelConverter.class, StoreConverter.class, OwnerConverter.class}
)
public interface TransportOrderCmdConverter {
    @Mapping(target = "likeTrNo", source = "trNo")
    @Mapping(target = "likeToNo", source = "toNo")
    @Mapping(target = "likePsku", source = "psku")
    @Mapping(target = "likePoNo", source = "poNo")
    @Mapping(target = "likePdNo", source = "pdNo")
    @Mapping(target = "likeContainerNo", source = "containerNo")
    @Mapping(target = "likeTrackingNo", source = "trackingNo")
    @Mapping(target = "likeBookingNo", source = "bookingNo")
    @Mapping(target = "trNo", ignore = true)
    @Mapping(target = "toNo", ignore = true)
    @Mapping(target = "psku", ignore = true)
    @Mapping(target = "poNo", ignore = true)
    @Mapping(target = "pdNo", ignore = true)
    @Mapping(target = "containerNo", ignore = true)
    @Mapping(target = "trackingNo", ignore = true)
    @Mapping(target = "bookingNo", ignore = true)
    TransportOrderQuery toPageDomain(TransportOrderListCmd cmd);

    TransportOrderQuery toDetailDomain(TransportOrderDetailCmd cmd);

    @Mapping(target = "id", source = "id")
    TransportOrderId toTransportOrderId(IdQuery id);

    @Mapping(target = "id.id", source = "toId")
    @Mapping(target = "logisticsSupplier.supplierCode", source = "forwarderCompanyCode")
    @Mapping(target = "logisticsSupplier.id.id", source = "forwarderCompanyId")
    TransportOrder toDomain(TransportOrderBookingCmd cmd);


    @Mappings({
            @Mapping(target = "id.id", source = "toId"),
            @Mapping(target = "destPort", source = "destPort"),
            @Mapping(target = "transPort", source = "transPort"),
            @Mapping(target = "estimatedDepartureDate", source = "estimatedDepartureDate"),
            @Mapping(target = "estimatedArrivalDate", source = "estimatedArrivalDate"),
            @Mapping(target = "isInsured", source = "isInsured"),
            @Mapping(target = "containerModel", source = "containerModel"),
            @Mapping(target = "containerNo", source = "containerNo"),
            @Mapping(target = "isPalletized", source = "isPalletized"),
            @Mapping(target = "palletQty", source = "palletQty"),
            @Mapping(target = "comment", source = "comment"),
            @Mapping(target = "transportRequestList", source = "trItems") // 需要写映射方法
    })
    TransportOrder toDomain(TransportOrderHandoverCmd cmd);

    @Mapping(target = "id.id", source = "toId")
    @Mapping(target = "trIds", source = "trIds", qualifiedByName = "mapTrId")
    TransportOrder toDomain(TransportOrderEditCmd cmd);

    @Mapping(target = "id.id", source = "toId")
    TransportOrder toDomain(TransportOrderCommentCmd cmd);

    @Named("mapTrId")
    default List<TransportRequestId> mapTrId(List<Integer> value) {
        return value.stream().map(TransportRequestId::new).toList();
    }

    @Mapping(target = "shippingCompany.shortName", source = "shippingCompany")
    @Mapping(target = "id.id", source = "trId")
    @Mapping(target = "transportOrderId.id", source = "toId")
    TransportRequest toTransportRequest(TransportOrderDepartureCmd cmd);


    @Mapping(target = "id.id", source = "trId")
    @Mapping(target = "transportOrderId.id", source = "toId")
    TransportRequest toTransportRequest(TransportOrderArrivalCmd cmd);

    @Mapping(target = "id.id", source = "trId")
    @Mapping(target = "transportOrderId.id", source = "toId")
    TransportRequest toTransportRequest(TransportOrderDeliveryCmd cmd);

    default List<TransportOrder> deliveryCmdToDomains(List<TransportOrderDeliveryCmd> cmds) {
        if (cmds == null || cmds.isEmpty()) {
            return Collections.emptyList();
        }

        // 分组：toId -> List<cmd>
        Map<Integer, List<TransportOrderDeliveryCmd>> grouped = cmds.stream()
                .collect(Collectors.groupingBy(TransportOrderDeliveryCmd::getToId));

        List<TransportOrder> result = new ArrayList<>();

        for (Map.Entry<Integer, List<TransportOrderDeliveryCmd>> entry : grouped.entrySet()) {
            Integer toId = entry.getKey();
            List<TransportOrderDeliveryCmd> cmdList = entry.getValue();

            TransportOrder order = new TransportOrder(new TransportOrderId(toId));
            order.setTransportRequestList(
                    cmdList.stream().map(this::toTransportRequest).collect(Collectors.toList())
            );

            result.add(order);
        }

        return result;

    }

    default List<TransportOrder> arrivalCmdToDomains(List<TransportOrderArrivalCmd> cmds) {
        if (cmds == null || cmds.isEmpty()) {
            return Collections.emptyList();
        }

        // 分组：toId -> List<cmd>
        Map<Integer, List<TransportOrderArrivalCmd>> grouped = cmds.stream()
                .collect(Collectors.groupingBy(TransportOrderArrivalCmd::getToId));

        List<TransportOrder> result = new ArrayList<>();

        for (Map.Entry<Integer, List<TransportOrderArrivalCmd>> entry : grouped.entrySet()) {
            Integer toId = entry.getKey();
            List<TransportOrderArrivalCmd> cmdList = entry.getValue();

            TransportOrder order = new TransportOrder(new TransportOrderId(toId));
            order.setTransportRequestList(
                    cmdList.stream().map(this::toTransportRequest).collect(Collectors.toList())
            );

            result.add(order);
        }

        return result;
    }

    // 将一组cmd转换为TransportOrder
    default List<TransportOrder> toDomains(List<TransportOrderDepartureCmd> cmds) {
        if (cmds == null || cmds.isEmpty()) {
            return Collections.emptyList();
        }

        // 分组：toId -> List<cmd>
        Map<Integer, List<TransportOrderDepartureCmd>> grouped = cmds.stream()
                .collect(Collectors.groupingBy(TransportOrderDepartureCmd::getToId));

        List<TransportOrder> result = new ArrayList<>();

        for (Map.Entry<Integer, List<TransportOrderDepartureCmd>> entry : grouped.entrySet()) {
            Integer toId = entry.getKey();
            List<TransportOrderDepartureCmd> cmdList = entry.getValue();

            TransportOrder order = new TransportOrder(new TransportOrderId(toId));
            order.setTransportRequestList(
                    cmdList.stream().map(this::toTransportRequest).collect(Collectors.toList())
            );

            result.add(order);
        }

        return result;
    }

    // 支持嵌套类 TransportRequestDeliveryItem -> TransportRequest (或你实际的实体类名)
    List<TransportRequest> mapTrItems(List<TransportOrderHandoverCmd.TransportRequestDeliveryItem> items);

    @Mappings({
            @Mapping(target = "id.id", source = "trId"),
            @Mapping(target = "trackingNo", source = "trackingNo"),
            @Mapping(target = "deliveryTime", source = "deliveryTime")
    })
    TransportRequest mapTrItem(TransportOrderHandoverCmd.TransportRequestDeliveryItem item);
}
