package com.renpho.erp.tms.adapter.web.controller.transportrequest.cmd.validator;

import com.renpho.erp.tms.adapter.web.controller.command.validator.IdExist;
import com.renpho.erp.tms.adapter.web.controller.command.validator.IdsExist;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.container.TrIdContainer;
import com.renpho.erp.tms.adapter.web.controller.transportrequest.cmd.container.TrIdsContainer;
import com.renpho.erp.tms.domain.transportrequest.TransportRequest;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestId;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestOrderLookup;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.collections4.CollectionUtils;

import java.lang.annotation.Annotation;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/25
 */
public abstract class TransportRequestValidator<A extends Annotation, T> implements ConstraintValidator<A, T> {

    protected Predicate<T> predicate;

    @Resource
    protected TransportRequestOrderLookup transportRequestOrderLookup;

    @Override
    public boolean isValid(T value, ConstraintValidatorContext context) {
        return transportRequestOrderLookup == null || predicate.test(value);
    }

    public static class IsIdExist extends TransportRequestValidator<IdExist, TrIdContainer> {
        @Override
        public void initialize(IdExist constraintAnnotation) {
            predicate = cmd -> Optional.ofNullable(cmd)
                    .map(TrIdContainer::getId)
                    .map(TransportRequestId::new)
                    .map(id -> {
                        try {
                            return transportRequestOrderLookup.findById(id);
                        } catch (Exception e) {
                            return null;
                        }
                    })
                    .isPresent();
        }
    }

    public static class IsIdsExist extends TransportRequestValidator<IdsExist, TrIdsContainer<Collection<Integer>>> {

        @Override
        public void initialize(IdsExist constraintAnnotation) {
            predicate = cmd -> {
                List<TransportRequestId> ids = Optional.ofNullable(cmd)
                        .map(TrIdsContainer::getIds)
                        .stream()
                        .flatMap(Collection::stream)
                        .map(TransportRequestId::new)
                        .toList();
                List<TransportRequest> domains = transportRequestOrderLookup.findByIds(ids);
                if (CollectionUtils.isEmpty(domains)) {
                    return false;
                }
                return domains.stream().map(TransportRequest::getId).collect(Collectors.toSet()).containsAll(ids);
            };
        }

    }
}
