package com.renpho.erp.tms.adapter.web.controller.shippingompany.vo.converter;

import com.renpho.erp.tms.adapter.web.controller.shippingompany.vo.ShippingCompanyVO;
import com.renpho.erp.tms.domain.shippingcompany.ShippingCompany;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/16
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE,
        componentModel = MappingConstants.ComponentModel.SPRING)
public interface ShippingCompanyVOConverter {

    @Mapping(target = "id", source = "id.id")
    ShippingCompanyVO toVO(ShippingCompany shippingCompany);

    List<ShippingCompanyVO> toVOs(Collection<ShippingCompany> shippingCompany);
}
