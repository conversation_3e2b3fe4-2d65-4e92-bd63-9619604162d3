package com.renpho.erp.tms.adapter.web.controller.transportorder.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/6/10
 */
@Data
public class TransportOrderApprovalVO implements VO {

    @JsonIgnore
    private Integer id;

    /**
     * 流程实例ID
     */
    private String instanceId;

    /**
     * 流程实例业务编码
     */
    private String instanceBizCode;

    /**
     * 审批结果
     */
    @Trans(type = TransType.DICTIONARY, key = "TO_APPROVAL_STATUS", ref = "approvalResultName")
    private String approvalResult;

    private String approvalResultName;

    /**
     * 审批结果说明
     */
    private String remark;
}
