package com.renpho.erp.tms.adapter.web.controller.firstleg.transittime.vo.converter;

import com.renpho.erp.tms.adapter.web.controller.firstleg.transittime.vo.FirstLegCountryRegionDetailVO;
import com.renpho.erp.tms.adapter.web.controller.firstleg.transittime.vo.FirstLegCountryRegionExcelExportVO;
import com.renpho.erp.tms.adapter.web.controller.firstleg.transittime.vo.FirstLegCountryRegionTransitTimeVO;
import com.renpho.erp.tms.adapter.web.controller.firstleg.transittime.vo.FirstLegCountryRegionVO;
import com.renpho.erp.tms.domain.firstleg.transittime.FirstLegCountryRegion;
import com.renpho.erp.tms.domain.firstleg.transittime.FirstLegCountryRegionTransitTime;
import com.renpho.erp.tms.infrastructure.common.converter.StatusConverter;
import com.renpho.erp.tms.infrastructure.remote.user.converter.OperatorConverter;
import com.renpho.karma.dto.Paging;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/21
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE,
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {StatusConverter.class, OperatorConverter.class})
public interface FirstLegCountryRegionVOConverter {
    @Mapping(target = "id", source = "id.id")
    @Mapping(target = "modeId", source = "firstLegMode.id.id")
    @Mapping(target = "firstLegMode", source = "firstLegMode.name")
    @Mapping(target = "country", source = "countryRegion.fullName")
    @Mapping(target = "countryCode", source = "countryRegion.code")
    @Mapping(target = "createBy", source = "created.operatorId.id")
    @Mapping(target = "creator", source = "created", qualifiedByName = "concatOperatorInfo")
    @Mapping(target = "createTime", source = "created.operateTime")
    @Mapping(target = "updateBy", source = "updated.operatorId.id")
    @Mapping(target = "updater", source = "updated", qualifiedByName = "concatOperatorInfo")
    @Mapping(target = "updateTime", source = "updated.operateTime")
    FirstLegCountryRegionVO toVO(FirstLegCountryRegion domain);

    List<FirstLegCountryRegionVO> toVOs(Collection<FirstLegCountryRegion> list);

    Paging<FirstLegCountryRegionVO> toPageVOs(Paging<FirstLegCountryRegion> domains);

    @Mapping(target = "id", source = "id.id")
    @Mapping(target = "modeId", source = "firstLegMode.id.id")
    @Mapping(target = "firstLegMode", source = "firstLegMode.name")
    @Mapping(target = "country", source = "countryRegion.fullName")
    @Mapping(target = "countryCode", source = "countryRegion.code")
    @Mapping(target = "countryRegionVOS", source = "transitTimes")
    FirstLegCountryRegionDetailVO toDetailVO(FirstLegCountryRegion data);

    @Mapping(target = "id", source = "id.id")
    FirstLegCountryRegionTransitTimeVO toVO(FirstLegCountryRegionTransitTime transitTime);

    List<FirstLegCountryRegionTransitTimeVO> toVOS(Collection<FirstLegCountryRegionTransitTime> transitTimes);

    @Mapping(target = "id", source = "id.id")
    @Mapping(target = "firstLegMode", source = "firstLegMode.name")
    @Mapping(target = "country", source = "countryRegion.fullName")
    @Mapping(target = "countryCode", source = "countryRegion.code")
    FirstLegCountryRegionExcelExportVO toExportVO(FirstLegCountryRegion domain);

    List<FirstLegCountryRegionExcelExportVO> toExportVOs(Collection<FirstLegCountryRegion> domains);
}
