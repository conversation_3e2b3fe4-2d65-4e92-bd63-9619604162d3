package com.renpho.erp.tms.adapter.web.controller.customsinfo.logger;

import com.renpho.erp.log.repository.annotation.LogRecord;
import com.renpho.erp.log.repository.support.LogRecordContextHolder;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.ListQuery;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.vo.PskuCustomsInfoDownloadExcel;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.vo.PskuCustomsInfoVo;
import com.renpho.erp.tms.infrastructure.common.oplog.LogModule;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/28
 */
@Component
public class PskuCustomsInfoLogger {

    @LogRecord(module = LogModule.CUSTOMS_INFO_LOG_MODULE, type = LogModule.CommonDesc.UPLOAD_OPERATOR, desc = LogModule.CommonDesc.UPLOAD_DESC)
    public void logUploadRecords(List<PskuCustomsInfoVo> vos) {
        for (PskuCustomsInfoVo vo : vos) {
            LogRecordContextHolder.putRecordData(String.valueOf(vo.getId()), null, vo);
        }

    }

    @LogRecord(module = LogModule.CUSTOMS_INFO_LOG_MODULE, type = LogModule.CommonDesc.DOWNLOAD_OPERATOR, desc = LogModule.CommonDesc.DOWNLOAD_DESC)
    public void logDownloadRecords(ListQuery query, List<PskuCustomsInfoDownloadExcel> vos) {
        for (PskuCustomsInfoDownloadExcel vo : vos) {
            LogRecordContextHolder.putRecordData(String.valueOf(vo.getId()), null, vo);
        }
    }
}
