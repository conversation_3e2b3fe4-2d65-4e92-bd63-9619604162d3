package com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.command.validator;

import com.renpho.erp.tms.adapter.web.controller.command.validator.IdExist;
import com.renpho.erp.tms.adapter.web.controller.command.validator.NameNotExist;
import com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.command.ShippingFeeFormulaConfigIdCmd;
import com.renpho.erp.tms.domain.shippingfeeformula.*;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.ClassUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.annotation.Annotation;
import java.util.Optional;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @since 2025/6/12
 */
public abstract class ShippingFeeFormulaValidator<A extends Annotation, T> implements ConstraintValidator<A, T> {

    @Resource
    protected ShippingFeeFormulaArgLookup shippingFeeFormulaArgLookup;

    @Resource
    protected ShippingFeeFormulaConfigLookup shippingFeeFormulaConfigLookup;

    protected Predicate<T> predicate;

    @Override
    public boolean isValid(T value, ConstraintValidatorContext context) {
        return shippingFeeFormulaConfigLookup == null || shippingFeeFormulaArgLookup == null || predicate.test(value);
    }

    public static class IsArgIdExist extends ShippingFeeFormulaValidator<IdExist, ShippingFeeFormulaIdContainer> {
        @Override
        public void initialize(IdExist constraintAnnotation) {
            predicate = cmd -> Optional.ofNullable(cmd)
                    .map(ShippingFeeFormulaIdContainer::getId)
                    .map(ShippingFeeFormulaArgId::new)
                    .flatMap(shippingFeeFormulaArgLookup::findById)
                    .isPresent();
        }
    }

    public static class IsConfigIdExist extends ShippingFeeFormulaValidator<IdExist, ShippingFeeFormulaConfigIdCmd> {
        @Override
        public void initialize(IdExist constraintAnnotation) {
            predicate = cmd -> Optional.ofNullable(cmd)
                    .map(ShippingFeeFormulaConfigIdCmd::getId)
                    .map(ShippingFeeFormulaConfigId::new)
                    .flatMap(shippingFeeFormulaConfigLookup::findById)
                    .isPresent();
        }
    }

    public static class IsNameNotExist extends ShippingFeeFormulaValidator<NameNotExist, ShippingFeeFormulaItemNameContainer> {
        @Override
        public void initialize(NameNotExist constraintAnnotation) {
            predicate = cmd -> {
                Optional<ShippingFeeFormulaItemNameContainer> optional = Optional.ofNullable(cmd);
                Optional<ShippingFeeFormulaArgQuery> query = optional.map(ShippingFeeFormulaItemNameContainer::getName)
                        .filter(StringUtils::isNotBlank)
                        .map(this::toQuery);
                optional.filter(c -> ClassUtils.isAssignable(c.getClass(), ShippingFeeFormulaIdContainer.class))
                        .map(ShippingFeeFormulaIdContainer.class::cast)
                        .map(ShippingFeeFormulaIdContainer::getId)
                        .map(ShippingFeeFormulaArgId::new)
                        .ifPresent(id -> query.ifPresent(q -> q.setNotId(id)));
                return query.filter(q -> shippingFeeFormulaArgLookup.findAll(q).isEmpty())
                        .isPresent();
            };
        }

        private ShippingFeeFormulaArgQuery toQuery(String name) {
            ShippingFeeFormulaArgQuery query = new ShippingFeeFormulaArgQuery();
            query.setName(name);
            return query;
        }

    }

    public static class IsArgNameNotExist extends ShippingFeeFormulaValidator<ArgNameNotExist, ShippingFeeFormulaItemArgNameContainer> {
        @Override
        public void initialize(ArgNameNotExist constraintAnnotation) {
            predicate = cmd -> {
                Optional<ShippingFeeFormulaItemArgNameContainer> optional = Optional.ofNullable(cmd);
                Optional<ShippingFeeFormulaArgQuery> query = optional.map(ShippingFeeFormulaItemArgNameContainer::getArgName)
                        .filter(StringUtils::isNotBlank)
                        .map(this::toQuery);
                optional.filter(c -> ClassUtils.isAssignable(c.getClass(), ShippingFeeFormulaIdContainer.class))
                        .map(ShippingFeeFormulaIdContainer.class::cast)
                        .map(ShippingFeeFormulaIdContainer::getId)
                        .map(ShippingFeeFormulaArgId::new)
                        .ifPresent(id -> query.ifPresent(q -> q.setNotId(id)));
                return query.filter(q -> shippingFeeFormulaArgLookup.findAll(q).isEmpty())
                        .isPresent();
            };
        }

        private ShippingFeeFormulaArgQuery toQuery(String argName) {
            ShippingFeeFormulaArgQuery query = new ShippingFeeFormulaArgQuery();
            query.setArgName(argName);
            return query;
        }

    }
}
