package com.renpho.erp.tms.adapter.web.controller.transportorder.vo.converter;

import com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.TransportOrderForwarderApprovalIdConfirmCmd;
import com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.TransportOrderForwarderApprovalSelectedConfirmCmd;
import com.renpho.erp.tms.adapter.web.controller.transportorder.vo.ShippingFeeFormulaCalculatorResultVo;
import com.renpho.erp.tms.adapter.web.controller.transportorder.vo.TransportOrderForwarderQuoteConfirmVo;
import com.renpho.erp.tms.adapter.web.controller.transportorder.vo.TransportOrderForwarderVo;
import com.renpho.erp.tms.adapter.web.controller.transportrequest.vo.converter.TransportRequestVOConverter;
import com.renpho.erp.tms.domain.shippingfeeformula.ShippingFeeFormulaCalculatorResult;
import com.renpho.erp.tms.domain.supplier.LogisticsSupplier;
import com.renpho.erp.tms.domain.transportorder.TransportOrderId;
import com.renpho.erp.tms.domain.transportorderforwarderapproval.TransportOrderFreightQuote;
import com.renpho.erp.tms.infrastructure.persistence.shippingfeeformula.converter.ShippingFeeFormulaArgConverter;
import com.renpho.erp.tms.infrastructure.persistence.shippingfeeformula.converter.ShippingFeeFormulaConfigConverter;
import com.renpho.erp.tms.infrastructure.persistence.transportorder.po.converter.TransportOrderConverter;
import com.renpho.erp.tms.infrastructure.persistence.transportorder.po.converter.TransportOrderFreightQuoteConverter;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.po.converter.TransportRequestConverter;
import com.renpho.erp.tms.infrastructure.remote.processinstance.converter.ProcessInstanceConverter;
import com.renpho.erp.tms.infrastructure.remote.srm.supplier.converter.LogisticsSupplierConverter;
import com.renpho.erp.tms.infrastructure.remote.user.converter.OperatorConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.util.*;
import java.util.stream.Collectors;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {TransportOrderConverter.class, TransportRequestConverter.class, TransportRequestVOConverter.class, TransportOrderVoConverter.class, ShippingFeeFormulaConfigConverter.class, ShippingFeeFormulaArgConverter.class, LogisticsSupplierConverter.class, TransportOrderFreightQuoteConverter.class, OperatorConverter.class, ProcessInstanceConverter.class})
public interface TransportOrderFreightQuoteVoConverter {

    default List<ShippingFeeFormulaCalculatorResultVo> toCalculateResultVos(Collection<ShippingFeeFormulaCalculatorResult> domains) {
        Map<TransportOrderId, List<ShippingFeeFormulaCalculatorResult>> tos = CollectionUtils.emptyIfNull(domains)
                .stream()
                .collect(Collectors.groupingBy(ShippingFeeFormulaCalculatorResult::getToId));
        List<ShippingFeeFormulaCalculatorResultVo> vos = new ArrayList<>();
        for (Map.Entry<TransportOrderId, List<ShippingFeeFormulaCalculatorResult>> entry : tos.entrySet()) {
            ShippingFeeFormulaCalculatorResultVo vo = new ShippingFeeFormulaCalculatorResultVo();
            Optional.ofNullable(entry.getKey()).map(TransportOrderId::id).ifPresent(vo::setToId);
            for (ShippingFeeFormulaCalculatorResult r : CollectionUtils.emptyIfNull(entry.getValue())) {
                vo.setToNo(r.getToNo());
                if (r.getIsIncludeTax()) {
                    vo.setAmount(r.getAmount());
                } else {
                    vo.setAmountNoTax(r.getAmount());
                }
            }
            vos.add(vo);
        }
        return vos;
    }

    default List<TransportOrderFreightQuote> toCommands(TransportOrderForwarderApprovalIdConfirmCmd cmd) {
        return cmd.getSelects().stream().map(s -> toCommand(cmd, s)).toList();
    }

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "instanceId", source = "root.processInstanceId")
    @Mapping(target = "forwarderCompanyId", source = "cmd.logisticsSupplierId")
    @Mapping(target = "forwarder.id", source = "cmd.logisticsSupplierId")
    TransportOrderFreightQuote toCommand(TransportOrderForwarderApprovalIdConfirmCmd root, TransportOrderForwarderApprovalSelectedConfirmCmd cmd);

    default Map<TransportOrderId, List<TransportOrderForwarderQuoteConfirmVo>> toConfirmVos(Collection<TransportOrderFreightQuote> domains) {
        Map<TransportOrderId, List<TransportOrderForwarderQuoteConfirmVo>> results = new HashMap<>();
        Map<TransportOrderId, List<TransportOrderFreightQuote>> groups = domains.stream().collect(Collectors.groupingBy(TransportOrderFreightQuote::getToId));
        for (List<TransportOrderFreightQuote> quotes : groups.values()) {
            // 是否已被选中
            Optional<TransportOrderFreightQuote> isAlreadyConfirm = CollectionUtils.emptyIfNull(quotes).stream().filter(TransportOrderFreightQuote::getStatus).findAny();
            // 已被选过的需要置灰
            Boolean isSelectable = isAlreadyConfirm.isEmpty();
            // 选中的是否包含税
            Boolean isIncludeTax = isAlreadyConfirm.map(TransportOrderFreightQuote::getIsIncludeTax).orElse(null);
            // 选中的货代
            Integer forwarderId = isAlreadyConfirm.map(TransportOrderFreightQuote::getForwarderCompanyId).map(LogisticsSupplier.LogisticsSupplierId::id).orElse(null);
            for (TransportOrderFreightQuote domain : quotes) {
                // 货代信息
                TransportOrderForwarderVo forwarderVo = toForwarderVo(domain);
                if (results.containsKey(domain.getToId())) {
                    Optional<TransportOrderForwarderVo> any = results.get(domain.getToId())
                            .stream()
                            .filter(Objects::nonNull)
                            .flatMap(vo -> vo.getForwarders().stream())
                            .filter(vo -> Objects.equals(vo.getForwarderId(), domain.getForwarderCompanyId().id()))
                            .findAny();
                    if (any.isPresent()) {
                        updateForwarderVo(any.get(), domain);
                    } else {
                        results.get(domain.getToId()).forEach(vo -> vo.getForwarders().add(forwarderVo));
                    }
                } else {
                    TransportOrderForwarderQuoteConfirmVo vo = toConfirmVo(domain);
                    results.put(domain.getToId(), new ArrayList<>(List.of(vo)));
                    vo.setForwarders(new ArrayList<>(List.of(forwarderVo)));
                    vo.setIsSelectable(isSelectable);
                    vo.setForwarderId(forwarderId);
                    vo.setIsIncludeTax(isIncludeTax);
                }
            }

        }
        for (List<TransportOrderForwarderQuoteConfirmVo> vos : results.values()) {
            for (TransportOrderForwarderQuoteConfirmVo vo : CollectionUtils.emptyIfNull(vos)) {
                for (TransportOrderForwarderVo forwarderVo : CollectionUtils.emptyIfNull(vo.getForwarders())) {
                    forwarderVo.setCommentNoTax(StringUtils.defaultIfBlank(forwarderVo.getCommentNoTax(), StringUtils.EMPTY));
                    forwarderVo.setCommentWithTax(StringUtils.defaultIfBlank(forwarderVo.getCommentWithTax(), StringUtils.EMPTY));
                }
            }
        }
        return results;
    }

    @Mapping(target = "id", source = "toId")
    @Mapping(target = "toNo", source = "toNo")
    @Mapping(target = "totalQty", source = "totalQty")
    @Mapping(target = "totalBoxQty", source = "totalBoxQty")
    @Mapping(target = "totalGrossWeight", source = "totalGrossWeight")
    @Mapping(target = "totalVolume", source = "totalVolume")
    @Mapping(target = "chargeWeight", source = "chargeWeight")
    @Mapping(target = "chargeWeightType", source = "chargeWeightType")
    @Mapping(target = "trs", source = "trs")
    TransportOrderForwarderQuoteConfirmVo toConfirmVo(TransportOrderFreightQuote domain);

    @Mapping(target = "forwarderId", source = "forwarder.id")
    @Mapping(target = "forwarderCode", source = "forwarder.supplierCode")
    @Mapping(target = "forwarderName", source = "forwarder.name")
    @Mapping(target = "forwarderShortName", source = "forwarder.shortName")
    @Mapping(target = "amountWithTax", source = "amount", conditionExpression = "java(domain.getIsIncludeTax())", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "commentWithTax", source = "comment", conditionExpression = "java(domain.getIsIncludeTax())", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_DEFAULT)
    @Mapping(target = "amountNoTax", source = "amount", conditionExpression = "java(!domain.getIsIncludeTax())", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "commentNoTax", source = "comment", conditionExpression = "java(!domain.getIsIncludeTax())", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_DEFAULT)
    TransportOrderForwarderVo toForwarderVo(TransportOrderFreightQuote domain);

    @Mapping(target = "amountWithTax", source = "amount", conditionExpression = "java(domain.getIsIncludeTax())", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "commentWithTax", source = "comment", conditionExpression = "java(domain.getIsIncludeTax())", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "amountNoTax", source = "amount", conditionExpression = "java(!domain.getIsIncludeTax())", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "commentNoTax", source = "comment", conditionExpression = "java(!domain.getIsIncludeTax())", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateForwarderVo(@MappingTarget TransportOrderForwarderVo vo, TransportOrderFreightQuote domain);

}