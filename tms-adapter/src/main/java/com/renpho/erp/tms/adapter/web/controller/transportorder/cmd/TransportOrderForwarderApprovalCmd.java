package com.renpho.erp.tms.adapter.web.controller.transportorder.cmd;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.renpho.erp.tms.adapter.web.controller.command.validator.IsLogisticsSupplierExist;
import com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.container.TransportOrderIdsContainer;
import com.renpho.erp.tms.domain.shippingfeeformula.ShippingFeeFormulaConfigType;
import com.renpho.erp.tms.domain.transportorder.TransportOrder;
import com.renpho.karma.dto.Command;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * TO 单提交货代审批请求参数
 *
 * <AUTHOR>
 * @since 2025/6/13
 */
@Getter
@Setter
public class TransportOrderForwarderApprovalCmd extends Command implements TransportOrderIdsContainer<Collection<Integer>> {

    @Serial
    private static final long serialVersionUID = 6169046036191010453L;

    /**
     * TO 单 ID 数组
     */
    @NotEmpty
    @Valid
    private Set<@Valid @NotNull Integer> ids;

    @JsonIgnore
    private List<TransportOrder> tos;

    /**
     * 报价公式类型: FCL-整柜, LCL-散货
     */
    @NotNull
    private ShippingFeeFormulaConfigType type;

    /**
     * 货代报价信息
     */
    @NotEmpty
    @Valid
    private List<@Valid @NotNull @IsLogisticsSupplierExist ForwarderWithShippingFeeCmd> forwarderWithShippingFee;

    @Override
    @JsonIgnore
    public void setTos(Collection<TransportOrder> tos) {
        this.tos = new ArrayList<>(tos);
    }

}
