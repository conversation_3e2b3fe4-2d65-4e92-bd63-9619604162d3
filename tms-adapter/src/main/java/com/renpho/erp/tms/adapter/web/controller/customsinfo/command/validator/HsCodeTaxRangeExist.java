package com.renpho.erp.tms.adapter.web.controller.customsinfo.command.validator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * <AUTHOR>
 * @since 2025/4/25
 */
@Documented
@Constraint(validatedBy = {HsCodeTaxRangeValidator.IsHsCodeTaxRangeExist.class})
@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
@Retention(RUNTIME)
public @interface HsCodeTaxRangeExist {

    String message() default "{error.at_least_1_hs_code}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
