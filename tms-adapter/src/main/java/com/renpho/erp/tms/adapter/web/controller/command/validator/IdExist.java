package com.renpho.erp.tms.adapter.web.controller.command.validator;

import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.validator.PskuCustomsInfoValidator;
import com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.command.validator.ShippingFeeFormulaValidator;
import com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.validator.TransportOrderFreightQuoteValidator;
import com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.validator.TransportOrderValidator;
import com.renpho.erp.tms.adapter.web.controller.transportrequest.cmd.validator.TransportRequestValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * <AUTHOR>
 * @since 2025/3/17
 */
@Documented
@Constraint(validatedBy = {PskuCustomsInfoValidator.IsIdExist.class, TransportRequestValidator.IsIdExist.class, ShippingFeeFormulaValidator.IsArgIdExist.class, ShippingFeeFormulaValidator.IsConfigIdExist.class, TransportOrderFreightQuoteValidator.IsIdExist.class, ProcessInstanceValidator.IsIdExist.class, TransportOrderValidator.IsIdExist.class})
@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
@Retention(RUNTIME)
public @interface IdExist {

    String message() default "{id.not.exist}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
