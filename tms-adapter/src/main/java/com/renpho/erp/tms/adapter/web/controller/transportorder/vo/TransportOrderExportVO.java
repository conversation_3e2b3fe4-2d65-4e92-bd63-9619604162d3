package com.renpho.erp.tms.adapter.web.controller.transportorder.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fhs.core.trans.vo.VO;
import com.github.houbb.sensitive.annotation.Sensitive;
import com.renpho.erp.data.sensitive.SensitiveDynamicCondition;
import com.renpho.erp.data.sensitive.SensitiveDynamicStrategy;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class TransportOrderExportVO implements VO {

    @ExcelIgnore
    private Integer id;

    /**
     * TO单号
     */
    @ExcelProperty(value = "export.to.to-no", index = 0)
    private String toNo;

    /**
     * 采购单号
     */
    @ExcelProperty(value = "export.to.po-no", index = 1)
    private String poNo;

    /**
     * 物流计划单号（TR单号）
     */
    @ExcelProperty(value = "export.to.tr-no", index = 2)
    private String trNo;

    /**
     * PSKU
     */
    @ExcelProperty(value = "export.to.psku", index = 3)
    private String psku;

    /**
     * PSKU名称
     */
    @ExcelProperty(value = "export.to.psku-name", index = 4)
    private String pskuName;

    /**
     * 型号
     */
    @ExcelProperty(value = "export.to.model", index = 5)
    private String model;

    /**
     * 品牌
     */
    @ExcelProperty(value = "export.to.brand", index = 6)
    private String brand;

    /**
     * 总数量（pcs）
     */
    @ExcelProperty(value = "export.to.total-quantity", index = 7)
    private Integer totalQty;

    /**
     * 总箱数（ctns）
     */
    @ExcelProperty(value = "export.to.total-cartons", index = 8)
    private Integer totalBoxQty;

    /**
     * 总毛重（KG）
     */
    @ExcelProperty(value = "export.to.total-gross-weight", index = 9)
    private BigDecimal totalGrossWeight;

    /**
     * 总净重（KG）
     */
    @ExcelProperty(value = "export.to.total-net-weight", index = 10)
    private BigDecimal totalNetWeight;

    /**
     * 总体积（CBM）
     */
    @ExcelProperty(value = "export.to.total-volume", index = 11)
    private BigDecimal totalVolume;

    /**
     * 敏感属性
     */
    @ExcelProperty(value = "export.to.sensitive-property", index = 12)
    private String hazardousPros;

    /**
     * 目的国/地区
     */
    @ExcelProperty(value = "export.to.destination-country", index = 13)
    private String destCountry;

    /**
     * 目的仓Code
     */
    @ExcelProperty(value = "export.to.destination-warehouse-code", index = 14)
    private String destWarehouseCode;

    /**
     * 目的地
     */
    @ExcelProperty(value = "export.to.destination", index = 15)
    private String destination;

    /**
     * 头程方式
     */
    @ExcelProperty(value = "export.to.first-leg-mode", index = 16)
    private String firstLegModeName;

    /**
     * 外箱尺寸（cm）
     */
    @ExcelProperty(value = "export.to.carton-dimension", index = 17)
    private String cartonDimension;

    /**
     * 供应商ID
     */
    @ExcelProperty(value = "export.to.vendor-id", index = 18)
    private String purchaseSupplierId;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "export.to.vendor-name", index = 19)
    @Sensitive(permission = "SUPPLIER:NAME", condition = SensitiveDynamicCondition.class,
            strategy = SensitiveDynamicStrategy.class)
    private String purchaseSupplierName;

    /**
     * ShipmentID
     */
    @ExcelProperty(value = "export.to.shipment-id", index = 20)
    private String shipmentId;

    /**
     * ReferenceID
     */
    @ExcelProperty(value = "export.to.reference-id", index = 21)
    private String referenceId;

    /**
     * 运单号
     */
    @ExcelProperty(value = "export.to.waybill-no", index = 22)
    private String trackingNo;

    /**
     * HS CODE
     */
    @ExcelProperty(value = "export.to.hs-code", index = 23)
    private String hsCode;

    /**
     * FNSKU
     */
    @ExcelProperty(value = "export.to.fnsku", index = 24)
    private String fnSku;

    /**
     * 海关申报单价
     */
    @ExcelProperty(value = "export.to.customs-unit-price", index = 25)
    private BigDecimal customsUnitPrice;

    /**
     * 币种
     */
    @ExcelProperty(value = "export.to.currency", index = 26)
    private String currency;

    /**
     * 图片
     */
    @ColumnWidth(20) // 设置列宽以适应图片
    @ExcelProperty(value = "export.to.image", index = 27)
    private byte[] image;

    /**
     * 材质
     */
    @ExcelProperty(value = "export.to.material", index = 28)
    private String material;

    /**
     * 销售链接
     */
    @ExcelProperty(value = "export.to.sales-link", index = 29)
    private String salesLink;
}

