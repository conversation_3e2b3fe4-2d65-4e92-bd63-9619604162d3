package com.renpho.erp.tms.adapter.stream.transportrequest;

import com.renpho.erp.stream.StreamCommonConstants;
import com.renpho.erp.tms.adapter.stream.transportrequest.vo.CancelShipmentOrderMsgVO;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestId;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.stereotype.Component;

/**
 * 取消入库单
 * <AUTHOR>
 * @since 2025/7/19
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class CancelShipmentOrderProducer {

    private final StreamBridge streamBridge;

    @Value("${spring.cloud.stream.bindings.cancelShipmentOrderProducer-out-0.destination}")
    private String cancelShipmentOrderTopic;


    /**
     * 取消入库单
     * @param shipmentId 入库单号
     * @param warehouseProviderType 服务商
     */
    public void cancelShipmentOrder(TransportRequestId trId, String shipmentId, WarehouseProviderType warehouseProviderType,
                                    Boolean isAmazonTr){
        log.info("发送取消入库单消息。 shipmentId: {}, 服务商名称（tag）: {}", shipmentId, warehouseProviderType.name());
        CancelShipmentOrderMsgVO msg = CancelShipmentOrderMsgVO.builder()
                .shipmentId(shipmentId)
                .isAmazonTr(isAmazonTr)
                .trId(trId.id())
                .warehouseProviderType(warehouseProviderType).build();

        streamBridge.send(cancelShipmentOrderTopic, MessageBuilder.withPayload(msg)
                .setHeader(StreamCommonConstants.HEADER_TAGS, warehouseProviderType.name())
                .build());
    }
}
