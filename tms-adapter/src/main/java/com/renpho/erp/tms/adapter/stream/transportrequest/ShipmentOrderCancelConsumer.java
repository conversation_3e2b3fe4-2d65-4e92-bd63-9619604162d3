package com.renpho.erp.tms.adapter.stream.transportrequest;

import com.alibaba.fastjson2.JSONObject;
import com.renpho.erp.stream.StreamCommonConstants;
import com.renpho.erp.tms.adapter.stream.transportrequest.vo.CancelShipmentOrderMsgVO;
import com.renpho.erp.tms.application.transportrequest.TransportRequestQueryService;
import com.renpho.erp.tms.application.transportrequest.YunWmsService;
import com.renpho.erp.tms.domain.exception.BusinessException;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transportrequest.TransportRequest;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestId;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.function.Consumer;

/**
 * 取消入库单消费者
 *
 * <AUTHOR>
 * @since 2025/7/19
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ShipmentOrderCancelConsumer {
    private final YunWmsService yunWmsService;
    private final TransportRequestQueryService transportRequestQueryService;

    @Bean
    public Consumer<Message<String>> cancelShipmentOrderConsumer() {
        return msg -> {
            String tag = Optional.ofNullable(msg.getHeaders().get(StreamCommonConstants.HEADER_TAGS))
                    .map(String::valueOf)
                    .filter(StringUtils::isNotBlank)
                    .orElseThrow(() -> new BusinessException("error.tr.tag-not-found"));
            String content = msg.getPayload();
            CancelShipmentOrderMsgVO msgVO = JSONObject.parseObject(content, CancelShipmentOrderMsgVO.class);
            log.info("----------接收到取消入库单的消息: tag=[{}], content=[{}], body=[{}]", tag, content, msg);
            WarehouseProviderType warehouseProviderType = WarehouseProviderType.fromName(tag);
            if (warehouseProviderType == null) {
                log.error("----------不合法的仓库服务商类型, name={}", tag);
                return;
            }

            TransportRequest tr = transportRequestQueryService.findDetailById(new TransportRequestId(msgVO.getTrId()));

            switch (warehouseProviderType) {
                case WMS -> {
                    //todo 调用取消入库预约单方法
//                    log.info("取消入库单: shipmentId=[{}]", shipmentId);
//                    wmsTransportRequestService.cancelShipmentOrder(shipmentId, warehouseProviderType);
                }
                case POLARIS_YUNWMS -> {
                    if (msgVO.getIsAmazonTr()) {
                        yunWmsService.cancelTransferOrder(tr);
                    } else {
                        yunWmsService.cancelAsn(tr);
                    }
                }
                default -> log.error("未知的仓库类型：{}", warehouseProviderType.name());
            }
        };
    }
}
