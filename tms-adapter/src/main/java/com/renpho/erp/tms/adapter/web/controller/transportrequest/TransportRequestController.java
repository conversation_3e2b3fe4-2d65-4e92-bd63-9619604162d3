/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.12.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.renpho.erp.tms.adapter.web.controller.transportrequest;


import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.extra.spring.SpringUtil;
import com.fhs.core.trans.anno.TransMethodResult;
import com.fhs.trans.service.impl.TransService;
import com.renpho.erp.data.sensitive.SensitiveMethodResult;
import com.renpho.erp.log.repository.annotation.LogRecord;
import com.renpho.erp.log.repository.support.LogRecordContextHolder;
import com.renpho.erp.security.annotation.Inner;
import com.renpho.erp.tms.adapter.web.controller.command.validator.FileAlreadyUpload;
import com.renpho.erp.tms.adapter.web.controller.command.validator.IdExist;
import com.renpho.erp.tms.adapter.web.controller.command.validator.IdsExist;
import com.renpho.erp.tms.adapter.web.controller.transportrequest.cmd.*;
import com.renpho.erp.tms.adapter.web.controller.transportrequest.cmd.converter.TransportRequestCmdConverter;
import com.renpho.erp.tms.adapter.web.controller.transportrequest.vo.TransportRequestDetailVO;
import com.renpho.erp.tms.adapter.web.controller.transportrequest.vo.TransportRequestExportVO;
import com.renpho.erp.tms.adapter.web.controller.transportrequest.vo.TransportRequestVO;
import com.renpho.erp.tms.adapter.web.controller.transportrequest.vo.converter.TransportRequestVOConverter;
import com.renpho.erp.tms.application.transportrequest.PushTaskQueryService;
import com.renpho.erp.tms.application.transportrequest.TransportRequestQueryService;
import com.renpho.erp.tms.application.transportrequest.TransportRequestService;
import com.renpho.erp.tms.application.transportrequest.stream.TransportRequestProducer;
import com.renpho.erp.tms.client.transportrequest.request.ListTrQuery;
import com.renpho.erp.tms.client.transportrequest.response.TransportRequestClientVo;
import com.renpho.erp.tms.client.transportrequest.response.TransportRequestProductVo;
import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.domain.transportrequest.dto.TransportRequestSignInDoListRequest;
import com.renpho.erp.tms.domain.transportrequest.dto.TransportRequestSignInRequest;
import com.renpho.erp.tms.domain.transportrequest.dto.TransportRequestSignInResponse;
import com.renpho.erp.tms.infrastructure.common.oplog.LogModule;
import com.renpho.erp.tms.infrastructure.util.Pdfkit;
import com.renpho.erp.tms.infrastructure.util.cartonlabel.CartonLabelGenerator;
import com.renpho.erp.tms.infrastructure.util.excel.ExcelUtil;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Generated;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * TR单接口
 *
 * <AUTHOR>
 * @since 2025/5/19
 */
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-05-19T18:06:01.197175700+08:00[Asia/Shanghai]", comments = "Generator version: 7.12.0")
@Validated
@RestController
@Tag(name = "transport request", description = "TR单接口")
@RequestMapping("/transport/request")
@ShenyuSpringCloudClient("/transport/request/**")
@RequiredArgsConstructor
public class TransportRequestController {

    private final TransportRequestQueryService transportRequestQueryService;
    private final TransportRequestCmdConverter transportRequestCmdConverter;
    private final TransportRequestVOConverter transportRequestVOConverter;
    private final TransService transService;
    private final TransportRequestService transportRequestService;
    private final TransportRequestProducer transportRequestProducer;
    private final PushTaskQueryService pushTaskQueryService;

    /**
     * 分页查询TR单
     */
    @Operation(operationId = "pageTransportRequest", description = "分页查询TR单", tags = {"transport request"})
    @PostMapping("/page")
    @PreAuthorize("hasPermission('tms:transportRequest:list')")
    @TransMethodResult
    public R<Paging<TransportRequestVO>> pageTransportRequest(@RequestBody(required = false) ListQuery listQuery) {
        TransportRequestListQuery domain = transportRequestCmdConverter.toPageDomain(listQuery);
        Paging<TransportRequest> domains = transportRequestQueryService.pageList(domain);
        if (CollectionUtils.isEmpty(domains.getRecords())) {
            return R.success(Paging.of(List.of(), 0, listQuery.getPageSize(), listQuery.getPageIndex()));
        }

        Map<String, PushTask> failures = pushTaskQueryService.findLastFailureByBizNo(domains.getRecords());

        Paging<TransportRequestVO> vos = transportRequestVOConverter.toPageListVOs(domains);
        List<TransportRequestVO> voList = domains.getRecords()
                .stream()
                .map(e -> SpringUtil.getBean(this.getClass()).dataDesensitization(e))
                .peek(e -> Optional.ofNullable(failures.get(e.getTrNo())).map(PushTask::getErrMsg).ifPresent(e::setLastErrMsg))
                .toList();
        vos.setRecords(voList);
        return R.success(vos);
    }

    /**
     * TR单-签收-列表
     */
    @Operation(operationId = "signInListTransportRequest", description = "TR单签收-列表", tags = {"transport request sign in list"})
    @PostMapping("/sign/in/list")
    @PreAuthorize("hasPermission('tms:transportRequest:markRecevied')")
    public R<List<TransportRequestSignInResponse>> signInList(@Valid @RequestBody SignInCmd signInCmd) {
        TransportRequestSignInRequest request = transportRequestCmdConverter.toSignInRequest(signInCmd);
        return R.success(transportRequestService.signInList(request));
    }

    /**
     * TR单-签收-执行
     */
    @Operation(operationId = "signInDoTransportRequest", description = "TR单签收-执行", tags = {"do transport request sign in"})
    @PostMapping("/sign/in/do")
    @PreAuthorize("hasPermission('tms:transportRequest:markRecevied')")
    public R<Boolean> signIn(@Valid @RequestBody SignInDoListCmd signInDoListCmd) {
        TransportRequestSignInDoListRequest request = transportRequestCmdConverter.toSignInDoListRequest(signInDoListCmd);
        return R.success(transportRequestService.signInDo(request));
    }

    /**
     * 数据脱敏（坑：只能简单对象，不能被page或R包裹）
     *
     * @param domain tr单
     * @return 脱敏tr单
     */
    @SensitiveMethodResult
    public TransportRequestVO dataDesensitization(TransportRequest domain) {
        return transportRequestVOConverter.toPageListVO(domain);
    }


    /**
     * 查询TR单详情
     */
    @Operation(operationId = "detailTransportRequest", description = "查询TR单详情", tags = {"transport request"})
    @PostMapping("/detail")
    @PreAuthorize("hasPermission('tms:transportRequest:detail')")
    @TransMethodResult
    public R<TransportRequestDetailVO> detailTransportRequest(@Valid @RequestBody IdQuery idQuery) {
        TransportRequestId domain = transportRequestCmdConverter.toDomain(idQuery);
        TransportRequest transportRequest = transportRequestQueryService.findDetailById(domain);
        TransportRequestDetailVO vo = SpringUtil.getBean(this.getClass()).detailDataDesensitization(transportRequest);
        return R.success(vo);
    }

    /**
     * 数据脱敏（坑：只能简单对象，不能被page或R包裹）
     *
     * @param domain tr单
     * @return 脱敏tr单
     */
    @SensitiveMethodResult
    public TransportRequestDetailVO detailDataDesensitization(TransportRequest domain) {
        TransportRequestDetailVO detailVO = transportRequestVOConverter.toDetailVO(domain);
        TransportRequestProductVo productVo = transportRequestVOConverter.toTransportRequestProductVo(domain.getItem());
        productVo.setPicture(domain.getProduct().getPicture());
        productVo.setTotalVolume(domain.getTotalBoxVolume());
        productVo.setTotalGrossWeight(domain.getTotalGrossWeight());
        productVo.setTotalNetWeight(domain.getTotalNetWeight());
        productVo.setTotalQty(domain.getQuantity());
        productVo.setTotalBoxes(Optional.ofNullable(domain.getBoxQty()).map(d -> d.setScale(0, RoundingMode.HALF_UP)).orElse(BigDecimal.ZERO).intValue());
        detailVO.setProduct(productVo);
        return detailVO;
    }

    /**
     * 作废TR单
     */
    @Operation(operationId = "cancelTransportRequest", description = "作废TR单", tags = {"transport request"})
    @PostMapping("/cancel")
    public R<List<Integer>> cancelTransportRequest(@Valid @RequestBody PdNosCmd pdNosCmd) {
        throw new IllegalArgumentException("Not implemented");
    }

    /**
     * 拼柜
     */
    @Operation(operationId = "consolidationTransportRequest", description = "拼柜", tags = {"transport request"})
    @PostMapping("/consolidation")
    @PreAuthorize("hasPermission('tms:transportRequest:consolidation')")
    public R<String> consolidation(@Valid @RequestBody @IdsExist IdsCmd idsCmd) {
        List<TransportRequestId> trIds = transportRequestCmdConverter.toIds(idsCmd.getIds());
        transportRequestService.consolidation(trIds);
        return R.success();
    }

    /**
     * 上传入库单
     */
    @Operation(operationId = "uploadInboundOrder", description = "上传入库单", tags = {"transport request"})
    @PostMapping("/inbound_order/upload")
    @LogRecord(module = LogModule.TRANSPORT_REQUEST, type = LogModule.TransportRequest.UPLOAD_INBOUND_ORDER_NO_OPERATOR, desc = LogModule.TransportRequest.UPLOAD_INBOUND_ORDER_NO_DESC)
    @PreAuthorize("hasPermission('tms:transportRequest:uploadOrder')")
    public R<Integer> uploadInboundOrder(@Valid @RequestBody @IdExist @FileAlreadyUpload UploadInboundOrderCmd cmd) {
        TransportRequest command = transportRequestCmdConverter.toDomain(cmd);
        TransportRequestDetailVO oldData = transportRequestVOConverter.toDetailVO(transportRequestQueryService.findDetailById(command.getId()));
        TransportRequestId trId = transportRequestService.updateInboundOrder(command);
        TransportRequest domain = transportRequestQueryService.findDetailById(trId);
        TransportRequestDetailVO newData = transportRequestVOConverter.toDetailVO(domain);
        LogRecordContextHolder.putRecordData(String.valueOf(trId.id()), oldData, newData);
        transportRequestProducer.sendTrCartonFiles(domain);
        return R.success(trId.id());
    }

    /**
     * 导出TR单
     */
    @Operation(operationId = "downloadTransportRequest", description = "导出TR单", tags = {"transport request"})
    @PostMapping("/download")
    @PreAuthorize("hasPermission('tms:transportRequest:export')")
    @TransMethodResult
    public void downloadTransportRequest(@Valid @RequestBody(required = false) ListQuery listQuery,
                                         HttpServletResponse response) {
        TransportRequestListQuery query = transportRequestCmdConverter.toPageDomain(listQuery);
        //excel总行数
        Long totalCount = transportRequestQueryService.countExport(query);

        ExcelUtil.exportExcelBatch(totalCount,
                1000,
                "TR",
                TransportRequestExportVO.class,
                response,
                (pageNum, size) -> {
                    query.setPageIndex(pageNum);
                    query.setPageSize(size);

                    List<TransportRequest> datas = transportRequestQueryService.findPageList(query);
                    if (datas.isEmpty()) {
                        return null;
                    }

                    List<TransportRequestExportVO> vos = datas.stream()
                            .map(e -> SpringUtil.getBean(this.getClass()).excelDataDesensitization(e)).toList();
                    transService.transBatch(vos);
                    return vos;
                });

    }

    /**
     * 数据脱敏（坑：只能简单对象，不能被page或R包裹）
     *
     * @param data tr单
     * @return 脱敏tr单
     */
    @SensitiveMethodResult
    public TransportRequestExportVO excelDataDesensitization(TransportRequest data) {
        return transportRequestVOConverter.toTransportRequestExportVo(data);
    }

    /**
     * 统计TR单各状态数量
     */
    @PreAuthorize("hasPermission('tms:transportRequest:list')")
    @PostMapping("/countByStatus")
    public R<Map<Integer, Object>> countByStatus() {
        return R.success(transportRequestQueryService.countByStatus());
    }


    /**
     * 查验
     *
     * @param cmd 参数
     * @return toId to单Id
     */
    @PostMapping("/inspection")
    @PreAuthorize("hasPermission('tms:transportOrder:inspection')")
    @LogRecord(module = LogModule.TRANSPORT_ORDER, type = LogModule.CommonDesc.EDIT_OPERATOR,
            desc = LogModule.CommonDesc.EDIT_DESC)
    public R<List<Integer>> inspection(@Valid @RequestBody List<TransportOrderInspectionCmd> cmd) {
        List<TransportRequest> domains = transportRequestCmdConverter.inspectionCmdToDomains(cmd);
        List<Integer> trIds = transportRequestService.inspection(domains);

        return R.success(trIds);
    }


    @Inner
    @PostMapping("/getToOrderInfoByPdNoList")
    public R<List<TransportRequestClientVo>> getToOrderInfoByPdNoList(@RequestBody ListTrQuery query) {
        TransportRequestListQuery queryData = transportRequestCmdConverter.toQueryPdDomain(query);
        List<TransportRequest> domain = transportRequestQueryService.findByNos(queryData);
        List<TransportRequestClientVo> newData = transportRequestVOConverter.toClientVos(domain);
        return R.success(newData);
    }


    /**
     * 根据TR单导出箱唛
     *
     * @param idQuery  trId
     * @param response 文件流
     */
    @PostMapping("/exportBoxLabel")
    public void exportBoxLabel(@Valid @RequestBody IdQuery idQuery, HttpServletResponse response) {

        TransportRequestId domain = transportRequestCmdConverter.toDomain(idQuery);
        TransportRequest transportRequest = transportRequestQueryService.findDetailById(domain);
        CartonLabelData cartonLabelData = CartonLabelData.extractCartonLabelData(transportRequest);

        String filename = new StrBuilder().append(cartonLabelData.getTrNo()).append("-")
                .append(DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN))
                .append(".pdf").toStringAndReset();

        try (ByteArrayOutputStream baos = CartonLabelGenerator.cartonLabelGenerator(cartonLabelData)) {
            Pdfkit.writeFile(filename, baos, response);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

}
