package com.renpho.erp.tms.adapter.web.controller.transportorder.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fhs.core.trans.vo.VO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/7/1
 */
@Getter
@Setter
public class TransportOrderForwarderVo implements VO, Serializable {

    @Serial
    private static final long serialVersionUID = -7421941448389666863L;

    @JsonIgnore
    private Integer id;

    /**
     * 货代(物流供应商) ID
     */
    private Integer forwarderId;

    /**
     * 货代(物流供应商)编码
     */
    private String forwarderCode;

    /**
     * 货代(物流供应商)名称
     */
    private String forwarderName;

    /**
     * 货代(物流供应商)简称
     */
    private String forwarderShortName;

    /**
     * 不包税金额
     */
    private BigDecimal amountNoTax;

    /**
     * 包税金额
     */
    private BigDecimal amountWithTax;

    /**
     * 是否包税
     */
    private Boolean isIncludeTax;

    /**
     * 不包税批注
     */
    private String commentNoTax;

    /**
     * 包税批注
     */
    private String commentWithTax;

}

