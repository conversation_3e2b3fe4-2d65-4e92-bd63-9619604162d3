package com.renpho.erp.tms.adapter.web.controller.customsinfo.command;

import com.renpho.erp.tms.domain.customsinfo.PskuAdditionalDutyStatus;
import com.renpho.karma.timezone.annotation.TimeZoneIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Generated;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/4/22
 */
@Getter
@Setter
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-04-22T12:09:54.179410400+08:00[Asia/Shanghai]", comments = "Generator version: 7.12.0")
public class AdditionalDutyCmd implements Serializable {

    @Serial
    private static final long serialVersionUID = 4984736976179397184L;

    /**
     * 附加关税 ID
     */
    @Schema(name = "id", description = "附加关税 ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer id;

    /**
     * 税种
     */
    @Schema(name = "name", description = "税种", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{excel.additional_duty_name} {jakarta.validation.constraints.NotBlank.message}")
    @Size(max = 30, message = "{excel.additional_duty_name} {jakarta.validation.constraints.Size.message}")
    private String name;

    /**
     * 税率
     */
    @Schema(name = "dutyRate", description = "税率", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{excel.additional_duty_rate} {jakarta.validation.constraints.NotBlank.message}")
    @PositiveOrZero(message = "{excel.additional_duty_rate} {jakarta.validation.constraints.PositiveOrZero.message}")
    private BigDecimal dutyRate;

    /**
     * 生效时间-国家时区
     */
    @Schema(name = "effectiveTime", description = "生效时间-国家时区", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{excel.additional_duty_effect_time} {jakarta.validation.constraints.NotBlank.message}")
    @TimeZoneIgnore
    private LocalDateTime effectiveTime;

    /**
     * 生效状态
     */
    @Schema(name = "effectiveStatus", description = "生效状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{excel.additional_duty_effect_status} {jakarta.validation.constraints.NotBlank.message}")
    private PskuAdditionalDutyStatus effectiveStatus;

}

