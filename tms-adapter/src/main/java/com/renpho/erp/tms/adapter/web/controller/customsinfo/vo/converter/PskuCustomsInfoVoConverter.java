package com.renpho.erp.tms.adapter.web.controller.customsinfo.vo.converter;

import com.renpho.erp.tms.adapter.web.controller.customsinfo.vo.PskuCustomsAdditionalDutyVOConverter;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.vo.PskuCustomsInfoDownloadExcel;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.vo.PskuCustomsInfoVo;
import com.renpho.erp.tms.domain.customsinfo.PskuCustomsAdditionalDuty;
import com.renpho.erp.tms.domain.customsinfo.PskuCustomsInfo;
import com.renpho.erp.tms.infrastructure.persistence.customsinfo.PskuCustomsInfoConverter;
import com.renpho.erp.tms.infrastructure.persistence.customsinfo.po.PskuCustomsAdditionalDutyConverter;
import com.renpho.erp.tms.infrastructure.remote.product.converter.ProductConverter;
import com.renpho.erp.tms.infrastructure.remote.user.converter.OperatorConverter;
import com.renpho.karma.dto.Paging;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.*;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {PskuCustomsInfoConverter.class, PskuCustomsAdditionalDutyConverter.class, PskuCustomsAdditionalDutyVOConverter.class, OperatorConverter.class, ProductConverter.class})
public interface PskuCustomsInfoVoConverter {

    @Mapping(target = "id", source = "id")
    @Mapping(target = "productId", source = "product.id")
    @Mapping(target = "psku", source = "psku")
    @Mapping(target = "picture", source = "product.picture")
    @Mapping(target = "currencyCode", source = "currency.code")
    @Mapping(target = "countryName", source = "countryRegion.name")
    @Mapping(target = "createBy", source = "created.operatorId")
    @Mapping(target = "createByName", source = "created.operatorName")
    @Mapping(target = "createByCode", source = "created.operatorCode")
    @Mapping(target = "createTime", source = "created.operateTime")
    @Mapping(target = "updateBy", source = "updated.operatorId")
    @Mapping(target = "updateByName", source = "updated.operatorName")
    @Mapping(target = "updateByCode", source = "updated.operatorCode")
    @Mapping(target = "updateTime", source = "updated.operateTime")
    @BeanMapping(qualifiedByName = "sumDutyRate")
    PskuCustomsInfoVo toVo(PskuCustomsInfo domain);

    @AfterMapping
    @Named("sumDutyRate")
    default void sumDutyRate(@MappingTarget PskuCustomsInfoVo vo) {
        vo.sum();
    }

    @IterableMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    List<PskuCustomsInfoVo> toVos(Collection<PskuCustomsInfo> domain);

    @BeanMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    Paging<PskuCustomsInfoVo> toPagesVo(Paging<PskuCustomsInfo> domains);

    PskuCustomsInfoDownloadExcel toExcel(PskuCustomsInfo domain);

    PskuCustomsInfoDownloadExcel copy(PskuCustomsInfoDownloadExcel domain);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "name", source = "name")
    @Mapping(target = "dutyRate", source = "dutyRate")
    @Mapping(target = "effectiveTime", source = "effectiveTimeLocal")
    @Mapping(target = "effectiveTimePage", expression = "java(domain.toPageTime().orElse(null))")
    @Mapping(target = "additionalDutyStatus", source = "status")
    @Mapping(target = "status", ignore = true)
    PskuCustomsInfoDownloadExcel updateAdditionalDuty(PskuCustomsAdditionalDuty domain, @MappingTarget PskuCustomsInfoDownloadExcel excel);

    default List<PskuCustomsInfoDownloadExcel> toExcelWithAdditionalDuty(PskuCustomsInfo domain) {
        PskuCustomsInfoDownloadExcel excel = toExcel(domain);
        List<PskuCustomsInfoDownloadExcel> excels = new ArrayList<>();
        for (PskuCustomsAdditionalDuty additionalDuty : CollectionUtils.emptyIfNull(domain.getAdditionalDuties())) {
            PskuCustomsInfoDownloadExcel copy = copy(excel);
            excels.add(updateAdditionalDuty(additionalDuty, copy));
        }
        return CollectionUtils.isEmpty(excels) ? List.of(excel) : excels;
    }

    default List<PskuCustomsInfoDownloadExcel> toExcels(Collection<PskuCustomsInfo> domains) {
        return CollectionUtils.emptyIfNull(domains)
                .stream()
                .filter(Objects::nonNull).map(this::toExcelWithAdditionalDuty)
                .filter(Objects::nonNull).flatMap(Collection::stream)
                .toList();
    }

}