package com.renpho.erp.tms.adapter.web.controller.shippingfeeformula;

import com.fhs.core.trans.anno.TransMethodResult;
import com.renpho.erp.log.repository.annotation.LogRecord;
import com.renpho.erp.log.repository.support.LogRecordContextHolder;
import com.renpho.erp.tms.adapter.web.controller.command.validator.IdExist;
import com.renpho.erp.tms.adapter.web.controller.command.validator.NameNotExist;
import com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.command.AddShippingFeeFormulaArgCmd;
import com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.command.ShippingFeeFormulaArgIdCmd;
import com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.command.UpdateShippingFeeFormulaArgCmd;
import com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.command.UpdateShippingFeeFormulaConfigCmd;
import com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.command.converter.ShippingFeeFormulaConfigCommandConverter;
import com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.command.validator.ArgNameNotExist;
import com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.vo.ShippingFeeFormulaArgVo;
import com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.vo.ShippingFeeFormulaConfigVo;
import com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.vo.converter.ShippingFeeFormulaConfigVoConverter;
import com.renpho.erp.tms.application.shippingfeeformula.ShippingFeeFormulaConfigQueryService;
import com.renpho.erp.tms.application.shippingfeeformula.ShippingFeeFormulaConfigService;
import com.renpho.erp.tms.domain.exception.BusinessException;
import com.renpho.erp.tms.domain.shippingfeeformula.ShippingFeeFormulaArg;
import com.renpho.erp.tms.domain.shippingfeeformula.ShippingFeeFormulaArgId;
import com.renpho.erp.tms.domain.shippingfeeformula.ShippingFeeFormulaConfig;
import com.renpho.erp.tms.domain.shippingfeeformula.ShippingFeeFormulaConfigType;
import com.renpho.erp.tms.infrastructure.common.oplog.LogModule;
import com.renpho.karma.dto.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 运费公式配置接口.
 *
 * <AUTHOR>
 * @since 2025/6/11
 */
@Tag(name = "shipping fee formula config", description = "运费公式配置")
@ShenyuSpringCloudClient("/config/formula/shippingfee/**")
@RestController
@RequestMapping("/config/formula/shippingfee")
@Validated
@RequiredArgsConstructor
public class ShippingFeeFormulaConfigController {

    private final ShippingFeeFormulaConfigQueryService shippingFeeFormulaConfigQueryService;
    private final ShippingFeeFormulaConfigService shippingFeeFormulaConfigService;
    private final ShippingFeeFormulaConfigVoConverter shippingFeeFormulaConfigVoConverter;
    private final ShippingFeeFormulaConfigCommandConverter shippingFeeFormulaConfigCommandConverter;

    /**
     * 查询所有运费公式计费项.
     *
     * <AUTHOR>
     * @since 2025/4/23
     */
    @Operation(operationId = "listShippingFeeFormulaArg", description = "查询所有运费公式计费项.", tags = {"shipping fee formula config"})
    @PostMapping(value = "/arg/list")
    @TransMethodResult
    public R<List<ShippingFeeFormulaArgVo>> listArg() {
        List<ShippingFeeFormulaArg> domains = shippingFeeFormulaConfigQueryService.findAllArgs();
        List<ShippingFeeFormulaArgVo> vos = shippingFeeFormulaConfigVoConverter.toArgVos(domains);
        return R.success(vos);
    }

    /**
     * 新增运费公式计费项.
     *
     * <AUTHOR>
     * @since 2025/4/23
     */
    @Operation(operationId = "addShippingFeeFormulaArg", description = "新增运费公式计费项.", tags = {"shipping fee formula config"})
    @PostMapping(value = "/arg/add")
    @LogRecord(module = LogModule.SHIPPING_FEE_FORMULA_CONFIG, type = LogModule.CommonDesc.EDIT_OPERATOR, desc = LogModule.CommonDesc.EDIT_DESC)
    public R<Integer> addArg(@RequestBody @Valid @NameNotExist @ArgNameNotExist AddShippingFeeFormulaArgCmd cmd) {
        ShippingFeeFormulaArg command = shippingFeeFormulaConfigCommandConverter.toCommand(cmd);
        ShippingFeeFormulaArg domain = shippingFeeFormulaConfigService.addArg(command);
        ShippingFeeFormulaArgVo vo = shippingFeeFormulaConfigVoConverter.toArgVo(domain);
        LogRecordContextHolder.putRecordData(String.valueOf(vo.getId()), null, vo);
        return R.success(vo.getId());
    }

    /**
     * 编辑运费公式计费项.
     *
     * <AUTHOR>
     * @since 2025/4/23
     */
    @Operation(operationId = "updateShippingFeeFormulaArg", description = "编辑运费公式计费项.", tags = {"shipping fee formula config"})
    @PostMapping(value = "/arg/update")
    @LogRecord(module = LogModule.SHIPPING_FEE_FORMULA_CONFIG, type = LogModule.CommonDesc.EDIT_OPERATOR, desc = LogModule.CommonDesc.EDIT_DESC)
    public R<Integer> updateArg(@RequestBody @Valid @IdExist @NameNotExist @ArgNameNotExist UpdateShippingFeeFormulaArgCmd cmd) {
        ShippingFeeFormulaArg command = shippingFeeFormulaConfigCommandConverter.toCommand(cmd);
        ShippingFeeFormulaArgVo oldData = shippingFeeFormulaConfigQueryService.findArgById(command.getId())
                .map(shippingFeeFormulaConfigVoConverter::toArgVo)
                .orElseThrow(() -> new BusinessException("id.not.exist"));
        ShippingFeeFormulaArg domain = shippingFeeFormulaConfigService.updateArg(command);
        ShippingFeeFormulaArgVo vo = shippingFeeFormulaConfigVoConverter.toArgVo(domain);
        ShippingFeeFormulaArgVo newData = shippingFeeFormulaConfigQueryService.findArgById(command.getId())
                .map(shippingFeeFormulaConfigVoConverter::toArgVo)
                .orElseThrow(() -> new BusinessException("id.not.exist"));
        LogRecordContextHolder.putRecordData(String.valueOf(oldData.getId()), oldData, newData);
        return R.success(vo.getId());
    }

    /**
     * 删除运费公式计费项.
     *
     * <AUTHOR>
     * @since 2025/4/23
     */
    @Operation(operationId = "deleteShippingFeeFormulaArg", description = "删除运费公式计费项.", tags = {"shipping fee formula config"})
    @PostMapping(value = "/arg/delete")
    @LogRecord(module = LogModule.SHIPPING_FEE_FORMULA_CONFIG, type = LogModule.CommonDesc.DELETE_OPERATOR, desc = LogModule.CommonDesc.DELETE_DESC)
    public R<Integer> deleteArg(@RequestBody @Valid @IdExist ShippingFeeFormulaArgIdCmd cmd) {
        ShippingFeeFormulaArgId command = shippingFeeFormulaConfigCommandConverter.toCommand(cmd);
        ShippingFeeFormulaArgVo oldData = shippingFeeFormulaConfigQueryService.findArgById(command)
                .map(shippingFeeFormulaConfigVoConverter::toArgVo)
                .orElseThrow(() -> new BusinessException("id.not.exist"));
        ShippingFeeFormulaArgId domain = shippingFeeFormulaConfigService.deleteArg(command);
        LogRecordContextHolder.putRecordData(String.valueOf(domain.id()), oldData, null);
        return R.success(domain.id());
    }

    /**
     * 查询运费公式.
     *
     * <AUTHOR>
     * @since 2025/4/23
     */
    @Operation(operationId = "listShippingFeeFormula", description = "查询运费公式.", tags = {"shipping fee formula config"})
    @PostMapping(value = "/list")
    public R<Map<ShippingFeeFormulaConfigType, ShippingFeeFormulaConfigVo>> list() {
        Map<ShippingFeeFormulaConfigType, ShippingFeeFormulaConfigVo> map = shippingFeeFormulaConfigQueryService.findAll()
                .stream()
                .collect(Collectors.toMap(ShippingFeeFormulaConfig::getType, shippingFeeFormulaConfigVoConverter::toConfigVo));
        return R.success(map);
    }

    /**
     * 编辑运费公式.
     *
     * <AUTHOR>
     * @since 2025/4/23
     */
    @Operation(operationId = "updateShippingFeeFormula", description = "编辑运费公式.", tags = {"shipping fee formula config"})
    @PostMapping(value = "/update")
    @LogRecord(module = LogModule.SHIPPING_FEE_FORMULA_CONFIG, type = LogModule.CommonDesc.EDIT_OPERATOR, desc = LogModule.CommonDesc.EDIT_DESC)
    public R<String> update(@RequestBody @Valid UpdateShippingFeeFormulaConfigCmd cmd) {
        List<ShippingFeeFormulaConfigVo> oldData = shippingFeeFormulaConfigVoConverter.toConfigVos(shippingFeeFormulaConfigQueryService.findAll());
        List<ShippingFeeFormulaConfig> commands = shippingFeeFormulaConfigCommandConverter.toCommands(cmd);
        shippingFeeFormulaConfigService.updateFormula(commands);
        Map<Integer, ShippingFeeFormulaConfigVo> newData = shippingFeeFormulaConfigQueryService.findAll()
                .stream().map(shippingFeeFormulaConfigVoConverter::toConfigVo)
                .collect(Collectors.toMap(ShippingFeeFormulaConfigVo::getId, Function.identity()));
        for (ShippingFeeFormulaConfigVo old : oldData) {
            LogRecordContextHolder.putRecordData(String.valueOf(old.getId()), old, newData.get(old.getId()));
        }
        return R.success();
    }

}
