package com.renpho.erp.tms.adapter.web.controller.transportrequest.cmd;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2025/6/16
 */
@Getter
@Setter
public class TransportOrderInspectionCmd {
    /**
     * 运单号
     */
    @NotBlank
    private String trackingNo;


    /**
     * 出口查验 0:否 1:是
     */
    @NotNull
    private Boolean exportInspection;

    /**
     * 进口查验 0:否 1:是
     */
    @NotNull
    private Boolean importInspection;

    /**
     * 清关备注
     */
    private String customsClearanceRemark;
}
