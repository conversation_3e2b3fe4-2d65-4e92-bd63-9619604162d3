package com.renpho.erp.tms.adapter.web.controller.transportorder;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.fhs.core.trans.anno.TransMethodResult;
import com.fhs.trans.service.impl.TransService;
import com.renpho.erp.data.sensitive.SensitiveMethodResult;
import com.renpho.erp.log.repository.annotation.LogRecord;
import com.renpho.erp.log.repository.support.LogRecordContextHolder;
import com.renpho.erp.tms.adapter.web.controller.command.validator.IdExist;
import com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.*;
import com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.converter.TransportOrderCmdConverter;
import com.renpho.erp.tms.adapter.web.controller.transportorder.vo.TransportOrderDeatilSimpleVO;
import com.renpho.erp.tms.adapter.web.controller.transportorder.vo.TransportOrderDeatilVO;
import com.renpho.erp.tms.adapter.web.controller.transportorder.vo.TransportOrderExportVO;
import com.renpho.erp.tms.adapter.web.controller.transportorder.vo.TransportOrderPageVO;
import com.renpho.erp.tms.adapter.web.controller.transportorder.vo.converter.TransportOrderVoConverter;
import com.renpho.erp.tms.adapter.web.controller.transportrequest.vo.TransportRequestViewVO;
import com.renpho.erp.tms.application.transportorder.TransportOrderQueryService;
import com.renpho.erp.tms.application.transportorder.TransportOrderService;
import com.renpho.erp.tms.application.transportorder.logger.TransportOrderLogger;
import com.renpho.erp.tms.domain.transportorder.TransportOrder;
import com.renpho.erp.tms.domain.transportorder.TransportOrderId;
import com.renpho.erp.tms.domain.transportorder.TransportOrderQuery;
import com.renpho.erp.tms.domain.transportrequest.TransportRequest;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestId;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestOrderLookup;
import com.renpho.erp.tms.infrastructure.common.oplog.LogModule;
import com.renpho.erp.tms.infrastructure.util.excel.Excel18nTools;
import com.renpho.erp.tms.infrastructure.util.excel.ExcelUtil;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * TO单接口.
 *
 * <AUTHOR>
 * @since 2025/6/9
 */
@Slf4j
@RestController
@Tag(name = "transport order", description = "TO单接口")
@RequestMapping("/transport/order")
@ShenyuSpringCloudClient("/transport/order/**")
@RequiredArgsConstructor
public class TransportOrderController {

    private final TransportOrderService transportOrderService;
    private final TransportOrderQueryService transportOrderQueryService;
    private final TransportOrderCmdConverter transportOrderCmdConverter;
    private final TransportOrderVoConverter transportOrderVoConverter;
    private final TransService transService;
    private final TransportRequestOrderLookup transportRequestOrderLookup;
    private final TransportOrderLogger transportOrderLogger;
    private final TransportOrderControllerLogger transportOrderControllerLogger;

    /**
     * 分页查询TO单
     *
     * <AUTHOR>
     */
    @PostMapping("/page")
    @TransMethodResult
    @PreAuthorize("hasPermission('tms:transportOrder:list')")
    public R<Paging<TransportOrderPageVO>> pageList(@RequestBody TransportOrderListCmd cmd) {
        TransportOrderQuery toPageDomain = transportOrderCmdConverter.toPageDomain(cmd);

        Paging<TransportOrder> domains = transportOrderQueryService.pageList(toPageDomain);
        Paging<TransportOrderPageVO> vos = transportOrderVoConverter.toPageListVOs(domains);
        return R.success(vos);
    }

    /**
     * 统计TO单各状态数量 -1 为所有TO单数量之和
     */
    @PreAuthorize("hasPermission('tms:transportOrder:list')")
    @PostMapping("/countByStatus")
    public R<Map<Integer, Object>> countByStatus() {
        return R.success(transportOrderQueryService.countByStatus());
    }


    /**
     * 详情
     *
     * @param cmd 参数
     * @return 详情
     */
    @PostMapping("/detail")
    @TransMethodResult
    @PreAuthorize("hasPermission('tms:transportOrder:detail')")
    public R<TransportOrderDeatilVO> detail(@RequestBody TransportOrderDetailCmd cmd) {
        TransportOrderQuery query = transportOrderCmdConverter.toDetailDomain(cmd);

        TransportOrder domain = transportOrderQueryService.detail(query);
        TransportOrderDeatilVO vo = SpringUtil.getBean(this.getClass()).toTransportOrderDeatilVO(domain);
        return R.success(vo);
    }

    /**
     * 详情简略版
     *
     * @param toId toId
     * @return 详情
     */
    @PostMapping("/simpleDetail")
    @TransMethodResult
    public R<TransportOrderDeatilSimpleVO> simpleDetail(Integer toId) {
        TransportOrderQuery query = new TransportOrderQuery();
        query.setToId(toId);

        TransportOrder domain = transportOrderQueryService.simpleDetail(query);
        TransportOrderDeatilSimpleVO vo = transportOrderVoConverter.toTransportOrderSimpleDetailVO(domain);
        if (domain != null) {
            vo.setDeliveryTime(domain.getLatestDeliveryTime(domain.getTransportRequestList()));
        }
        return R.success(vo);
    }

    @SensitiveMethodResult
    public TransportOrderDeatilVO toTransportOrderDeatilVO(TransportOrder domain) {
        return transportOrderVoConverter.toDetailVO(domain);
    }

    /**
     * 根据TO单Id和运单号，查询运单信息（标记离港、到港、派送接口使用）
     *
     * @param cmd 参数
     * @return 详情
     */
    @PostMapping("/findTrList")
    @TransMethodResult
    public R<List<TransportRequestViewVO>> findTrList(@RequestBody TransportOrderViewCmd cmd) {

        List<TransportRequest> domains = transportOrderQueryService.findTrList(cmd.getToIds(), cmd.getTrackingNos());
        List<TransportRequestViewVO> vo = transportOrderVoConverter.toViewDetailVOs(domains);
        return R.success(vo);
    }


    /**
     * 作废TO
     *
     * @param id toId
     * @return toId
     */
    @PostMapping("/invalid")
    @PreAuthorize("hasPermission('tms:transportOrder:invalid')")
    public R<Integer> invalid(@Valid @RequestBody @IdExist IdQuery id) {
        TransportOrderId toId = transportOrderCmdConverter.toTransportOrderId(id);
        Optional<TransportOrder> oldToOpt = transportOrderQueryService.findById(toId);
        List<TransportRequestId> trIds =
                oldToOpt.map(old -> old.getTransportRequestList().stream().map(TransportRequest::getId).toList()).orElse(List.of());

        Map<TransportRequestId, TransportRequest> oldData =
                transportRequestOrderLookup.findByIds(trIds).stream().collect(Collectors.toMap(TransportRequest::getId
                        , Function.identity()));

        Optional<TransportOrder> newToOpt = transportOrderQueryService.findById(toId);
        Map<TransportRequestId, TransportRequest> newData =
                transportRequestOrderLookup.findByIds(trIds).stream().collect(Collectors.toMap(TransportRequest::getId, Function.identity()));

        transportOrderLogger.logTO(String.valueOf(toId.id()), oldToOpt.orElse(null), newToOpt.orElse(null));
        transportOrderLogger.logTR(oldData, newData, trIds);
        return R.success(id.getId());
    }


    /**
     * 订舱
     *
     * @param cmd 参数
     * @return toId to单Id
     */
    @PostMapping("/booking")
    @PreAuthorize("hasPermission('tms:transportOrder:booking')")
    @LogRecord(module = LogModule.TRANSPORT_ORDER, type = LogModule.CommonDesc.EDIT_OPERATOR)
    public R<Integer> booking(@Valid @RequestBody TransportOrderBookingCmd cmd) {
        TransportOrder domain = transportOrderCmdConverter.toDomain(cmd);
        TransportOrder oldData = transportOrderQueryService.findById(domain.getId()).orElse(null);
        TransportOrder newData = transportOrderService.booking(domain);
        LogRecordContextHolder.putRecordData(String.valueOf(domain.getId().id()), oldData, newData, LogModule.TransportOrder.BOOKING_DESC);

        return R.success(domain.getId().id());
    }


    /**
     * 交货
     *
     * @param cmd 参数
     * @return toId to单Id
     */
    @PostMapping("/handover")
    @PreAuthorize("hasPermission('tms:transportOrder:handover')")
    @LogRecord(module = LogModule.TRANSPORT_ORDER, type = LogModule.CommonDesc.EDIT_OPERATOR)
    public R<Integer> handover(@Valid @RequestBody TransportOrderHandoverCmd cmd) {
        TransportOrder domain = transportOrderCmdConverter.toDomain(cmd);
        TransportOrder oldData = transportOrderQueryService.findById(domain.getId()).orElse(null);
        transportOrderService.handover(domain);
        TransportOrder newData = transportOrderQueryService.findById(domain.getId()).orElse(null);
        LogRecordContextHolder.putRecordData(String.valueOf(domain.getId().id()), oldData, newData, LogModule.TransportOrder.HANDOVER_DESC);

        return R.success(domain.getId().id());
    }

    /**
     * 标记离港
     *
     * @param cmd 参数
     * @return toId to单Id
     */
    @PostMapping("/markDeparture")
    @PreAuthorize("hasPermission('tms:transportOrder:markDeparture')")
    @LogRecord(module = LogModule.TRANSPORT_ORDER, type = LogModule.CommonDesc.EDIT_OPERATOR)
    public R<List<Integer>> markDeparture(@Valid @RequestBody List<TransportOrderDepartureCmd> cmd) {
        List<TransportOrder> domains = transportOrderCmdConverter.toDomains(cmd);
        List<TransportOrderId> ids = domains.stream().map(TransportOrder::getId).toList();
        Map<TransportOrderId, TransportOrder> oldDataMap = transportOrderQueryService.findByIds(ids);
        List<Integer> toIds = transportOrderService.departure(domains);
        Map<TransportOrderId, TransportOrder> newDataMap = transportOrderQueryService.findByIds(ids);
        ids.forEach(id -> LogRecordContextHolder.putRecordData(String.valueOf(id.id()), oldDataMap.get(id), newDataMap.get(id), LogModule.TransportOrder.MARK_DEPARTURE_DESC));

        return R.success(toIds);
    }

    /**
     * 标记到港
     *
     * @param cmd 参数
     * @return toId to单Id
     */
    @PostMapping("/markArrival")
    @PreAuthorize("hasPermission('tms:transportOrder:markArrival')")
    @LogRecord(module = LogModule.TRANSPORT_ORDER, type = LogModule.CommonDesc.EDIT_OPERATOR)
    public R<List<Integer>> markArrival(@Valid @RequestBody List<TransportOrderArrivalCmd> cmd) {
        List<TransportOrder> domains = transportOrderCmdConverter.arrivalCmdToDomains(cmd);
        List<TransportOrderId> ids = domains.stream().map(TransportOrder::getId).toList();
        Map<TransportOrderId, TransportOrder> oldDataMap = transportOrderQueryService.findByIds(ids);
        List<Integer> toIds = transportOrderService.arrival(domains);
        Map<TransportOrderId, TransportOrder> newDataMap = transportOrderQueryService.findByIds(ids);
        ids.forEach(id -> LogRecordContextHolder.putRecordData(String.valueOf(id.id()), oldDataMap.get(id), newDataMap.get(id), LogModule.TransportOrder.MARK_ARRIVAL_DESC));

        return R.success(toIds);
    }

    /**
     * 标记派送
     *
     * @param cmd 参数
     * @return toId to单Id
     */
    @PostMapping("/markDelivery")
    @PreAuthorize("hasPermission('tms:transportOrder:markDelivery')")
    @LogRecord(module = LogModule.TRANSPORT_ORDER, type = LogModule.CommonDesc.EDIT_OPERATOR)
    public R<List<Integer>> markDelivery(@Valid @RequestBody List<TransportOrderDeliveryCmd> cmd) {
        List<TransportOrder> domains = transportOrderCmdConverter.deliveryCmdToDomains(cmd);
        List<TransportOrderId> ids = domains.stream().map(TransportOrder::getId).toList();
        Map<TransportOrderId, TransportOrder> oldDataMap = transportOrderQueryService.findByIds(ids);
        List<Integer> toIds = transportOrderService.delivery(domains);
        Map<TransportOrderId, TransportOrder> newDataMap = transportOrderQueryService.findByIds(ids);
        ids.forEach(id -> LogRecordContextHolder.putRecordData(String.valueOf(id.id()), oldDataMap.get(id), newDataMap.get(id), LogModule.TransportOrder.MARK_DELIVERY_DESC));
        return R.success(toIds);
    }


    /**
     * 导出TO
     *
     * @param cmd      参数
     * @param response 响应
     */
    @TransMethodResult
    @PostMapping("/export")
    @PreAuthorize("hasPermission('tms:transportOrder:export')")
    public void downloadTransportRequest(@Valid @RequestBody TransportOrderListCmd cmd,
                                         HttpServletResponse response) {
        TransportOrderQuery query = transportOrderCmdConverter.toPageDomain(cmd);

        //excel总行数
        Long totalCount = transportOrderQueryService.countExport(query);

        ExcelUtil.exportExcelBatch(totalCount,
                1000,
                "TO",
                TransportOrderExportVO.class,
                response,
                (pageNum, size) -> {
                    query.setPageIndex(pageNum);
                    query.setPageSize(size);

                    List<TransportOrder> datas = transportOrderQueryService.findPageList(query);
                    if (datas.isEmpty()) {
                        return null;
                    }

                    List<TransportOrderExportVO> vos = datas.stream()
                            .filter(to -> to != null && to.getTransportRequestList() != null)
                            .map(to -> to.getTransportRequestList().stream()
                                    .map(req -> SpringUtil.getBean(this.getClass()).toExportVO(to, req))
                                    .toList())
                            .flatMap(Collection::stream).toList();
                    transService.transBatch(vos);

                    transportOrderControllerLogger.logDownloadRecords(cmd, vos.stream().map(TransportOrderExportVO::getId).toList());
                    return vos;
                });
    }

    /**
     * 数据脱敏（坑：只能简单对象，不能被page或R包裹）
     *
     * @param to to单
     * @param tr tr单
     * @return 脱敏tr单
     */
    @SensitiveMethodResult
    public TransportOrderExportVO toExportVO(TransportOrder to, TransportRequest tr) {
        return transportOrderVoConverter.toExportVO(to, tr);
    }


    /**
     * 导出箱单
     *
     * @param cmd      参数
     * @param response 响应
     */

    @PostMapping("/exportPackingList")
    @PreAuthorize("hasPermission('tms:transportOrder:exportPackingList')")
    public void exportPackingList(@Valid @RequestBody TransportOrderExportPackingListCmd cmd,
                                  HttpServletResponse response) throws IOException {
        transportOrderControllerLogger.logDownloadPackingListRecords(String.valueOf(cmd.getToId()));
        String filename = "packing_list_" + DateUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN) + ".zip";
        Excel18nTools.setZipResponseProp(response, filename);
        transportOrderQueryService.exportPackingList(cmd.getToId(), cmd.getDimension(), cmd.getGroupByOwner(), response);
    }

    /**
     * 导出发票
     *
     * @param cmd 参数
     */
    @PostMapping("/exportInvoice")
    @PreAuthorize("hasPermission('tms:transportOrder:exportInvoice')")
    public void exportInvoice(@Valid @RequestBody TransportOrderExportPackingListCmd cmd,
                              HttpServletResponse response) throws IOException {
        transportOrderControllerLogger.logDownloadInvoiceRecords(String.valueOf(cmd.getToId()));
        String filename = "invoice_" + DateUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN) + ".zip";
        Excel18nTools.setZipResponseProp(response, filename);
        transportOrderQueryService.exportInvoice(cmd.getToId(), cmd.getDimension(), cmd.getGroupByOwner(), response);
    }


    /**
     * 编辑TO单
     *
     * @param cmd 参数
     * @return TOId
     */
    @PostMapping("/edit")
    @PreAuthorize("hasPermission('tms:transportOrder:editTr')")
    @LogRecord(module = LogModule.TRANSPORT_ORDER, type = LogModule.CommonDesc.EDIT_OPERATOR)
    public R<Integer> edit(@Valid @RequestBody TransportOrderEditCmd cmd) {
        TransportOrder to = transportOrderCmdConverter.toDomain(cmd);
        TransportOrder oldData = transportOrderQueryService.findById(to.getId()).orElse(null);
        transportOrderService.edit(to);
        TransportOrder newData = transportOrderQueryService.findById(to.getId()).orElse(null);
        LogRecordContextHolder.putRecordData(String.valueOf(to.getId().id()), oldData, newData, LogModule.CommonDesc.EDIT_DESC);
        return R.success();
    }

    /**
     * 检查是否需要重新审核
     *
     * @param cmd 参数
     * @return true:需要重新审核 false:不需要重新审核
     */
    @GetMapping("/checkIsApproveAgain")
    public R<Boolean> checkIsApproveAgain(TransportOrderEditCmd cmd) {
        TransportOrder to = transportOrderCmdConverter.toDomain(cmd);
        Boolean isApproveAgain = transportOrderService.checkIsApproveAgain(to);
        return R.success(isApproveAgain);
    }

    /**
     * 添加TO单批注
     *
     * @param cmd 参数
     */
    @PostMapping("/addComment")
    @PreAuthorize("hasPermission('tms:transportOrder:comment')")
    public R<Integer> addComment(@RequestBody TransportOrderCommentCmd cmd) {
        TransportOrder to = transportOrderCmdConverter.toDomain(cmd);
        transportOrderService.addComment(to);
        return R.success();
    }
}
