package com.renpho.erp.tms.adapter.web.controller.customsinfo.command;

import com.renpho.erp.tms.adapter.web.controller.command.container.IdsContainer;
import com.renpho.erp.tms.domain.customsinfo.PskuCustomInfoStatus;
import jakarta.annotation.Generated;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.Collection;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/4/22
 */
@Getter
@Setter
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-04-22T12:09:54.179410400+08:00[Asia/Shanghai]", comments = "Generator version: 7.12.0")
public class UpdateStatusCmd implements IdsContainer<Collection<Integer>, Integer>, Serializable {

    @Serial
    private static final long serialVersionUID = -3954008245468049821L;

    @NotNull
    @Valid
    private Set<@Valid @NotNull Integer> ids;

    @NotNull
    private PskuCustomInfoStatus status;

}

