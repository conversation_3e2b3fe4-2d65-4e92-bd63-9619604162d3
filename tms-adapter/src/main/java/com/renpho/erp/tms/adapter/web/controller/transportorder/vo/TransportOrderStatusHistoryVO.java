package com.renpho.erp.tms.adapter.web.controller.transportorder.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/6/17
 */
@Getter
@Setter
public class TransportOrderStatusHistoryVO implements VO, Serializable {
    /**
     * 主键
     */
    @JsonIgnore
    private Integer id;
    /**
     * 状态
     */
    @Trans(type = TransType.DICTIONARY, key = "TO_STATUS", ref = "statusName")
    private Integer status;
    /**
     * 状态名称
     */
    private String statusName;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
