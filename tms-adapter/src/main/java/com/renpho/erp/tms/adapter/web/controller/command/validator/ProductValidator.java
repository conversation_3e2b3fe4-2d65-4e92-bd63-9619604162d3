package com.renpho.erp.tms.adapter.web.controller.command.validator;

import com.renpho.erp.tms.adapter.web.controller.command.container.ProductIdContainer;
import com.renpho.erp.tms.adapter.web.controller.command.container.PskuContainer;
import com.renpho.erp.tms.domain.product.ProductId;
import com.renpho.erp.tms.infrastructure.remote.product.repository.ProductLookup;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

import java.lang.annotation.Annotation;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @since 2025/3/19
 */
public abstract class ProductValidator<A extends Annotation, T> implements ConstraintValidator<A, T> {

    protected Predicate<T> predicate;

    @Resource
    protected ProductLookup productLookup;

    @Override
    public boolean isValid(T value, ConstraintValidatorContext context) {
        return productLookup == null || predicate.test(value);
    }

    public static class IsProductIdExist extends ProductValidator<ProductExist, ProductIdContainer> {
        @Override
        public void initialize(ProductExist constraintAnnotation) {
            predicate = cmd -> Optional.ofNullable(cmd)
                    .map(ProductIdContainer::getProductId)
                    .map(ProductId::new)
                    .flatMap(productLookup::findById)
                    .filter(p -> Objects.equals(p.getReviewStatus(), 2))
                    .stream()
                    .peek(product -> Optional.ofNullable(cmd).ifPresent(c -> c.setProduct(product)))
                    .peek(product -> Optional.ofNullable(cmd).ifPresent(c -> c.setProduct(product)))
                    .findAny()
                    .isPresent();
        }
    }

    public static class IsPskuExist extends ProductValidator<ProductExist, PskuContainer> {
        @Override
        public void initialize(ProductExist constraintAnnotation) {
            predicate = cmd -> Optional.ofNullable(cmd)
                    .map(PskuContainer::getPsku)
                    .filter(StringUtils::isNotBlank)
                    .flatMap(productLookup::findByPsku)
                    .filter(p -> Objects.equals(p.getReviewStatus(), 2))
                    .stream()
                    .peek(product -> Optional.ofNullable(cmd).ifPresent(c -> c.setProduct(product)))
                    .findAny()
                    .isPresent();
        }
    }
}
