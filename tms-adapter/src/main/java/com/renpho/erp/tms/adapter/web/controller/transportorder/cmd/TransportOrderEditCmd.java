package com.renpho.erp.tms.adapter.web.controller.transportorder.cmd;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/23
 */
@Getter
@Setter
public class TransportOrderEditCmd {
    /**
     * TO单ID
     */
    private Integer toId;

    /**
     * 批注
     */
    private String comment;

    /**
     * TR单ID
     */
    @Size(min = 1)
    @NotEmpty
    private List<Integer> trIds;

}
