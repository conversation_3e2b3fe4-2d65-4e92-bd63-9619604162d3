package com.renpho.erp.tms.adapter.web.job.TransportRequest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.renpho.erp.tms.application.transportorder.mq.TransportOrderMQProducer;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/7/17
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TransportRequestFmsJob {
    private final TransportOrderMQProducer transportOrderMQProducer;

    /**
     * 推送TR数据到FMS
     */
    @XxlJob("pushTrDataToFms")
    public void pushTrDataToFms(){
        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jo = JSON.parseObject(jobParam);
        String tag = jo.getString("tag");
        JSONArray toNos = jo.getJSONArray("toNos");
        toNos.forEach(toNo -> transportOrderMQProducer.pushMsgToFms((String) toNo, tag));
    }
}
