package com.renpho.erp.tms.adapter.web.controller.transportrequest.cmd;

import com.renpho.erp.tms.adapter.web.controller.transportrequest.cmd.container.TransportRequestIdContainer;
import jakarta.annotation.Generated;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/5/19
 */
@lombok.Getter
@lombok.Setter
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-05-19T18:06:01.197175700+08:00[Asia/Shanghai]", comments = "Generator version: 7.12.0")
public class IdQuery implements TransportRequestIdContainer<Integer>, Serializable {

    @Serial
    private static final long serialVersionUID = -7547392096868791764L;

    @NotNull
    private Integer id;

}

