package com.renpho.erp.tms.adapter.web.controller.transportrequest.cmd.converter;

import com.renpho.erp.tms.adapter.web.controller.transportrequest.cmd.*;
import com.renpho.erp.tms.client.transportrequest.request.ListTrQuery;
import com.renpho.erp.tms.domain.transportrequest.TransportRequest;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestId;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestListQuery;
import com.renpho.erp.tms.domain.transportrequest.UpdateTransportRequestCmd;
import com.renpho.erp.tms.domain.transportrequest.dto.TransportRequestSignInDoListRequest;
import com.renpho.erp.tms.domain.transportrequest.dto.TransportRequestSignInRequest;
import com.renpho.erp.tms.infrastructure.persistence.firstleg.mode.po.converter.FirstLegModeConverter;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.po.converter.TransportRequestConverter;
import com.renpho.erp.tms.infrastructure.remote.owner.converter.OwnerConverter;
import com.renpho.erp.tms.infrastructure.remote.product.converter.ProductConverter;
import com.renpho.erp.tms.infrastructure.remote.product.converter.ProductPriceConverter;
import com.renpho.erp.tms.infrastructure.remote.purchasesupplier.PurchaseSupplierConverter;
import com.renpho.erp.tms.infrastructure.remote.saleschannel.converter.SalesChannelConverter;
import com.renpho.erp.tms.infrastructure.remote.store.converter.StoreConverter;
import com.renpho.erp.tms.infrastructure.remote.user.converter.OperatorConverter;
import com.renpho.erp.tms.infrastructure.remote.warehouse.converter.WarehouseConverter;
import org.mapstruct.*;

import java.util.Collection;
import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {TransportRequestConverter.class, ProductConverter.class, ProductPriceConverter.class, WarehouseConverter.class, SalesChannelConverter.class, StoreConverter.class, OwnerConverter.class, PurchaseSupplierConverter.class, FirstLegModeConverter.class, OperatorConverter.class})
public interface TransportRequestCmdConverter {

    @Mapping(target = "likeTrNo", source = "trNo")
    @Mapping(target = "likeToNo", source = "toNo")
    @Mapping(target = "likePsku", source = "psku")
    @Mapping(target = "likePoNo", source = "poNo")
    @Mapping(target = "trNo", ignore = true)
    @Mapping(target = "toNo", ignore = true)
    @Mapping(target = "psku", ignore = true)
    @Mapping(target = "poNo", ignore = true)
    @Mapping(target = "ownerId.id", source = "ownerId")
    TransportRequestListQuery toPageDomain(ListQuery listQuery);

    @Mapping(target = "id", source = "id")
    TransportRequestId toDomain(IdQuery idQuery);

    @Mapping(target = "shipmentId", source = "inboundOrderNo")
    TransportRequest toDomain(UploadInboundOrderCmd cmd);

    TransportRequest update(UpdateTransportRequestCmd cmd, @MappingTarget TransportRequest command);

    List<TransportRequestId> toIds(Collection<Integer> ids);

    TransportRequest inspectionCmdToDomain(TransportOrderInspectionCmd cmd);

    List<TransportRequest> inspectionCmdToDomains(List<TransportOrderInspectionCmd> cmd);

    @Mapping(target = "pdNoList", source = "pdNos")
    @Mapping(target = "trNos", source = "trNos")
    TransportRequestListQuery toQueryPdDomain(ListTrQuery query);

    TransportRequestSignInRequest toSignInRequest(SignInCmd signInCmd);

    TransportRequestSignInDoListRequest toSignInDoListRequest(SignInDoListCmd signInDoListCmd);

}
