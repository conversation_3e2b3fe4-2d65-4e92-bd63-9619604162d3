package com.renpho.erp.tms.adapter.web.controller.firstleg.mode.command.validator;

import com.renpho.erp.tms.infrastructure.remote.countryregion.repository.CountryRegionLookup;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;


/**
 * <AUTHOR>
 * @since 2025/4/23
 */
public class CountryRegionValidator implements ConstraintValidator<CountryRegionExist, String> {
    @Resource
    protected CountryRegionLookup countryRegionLookup;

    @Override
    public boolean isValid(String code, ConstraintValidatorContext context) {
        return countryRegionLookup.findByCode(code).isPresent();
    }
}
