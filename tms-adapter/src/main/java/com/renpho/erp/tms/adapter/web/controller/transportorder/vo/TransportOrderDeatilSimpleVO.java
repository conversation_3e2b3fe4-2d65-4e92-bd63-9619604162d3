package com.renpho.erp.tms.adapter.web.controller.transportorder.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.renpho.erp.tms.adapter.web.controller.orderfile.vo.OrderFileVO;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/10
 */
@Data
public class TransportOrderDeatilSimpleVO implements VO, Serializable {

    @Serial
    private static final long serialVersionUID = -7741876717531447841L;

    /**
     * TO单ID
     */
    private Integer id;

    /**
     * TO单号
     */
    private String toNo;

    /**
     * TO单状态
     */
    @Trans(type = TransType.DICTIONARY, key = "TO_STATUS", ref = "statusName")
    private Integer status;

    /**
     * TO单状态名称
     */
    private String statusName;


    /**
     * 柜号
     */
    private String containerNo;

    /**
     * 交货时间
     */
    private String deliveryTime;

    /**
     * 头程方式
     */
    private String firstLegModeName;

    /**
     * 起运港（SRM贸易条款出货地点）
     */
    private String shippingPort;

    /**
     * 目的国/地区  国家(地区)
     */
    private String destCountry;


    /**
     * 文件信息
     */
    private List<OrderFileVO> files;

}

