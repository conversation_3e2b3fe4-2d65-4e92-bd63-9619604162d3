package com.renpho.erp.tms.adapter.web.controller.customsinfo.command;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2025/4/25
 */
@Getter
@Setter
public class PskuCustomsInfoUploadExcelWithError extends PskuCustomsInfoUploadExcel {
    @Serial
    private static final long serialVersionUID = 2154081559928944201L;

    @ExcelProperty(value = "excel.error", index = 14)
    private String error;

    public PskuCustomsInfoUploadExcelWithError addError(String error) {
        this.error = StringUtils.defaultIfBlank(this.error, "") + error + (StringUtils.endsWith(error, "; ") ? StringUtils.EMPTY : "; ");
        return this;
    }
}
