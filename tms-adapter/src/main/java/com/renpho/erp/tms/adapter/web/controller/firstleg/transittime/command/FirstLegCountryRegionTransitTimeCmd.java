package com.renpho.erp.tms.adapter.web.controller.firstleg.transittime.command;

import com.renpho.karma.dto.Command;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2025/4/23
 */
@Getter
@Setter
public class FirstLegCountryRegionTransitTimeCmd extends Command {

    /**
     * 编辑时必填，新增时不填；
     */
    private Integer id;

    /**
     * 区域
     */
    @Size(max = 20, message = "validation.transitTime.area.maxLength")
    @NotBlank(message = "validation.nameZh.required")
    private String area;

    /**
     * 最小时效(天)
     */
    @Positive
    @NotNull(message = "validation.status.required")
    private Integer minTransitTime;

    /**
     * 最大时效(天)
     */
    @Positive
    @NotNull(message = "validation.status.required")
    private Integer maxTransitTime;
}
