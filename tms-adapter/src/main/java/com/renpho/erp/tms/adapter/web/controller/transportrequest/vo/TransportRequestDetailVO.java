package com.renpho.erp.tms.adapter.web.controller.transportrequest.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.github.houbb.sensitive.annotation.Sensitive;
import com.renpho.erp.data.sensitive.SensitiveDynamicCondition;
import com.renpho.erp.data.sensitive.SensitiveDynamicStrategy;
import com.renpho.erp.tms.client.transportrequest.response.TransportRequestProductVo;
import jakarta.annotation.Generated;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/19
 */
@Getter
@Setter
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-05-19T18:06:01.197175700+08:00[Asia/Shanghai]", comments = "Generator version: 7.12.0")
public class TransportRequestDetailVO implements VO, Serializable {

    @Serial
    private static final long serialVersionUID = -102087812492189181L;

    private Integer id;

    /**
     * TR单号
     */
    private String trNo;

    /**
     * 交付单号（PD单号）
     */
    private String pdNo;

    /**
     * PO单号
     */
    private String poNo;
    /**
     * PO单ID
     */
    private Integer poId;

    /**
     * TO单号
     */
    private String toNo;

    /**
     * TO单ID
     */
    private Integer toId;

    /**
     * QC单ID
     */
    private Integer qcId;

    /**
     * QC单号
     */
    private String qcNo;

    /**
     * TR单状态, 字典值: transport_request_status
     */
    @Trans(type = TransType.DICTIONARY, key = "transport_request_status", ref = "statusName")
    private Integer status;

    /**
     * TR单状态
     */
    private String statusName;

    /**
     * 头程方式 ID
     */
    private String firstLegModeId;

    /**
     * 头程方式
     */
    private String firstLegModeName;

    /**
     * 头程类型字典值，字典：first_leg_type
     */
    @Trans(type = TransType.DICTIONARY, key = "first_leg_type", ref = "firstLegTypeName")
    private String firstLegType;

    /**
     * 头程类型，字典：first_leg_type
     */
    private String firstLegTypeName;

    /**
     * 业务类型
     */
    @Trans(type = TransType.DICTIONARY, key = "BUSINESS_TYPE", ref = "businessTypeName")
    private String businessType;
    /**
     * 业务类型名称
     */
    private String businessTypeName;

    /**
     * 平台 ID
     */
    private Integer salesChannelId;

    /**
     * 平台名称
     */
    private String salesChannelName;

    /**
     * 平台编码
     */
    private String salesChannelCode;

    /**
     * 店铺 ID
     */
    private Integer storeId;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 货主 ID
     */
    private Integer ownerId;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 货主编码
     */
    private String ownerCode;

    /**
     * 起运港字典值, 字典: trade_terms_ship_to
     */
//    @Trans(type = TransType.DICTIONARY, key = "trade_terms_ship_to", ref = "shippingPortName")
    private String shippingPort;

    /**
     * 起运港
     */
    private String shippingPortName;

    /**
     * 贸易条款字典值, 字典: trade_terms
     */
//    @Trans(type = TransType.DICTIONARY, key = "trade_terms", ref = "tradeTermsName")
    private String tradeTerms;

    /**
     * 贸易条款
     */
    private String tradeTermsName;

    /**
     * 服务商类型字典值, 字典: logistics_type
     */
    @Trans(type = TransType.DICTIONARY, key = "logistics_type", ref = "carrierTypeName")
    private String carrierType;

    /**
     * 服务商类型
     */
    private String carrierTypeName;

    /**
     * 发运与入库字典值, 字典: ship_warehouse_type
     */
    @Trans(type = TransType.DICTIONARY, key = "ship_warehouse_type", ref = "shipWarehouseMethodName")
    private String shipWarehouseMethod;

    /**
     * 发运与入库
     */
    private String shipWarehouseMethodName;

    /**
     * 发货仓 ID
     */
    private String shippingWarehouseId;

    /**
     * 发货仓名称
     */
    private String shippingWarehouseName;

    /**
     * 发货仓编码
     */
    private String shippingWarehouseCode;
    /**
     * 目的国/地区  国家(地区)
     */
    private String destCountry;

    /**
     * 目的仓 ID
     */
    private String destWarehouseId;

    /**
     * 目的仓名称
     */
    private String destWarehouseName;

    /**
     * 目的仓编码
     */
    private String destWarehouseCode;

    /**
     * 目的地
     */
    private String destination;

    /**
     * 计划出货日期-开始
     */
    private LocalDate plannedShipStartDate;

    /**
     * 计划出货日期-结束
     */
    private LocalDate plannedShipEndDate;

    /**
     * 工厂交期
     */
    private LocalDate latestDeliveryDate;

    /**
     * 采购供应商 ID
     */
    private String purchaseSupplierId;

    /**
     * 采购供应商名称
     */
    @Sensitive(permission = "SUPPLIER:NAME", condition = SensitiveDynamicCondition.class,
            strategy = SensitiveDynamicStrategy.class)
    private String purchaseSupplierName;

    /**
     * 采购供应商编码
     */
    private String purchaseSupplierCode;

    /**
     * 采购供应商简称
     */
    private String purchaseSupplierShortName;

    /**
     * ReferenceId
     */
    private String referenceId;

    /**
     * ShipmentId
     */
    private String shipmentId;

    /**
     * 质检结果
     */
    @Trans(type = TransType.DICTIONARY, key = "quality_task_result", ref = "qcResultName")
    private Integer qcResult;

    /**
     * 质检结果名称
     */
    private String qcResultName;

    /**
     * 运营人员 ID
     */
    private String salesStaffId;

    /**
     * 运营人员名称
     */
    private String salesStaffName;

    /**
     * 运营人员工号
     */
    private String salesStaffCode;

    /**
     * 计划人员 ID
     */
    private String planningStaffId;

    /**
     * 计划人员名称
     */
    private String planningStaffName;

    /**
     * 计划人员工号
     */
    private String planningStaffCode;

    /**
     * 采购人员 ID
     */
    private String purchaseStaffId;

    /**
     * 采购人员名称
     */
    private String purchaseStaffName;

    /**
     * 采购人员工号
     */
    private String purchaseStaffCode;

    /**
     * 箱唛文件ID
     */
    private List<String> cartonLabelFileId;

    /**
     * 交货时间
     */
    private LocalDate deliveryTime;

    /**
     * 实际离港时间
     */
    private LocalDate actualDepartureTime;

    /**
     * 实际到港时间
     */
    private LocalDate actualArrivalTime;

    /**
     * 实际派送时间
     */
    private LocalDate actualDeliveryTime;

    /**
     * 签收时间
     */
    private LocalDateTime receivedTime;

    /**
     * 预估运费
     */
    private BigDecimal estimatedFreight;

    /**
     * 预估运费币种
     */
    private String estimatedFreightCurrencyCode;

    /**
     * 预估税费
     */
    private BigDecimal estimatedTax;

    /**
     * 预估税费币种
     */
    private String estimatedTaxCurrencyCode;

    /**
     * 产品信息
     */
    private TransportRequestProductVo product;

}

