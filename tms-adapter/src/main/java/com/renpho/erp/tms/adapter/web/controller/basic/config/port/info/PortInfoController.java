package com.renpho.erp.tms.adapter.web.controller.basic.config.port.info;

import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.fastjson.JSON;
import com.renpho.erp.data.trans.dict.Dict;
import com.renpho.erp.ftm.client.request.FileRequest;
import com.renpho.erp.ftm.client.response.FileDetailResponse;
import com.renpho.erp.ftm.client.response.FileInfoResponse;
import com.renpho.erp.tms.adapter.web.controller.basic.config.port.info.cmd.PortInfoAddListCmd;
import com.renpho.erp.tms.adapter.web.controller.basic.config.port.info.cmd.PortInfoCmd;
import com.renpho.erp.tms.adapter.web.controller.basic.config.port.info.cmd.PortInfoUpdateListCmd;
import com.renpho.erp.tms.adapter.web.controller.basic.config.port.info.converter.PortInfoConverter;
import com.renpho.erp.tms.application.basicconfig.PortInfoService;
import com.renpho.erp.tms.application.common.CommonService;
import com.renpho.erp.tms.domain.common.LanguageEnum;
import com.renpho.erp.tms.domain.exception.CommonErrorCode;
import com.renpho.erp.tms.domain.file.FilePathConstants;
import com.renpho.erp.tms.domain.portinfo.dto.PortInfoAddListRequest;
import com.renpho.erp.tms.domain.portinfo.dto.PortInfoRequest;
import com.renpho.erp.tms.domain.portinfo.dto.PortInfoResponse;
import com.renpho.erp.tms.domain.portinfo.dto.PortInfoUpdateListRequest;
import com.renpho.erp.tms.infrastructure.persistence.portinfo.PortInfoDBConverter;
import com.renpho.erp.tms.infrastructure.persistence.portinfo.dto.PortInfoExport;
import com.renpho.erp.tms.infrastructure.persistence.portinfo.dto.PortInfoImport;
import com.renpho.erp.tms.infrastructure.persistence.portinfo.dto.PortInfoImportError;
import com.renpho.erp.tms.infrastructure.persistence.portinfo.dto.PortInfoImportVerify;
import com.renpho.erp.tms.infrastructure.remote.file.RemoteFileFeign;
import com.renpho.erp.tms.infrastructure.util.LanguageUtil;
import com.renpho.erp.tms.infrastructure.util.excel.Excel18nTools;
import com.renpho.erp.tms.infrastructure.util.excel.MyMultipartFile;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import com.renpho.karma.exception.ErrorCodeException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 港口配置-接口.
 *
 * <AUTHOR>
 * @since 2025/6/10
 */

@Slf4j
@RestController
@RequestMapping("/port/info")
@ShenyuSpringCloudClient("/port/info/**")
@RequiredArgsConstructor
public class PortInfoController {

    private final PortInfoService portInfoService;
    private final PortInfoConverter portInfoConverter;
    private final RemoteFileFeign remoteFileFeign;
    private final PortInfoDBConverter portInfoDBConverter;
    private final CommonService commonService;

    /**
     * 港口列表查询（有权限）
     * @param cmd 参数
     * @return page
     */
    @PostMapping("/page")
    @PreAuthorize("hasPermission('tms:port:info:page')")
    public R<Paging<PortInfoResponse>> page(@RequestBody @Valid PortInfoCmd cmd){
        PortInfoRequest request = portInfoConverter.cmdToRequest(cmd);
        Paging<PortInfoResponse> paging = portInfoService.page(request);
        return R.success(paging);
    }

    /**
     * 港口列表查询（无权限）
     * @param cmd 参数
     * @return page
     */
    @PostMapping("/list")
    public List<PortInfoResponse> list(@RequestBody @Valid PortInfoCmd cmd){
        PortInfoRequest request = portInfoConverter.cmdToRequest(cmd);
        return portInfoService.list(request);
    }

    /**
     * 编辑-详情页面（有权限）
     * @param countryCode 国家/地区代码
     * @return 港口信息列表
     */
    @GetMapping("/details")
    @PreAuthorize("hasPermission('tms:port:info:edit')")
    public List<PortInfoResponse> selectPortInfoList(@RequestParam String countryCode){
        return portInfoService.selectPortInfoList(countryCode);
    }

    /**
     * 新增-批量
     * @param cmd 新增参数
     * @param req 请求
     * @return 新增结果
     */
    @PostMapping("/add")
    @PreAuthorize("hasPermission('tms:port:info:add')")
    public R<Boolean> add(@RequestBody PortInfoAddListCmd cmd, HttpServletRequest req) {
        // 参数校验
        LanguageEnum language = LanguageUtil.getLanguage(req);
        cmd.validate(language);

        // 转换请求
        PortInfoAddListRequest request = portInfoConverter.cmdAddToRequest(cmd);
        portInfoService.add(request);
        return R.success(true);
    }

    /**
     * 更新-批量
     * @param cmd 更新参数
     * @param req 请求
     * @return 更新结果
     */
    @PostMapping("/edit")
    @PreAuthorize("hasPermission('tms:port:info:edit')")
    public R<Boolean> update(@RequestBody PortInfoUpdateListCmd cmd, HttpServletRequest req) {
        // 参数校验
        LanguageEnum language = LanguageUtil.getLanguage(req);
        cmd.validate(language);

        // 转换请求
        PortInfoUpdateListRequest request = portInfoConverter.cmdUpdateToRequest(cmd);
        portInfoService.edit(request);
        return R.success(true);
    }

    /**
     * 导入模版下载
     */
    @PostMapping("/template")
    @PreAuthorize("hasPermission('tms:port:info:import')")
    public void importTempFile(HttpServletResponse response, HttpServletRequest req) throws IOException {
        String fileName = null;
        LanguageEnum language = LanguageUtil.getLanguage(req);
        if (language == LanguageEnum.China) {
            fileName = "港口配置-导入模板.xlsx";
        }
        else {
            fileName = "PortInfo-ImportTemplate.xlsx";
        }

        // 写入数据
        List<PortInfoImport> exportList = new ArrayList<>();

        // 处理 Excel 文件
        ByteArrayOutputStream ous = Excel18nTools.handleExcelFile(exportList, PortInfoImport.class);

        // 写出去
        Excel18nTools.writeExcelFile(fileName, ous, response);
    }

    /**
     * 导入数据
     */
    @PostMapping("/import")
    @PreAuthorize("hasPermission('tms:port:info:import')")
    public R<Object> importData(@RequestParam("file") MultipartFile multipartFile, HttpServletRequest req) throws IOException {
        long begin = System.currentTimeMillis();
        LanguageEnum language = LanguageUtil.getLanguage(req);
        try {
            long start = System.currentTimeMillis();
            // 获取解析后的数据
            List<PortInfoImport> importDataList = Excel18nTools.readExcelFile(multipartFile, PortInfoImport.class);
            if (importDataList.isEmpty()) {
                return R.fail(new ErrorCodeException(CommonErrorCode.IMPORT_NONNULL), null);
            }
            long end = System.currentTimeMillis();
            log.info("解析文件耗时：{} ms", end - start);

            start = System.currentTimeMillis();

            // 正式导入
            PortInfoImportVerify verify = portInfoService.importData(importDataList, language);

            end = System.currentTimeMillis();
            log.info("处理业务耗时：{} ms", end - start);

            FileDetailResponse fileInfoRespData = null;
            if (!verify.verify()) {
                // 参数反转
                Map<String,String> portTypeMap = commonService.getSourceMap(verify.getPortTypeDict(), language);
                verify.backParams(portInfoDBConverter, language,portTypeMap);

                String fileName = "港口配置-导入错误.xlsx";
                if (LanguageEnum.English == language ) {
                    fileName = "PortInfo-ImportError.xlsx";
                }

                // 处理 Excel 文件
                ByteArrayOutputStream ous = Excel18nTools.handleExcelFile(verify.getErrorList(), PortInfoImportError.class);
                InputStream inputStream = new ByteArrayInputStream(ous.toByteArray());

                MyMultipartFile reqMultipartFile = new MyMultipartFile(fileName, fileName, null, inputStream);
                MultipartFile[] multipartFiles = { reqMultipartFile };
                R<List<FileInfoResponse>> uploadResp = remoteFileFeign.upload(multipartFiles,
                        RemoteFileFeign.UPLOAD_SYSTEM, FilePathConstants.PORT_INFO_MANAGER, false);
                log.info("请求【文件上传】接口，resp：{}", JSON.toJSON(uploadResp));
                reqMultipartFile.getInputStream().close();
                if (!uploadResp.isSuccess()) {
                    throw new ErrorCodeException(CommonErrorCode.PRODUCTMODEL_UPLOAD_FAIL);
                }
                FileInfoResponse uploadRespData = uploadResp.getData().get(0);
                FileRequest fileInfoReq = new FileRequest();
                fileInfoReq.setFileIds(Arrays.asList(uploadRespData.getId()));
                fileInfoReq.setContentDisposition("attachment;");
                log.info("请求【文件信息】接口，req：{}", JSON.toJSON(fileInfoReq));
                R<FileDetailResponse> fileInfoResp = remoteFileFeign.getFileInfo(fileInfoReq);
                log.info("请求【文件信息】接口，resp：{}", JSON.toJSON(fileInfoResp));
                if (!fileInfoResp.isSuccess()) {
                    throw new ErrorCodeException(CommonErrorCode.PRODUCTMODEL_GETFILEINFO_FAIL);
                }
                fileInfoRespData = fileInfoResp.getData();
                fileInfoRespData.setOriginalFilename(uploadRespData.getOriginalFilename());
            }

            if (!verify.verify()) {
                return R.fail(new ErrorCodeException(CommonErrorCode.IMPORT_VERIFY_FAIL), fileInfoRespData);
            }
            return R.success(true);
        }
        catch (ExcelDataConvertException e1) {
            log.error("导入失败: ", e1);
            if (LanguageEnum.China == language ){
                return R.fail("文件读取异常，数据转换失败：( 第" + (e1.getRowIndex() + 1) + "行，第" + (e1.getColumnIndex() + 1) + "列)"
                        + e1.getLocalizedMessage());
            } else{
                return R.fail("File reading exception, data conversion failed: (Row " + (e1.getRowIndex() + 1) + ", Column "
                        + (e1.getColumnIndex() + 1) + ")" + e1.getLocalizedMessage());
            }
        }
        catch (Exception e2) {
            log.error("导入失败: ", e2);
            if (LanguageEnum.China == language ){
                return R.fail("文件读取失败,请联系管理员!");
            } else {
                return R.fail("File reading failed, please contact the admin!");
            }
        }
        finally {
            long end = System.currentTimeMillis();
            log.info("导入操作整体耗时：{}ms", end - begin);
        }
    }

    /**
     * 导出
     */
    @PostMapping("/export")
    @PreAuthorize("hasPermission('tms:port:info:export')")
    public void export(@RequestBody @Valid PortInfoCmd cmd, HttpServletRequest req, HttpServletResponse response) throws IOException {
        String fileName = null;
        LanguageEnum language = LanguageUtil.getLanguage(req);
        if (language == LanguageEnum.China) {
            fileName = "港口配置-导出.xlsx";
        }
        else {
            fileName = "PortInfo-Export.xlsx";
        }

        // 转换对象
        PortInfoRequest request = portInfoConverter.cmdToRequest(cmd);

        // 查询数据
        List<PortInfoResponse> list = portInfoService.list(request);

        // 港口类型对应的字典
        List<Dict.ParamItem> portTypeDict = commonService.getDict("PORT_TYPE");

        // 转成导出对象
        List<PortInfoExport> exportList = portInfoDBConverter.listToExportList(list);
        for (PortInfoExport export : exportList) {
            String portTypeDes = commonService.getDictDescByItem(export.getPortType(), portTypeDict);
            if(StringUtils.isNotBlank(portTypeDes)){
                export.setPortType(portTypeDes);
            }
            export.backParam(language);
        }

        // 处理 Excel 文件
        ByteArrayOutputStream ous = Excel18nTools.handleExcelFile(exportList, PortInfoExport.class);
        // 写出去
        Excel18nTools.writeExcelFile(fileName, ous, response);
    }
}
