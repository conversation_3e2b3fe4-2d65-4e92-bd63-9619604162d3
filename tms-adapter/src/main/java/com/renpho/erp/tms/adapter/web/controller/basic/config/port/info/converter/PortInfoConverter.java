package com.renpho.erp.tms.adapter.web.controller.basic.config.port.info.converter;

import com.renpho.erp.tms.adapter.web.controller.basic.config.port.info.cmd.PortInfoAddListCmd;
import com.renpho.erp.tms.adapter.web.controller.basic.config.port.info.cmd.PortInfoCmd;
import com.renpho.erp.tms.adapter.web.controller.basic.config.port.info.cmd.PortInfoUpdateListCmd;
import com.renpho.erp.tms.domain.portinfo.dto.PortInfoAddListRequest;
import com.renpho.erp.tms.domain.portinfo.dto.PortInfoRequest;
import com.renpho.erp.tms.domain.portinfo.dto.PortInfoUpdateListRequest;
import com.renpho.erp.tms.infrastructure.common.converter.StatusConverter;
import com.renpho.erp.tms.infrastructure.remote.user.converter.OperatorConverter;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

/**
 * 港口配置转换器.
 *
 * <AUTHOR>
 * @since 2025/6/11
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE,
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {OperatorConverter.class,
                StatusConverter.class})
public interface PortInfoConverter {

    PortInfoRequest cmdToRequest(PortInfoCmd cmd);

    PortInfoAddListRequest cmdAddToRequest(PortInfoAddListCmd cmd);

    PortInfoUpdateListRequest cmdUpdateToRequest(PortInfoUpdateListCmd cmd);
}
