package com.renpho.erp.tms.adapter.web.controller.customsinfo.command.converter;

import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.*;
import com.renpho.erp.tms.client.customsinfo.request.SubmitPskuCustomsInfoItem;
import com.renpho.erp.tms.domain.currency.CurrencyId;
import com.renpho.erp.tms.domain.customsinfo.*;
import com.renpho.erp.tms.infrastructure.persistence.customsinfo.PskuCustomsInfoConverter;
import com.renpho.erp.tms.infrastructure.persistence.regiontimezone.converter.CountryTimeZoneConverter;
import com.renpho.erp.tms.infrastructure.remote.countryregion.converter.CountryRegionConverter;
import com.renpho.erp.tms.infrastructure.remote.currency.converter.CurrencyConverter;
import com.renpho.erp.tms.infrastructure.remote.product.converter.ProductConverter;
import com.renpho.erp.tms.infrastructure.remote.user.converter.OperatorConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.util.*;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING,
        imports = {PskuCustomInfoStatus.class, Optional.class, StringUtils.class, CurrencyId.class},
        uses = {PskuCustomsAdditionalDutyCommandConverter.class, PskuCustomsInfoConverter.class, OperatorConverter.class, CountryRegionConverter.class, CountryTimeZoneConverter.class, CurrencyConverter.class, ProductConverter.class})
public interface PskuCustomsInfoCommandConverter {

    @BeanMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    @Mapping(target = "likePsku", source = "psku")
    @Mapping(target = "psku", ignore = true)
    PskuCustomsInfoQuery toQuery(ListQuery query);

    @Mapping(target = "id", source = "id")
    PskuCustomsInfoId toQuery(IdQuery query);

    @Mapping(target = "status", ignore = true)
    @Mapping(target = "countryCode", source = "countryCode")
    PskuCustomsInfoQuery toQuery(PskuCustomsInfoUploadExcelWithError upload);

    @Mapping(target = "productId", source = "productId")
    @Mapping(target = "product", source = "product")
    @Mapping(target = "psku", source = "product.psku")
    PskuCustomsInfo toCommand(AddCmd cmd);

    @InheritConfiguration
    PskuCustomsInfo toCommand(UpdateCmd cmd);

    default List<PskuCustomsInfoId> toCommands(UpdateStatusCmd cmd) {
        Optional<UpdateStatusCmd> optional = Optional.ofNullable(cmd);
        return optional.map(UpdateStatusCmd::getIds)
                .stream()
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .map(PskuCustomsInfoId::new)
                .toList();
    }

    default PskuCustomsInfo toCommand(PskuCustomsInfoUploadExcelWithError upload) {
        PskuCustomsInfo command = toUploadCommand(upload);
        PskuCustomsAdditionalDuty additionalDuty = toAdditionalDuty(upload);
        if (StringUtils.isNotBlank(additionalDuty.getName()) && additionalDuty.getDutyRate() != null && additionalDuty.getEffectiveTimeLocal() != null) {
            command.setAdditionalDuties(new ArrayList<>(List.of(additionalDuty)));
        }
        return command;
    }

    @Mapping(target = "psku", source = "psku")
    @Mapping(target = "product.psku", source = "psku")
    @Mapping(target = "countryCode", source = "countryCode")
    @Mapping(target = "countryRegion.code", source = "countryCode")
    @Mapping(target = "hsCodeAddTax", source = "hsCodeAddTax")
    @Mapping(target = "hsCodeReducedTax", source = "hsCodeReducedTax")
    @Mapping(target = "currencyCode", source = "currencyCode")
    @Mapping(target = "currency.id", source = "currencyCode")
    @Mapping(target = "status", source = "status")
    @Mapping(target = "baseDutyRate", source = "baseDutyRate", conditionExpression = "java(Optional.ofNullable(upload.getBaseDutyRate()).filter(StringUtils::isNotBlank).filter(s -> s.matches(\"^(?:0|[1-9]\\\\d*)(?:\\\\.\\\\d{1,2})?$\")).isPresent())")
    @Mapping(target = "others", source = "others", conditionExpression = "java(Optional.ofNullable(upload.getOthers()).filter(StringUtils::isNotBlank).filter(s -> s.matches(\"^(?:0|[1-9]\\\\d*)(?:\\\\.\\\\d{1,2})?$\")).isPresent())")
    @Mapping(target = "gst", source = "gst", conditionExpression = "java(Optional.ofNullable(upload.getGst()).filter(StringUtils::isNotBlank).filter(s -> s.matches(\"^(?:0|[1-9]\\\\d*)(?:\\\\.\\\\d{1,2})?$\")).isPresent())")
    @Mapping(target = "vat", source = "vat", conditionExpression = "java(Optional.ofNullable(upload.getVat()).filter(StringUtils::isNotBlank).filter(s -> s.matches(\"^(?:0|[1-9]\\\\d*)(?:\\\\.\\\\d{1,2})?$\")).isPresent())")
    PskuCustomsInfo toUploadCommand(PskuCustomsInfoUploadExcelWithError upload);

    @Mapping(target = "name", source = "additionalDutyName")
    @Mapping(target = "dutyRate", source = "additionalDutyRate", conditionExpression = "java(Optional.ofNullable(upload.getAdditionalDutyRate()).filter(StringUtils::isNotBlank).filter(s -> s.matches(\"^(?:0|[1-9]\\\\d*)(?:\\\\.\\\\d{1,2})?$\")).isPresent())")
    @Mapping(target = "effectiveTimeLocal", source = "additionalDutyEffectiveTime")
    @Mapping(target = "status", ignore = true)
    PskuCustomsAdditionalDuty toAdditionalDuty(PskuCustomsInfoUploadExcelWithError upload);

    PskuCustomsInfoUploadExcelWithError toError(PskuCustomsInfoUploadExcel excel);

    @Mapping(target = "psku", source = "psku")
    @Mapping(target = "product.psku", source = "psku")
    @Mapping(target = "countryCode", source = "countryCode")
    @Mapping(target = "countryRegion.code", source = "countryCode")
    @Mapping(target = "currencyCode", constant = "USD")
    @Mapping(target = "others", constant = "100.00")
    @Mapping(target = "hsCodeAddTax", constant = "0.00")
    @Mapping(target = "hsCodeReducedTax", constant = "0.00")
    @Mapping(target = "currency.id", expression = "java(new CurrencyId(\"USD\"))")
    @Mapping(target = "status", expression = "java(PskuCustomInfoStatus.DRAFT)")
    PskuCustomsInfo toCommitCommand(SubmitPskuCustomsInfoItem item);

    List<PskuCustomsInfo> toBatchCommitCommands(Collection<SubmitPskuCustomsInfoItem> items);

    default PskuCustomsInfoQuery toCommitQuery(Collection<SubmitPskuCustomsInfoItem> items) {
        PskuCustomsInfoQuery query = new PskuCustomsInfoQuery();
        query.setPskus(new LinkedList<>());
        query.setCountryCodes(new LinkedList<>());
        for (SubmitPskuCustomsInfoItem item : CollectionUtils.emptyIfNull(items)) {
            Optional.ofNullable(item.psku()).filter(StringUtils::isNotBlank).ifPresent(query.getPskus()::add);
            Optional.ofNullable(item.countryCode()).filter(StringUtils::isNotBlank).ifPresent(query.getCountryCodes()::add);
        }
        return query;
    }

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "countryCode", source = "countryCode")
    @Mapping(target = "countryRegion.code", source = "countryCode")
    @Mapping(target = "additionalDuties", source = "additionalDuties")
    PskuCustomsInfo toDomain(CountryTimeZoneQuery query);

}
