package com.renpho.erp.tms.adapter.web.controller.command.validator;

import com.renpho.erp.tms.adapter.web.controller.command.container.CountryCodeContainer;
import com.renpho.erp.tms.infrastructure.common.converter.BooleanConverter;
import com.renpho.erp.tms.infrastructure.remote.countryregion.repository.CountryRegionLookup;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

import java.lang.annotation.Annotation;
import java.util.Optional;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @since 2025/4/25
 */
public abstract class CountryRegionValidator<A extends Annotation, T> implements ConstraintValidator<A, T> {

    protected Predicate<T> predicate;

    @Resource
    protected CountryRegionLookup countryRegionLookup;

    @Resource
    protected BooleanConverter booleanConverter;

    @Override
    public boolean isValid(T value, ConstraintValidatorContext context) {
        return countryRegionLookup == null || predicate.test(value);
    }

    public static class IsCountryRegionExist extends CountryRegionValidator<CountryRegionExist, CountryCodeContainer> {
        @Override
        public void initialize(CountryRegionExist constraintAnnotation) {
            predicate = cmd -> Optional.ofNullable(cmd)
                    .map(CountryCodeContainer::getCountryCode)
                    .filter(StringUtils::isNotBlank)
                    .flatMap(countryRegionLookup::findByCode)
                    .filter(c -> booleanConverter.toBoolean(c.getStatus()))
                    .stream()
                    .peek(c -> Optional.ofNullable(cmd).ifPresent(cm -> cm.setCountryRegion(c)))
                    .findAny()
                    .isPresent();
        }
    }
}
