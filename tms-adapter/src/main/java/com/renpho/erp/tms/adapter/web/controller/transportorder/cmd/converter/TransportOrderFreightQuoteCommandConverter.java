package com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.converter;

import com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.ArgCmd;
import com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.CalculateShippingFeeCmd;
import com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.ForwarderWithShippingFeeCmd;
import com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.TransportOrderForwarderApprovalCmd;
import com.renpho.erp.tms.domain.shippingfeeformula.ShippingFeeFormulaCalculator;
import com.renpho.erp.tms.domain.transportorder.TransportOrder;
import com.renpho.erp.tms.domain.transportorderforwarderapproval.ChargeWeightType;
import com.renpho.erp.tms.domain.transportorderforwarderapproval.TransportOrderFreightQuote;
import com.renpho.erp.tms.domain.transportrequest.TransportRequest;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestId;
import com.renpho.erp.tms.infrastructure.persistence.shippingfeeformula.converter.ShippingFeeFormulaConfigConverter;
import com.renpho.erp.tms.infrastructure.persistence.transportorder.po.converter.TransportOrderFreightQuoteConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {TransportOrderFreightQuoteConverter.class, ShippingFeeFormulaConfigConverter.class})
public interface TransportOrderFreightQuoteCommandConverter {

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "to", source = "to")
    @Mapping(target = "toId", source = "to.id")
    @Mapping(target = "toNo", source = "to.toNo")
    @Mapping(target = "forwarder", source = "cmd.logisticsSupplier")
    @Mapping(target = "forwarderCompanyId", source = "cmd.logisticsSupplier.id")
    @Mapping(target = "forwarderCompanyCode", source = "cmd.logisticsSupplier.supplierCode")
    @Mapping(target = "transportMode", source = "root.type")
    @Mapping(target = "type", source = "cmd.priceType")
    @Mapping(target = "isIncludeTax", source = "cmd.isIncludeTax")
    @Mapping(target = "formulaId", source = "cmd.formulaId")
    @Mapping(target = "formulaArgs", source = "cmd.args")
    @Mapping(target = "amount", source = "cmd.totalAmount")
    @Mapping(target = "currencyCode", constant = "CNY")
    @Mapping(target = "comment", source = "cmd.comment")
    @Mapping(target = "trIds", source = "to.transportRequestList")
    @Mapping(target = "trs", source = "to.transportRequestList")
    @Mapping(target = "totalQty", source = "to.totalQty")
    @Mapping(target = "totalBoxQty", source = "to.totalBoxQty")
    @Mapping(target = "totalGrossWeight", source = "to.totalGrossWeight")
    @Mapping(target = "totalVolume", source = "to.totalVolume")
    @Mapping(target = "chargeWeight", source = "to.chargeWeight")
    @Mapping(target = "chargeWeightType", expression = "java(toChargeWeightType(to))")
    @Mapping(target = "returnName", source = "cmd.returnName")
    @Mapping(target = "status", constant = "false")
    TransportOrderFreightQuote toCommand(TransportOrderForwarderApprovalCmd root, ForwarderWithShippingFeeCmd cmd, TransportOrder to);

    default ChargeWeightType toChargeWeightType(TransportOrder to) {
        to.toChargeWeight();
        return to.getChargeWeightType();
    }

    default Map<String, BigDecimal> toArgs(List<ArgCmd> args) {
        return CollectionUtils.emptyIfNull(args)
                .stream()
                .filter(Objects::nonNull)
                .filter(a -> StringUtils.isNotBlank(a.getName()) && a.getValue() != null)
                .collect(Collectors.toMap(ArgCmd::getName, ArgCmd::getValue));
    }

    default List<TransportRequestId> toTrIds(List<TransportRequest> trs) {
        return CollectionUtils.emptyIfNull(trs)
                .stream().filter(Objects::nonNull)
                .map(TransportRequest::getId).filter(Objects::nonNull)
                .toList();
    }

    default List<TransportOrderFreightQuote> toCommands(TransportOrderForwarderApprovalCmd cmd) {
        return CollectionUtils.emptyIfNull(cmd.getForwarderWithShippingFee())
                .stream().flatMap(c -> cmd.getTos().stream().map(to -> toCommand(cmd, c, to)))
                .toList();
    }

    default List<ShippingFeeFormulaCalculator> toCalculateCommands(CalculateShippingFeeCmd cmd) {
        return cmd.getTos().stream().flatMap(to -> Stream.of(toCalculateCommandWithTax(cmd, to), toCalculateCommandNoTax(cmd, to))).toList();
    }

    @Mapping(target = "formulaId", source = "cmd.id")
    @Mapping(target = "args", source = "cmd.argsWithTax")
    @Mapping(target = "isIncludeTax", constant = "true")
    @Mapping(target = "to", source = "to")
    ShippingFeeFormulaCalculator toCalculateCommandWithTax(CalculateShippingFeeCmd cmd, TransportOrder to);

    @InheritConfiguration
    @Mapping(target = "args", source = "cmd.argsNoTax")
    @Mapping(target = "isIncludeTax", constant = "false")
    ShippingFeeFormulaCalculator toCalculateCommandNoTax(CalculateShippingFeeCmd cmd, TransportOrder to);
}