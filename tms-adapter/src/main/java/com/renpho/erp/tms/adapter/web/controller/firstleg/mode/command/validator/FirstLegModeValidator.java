package com.renpho.erp.tms.adapter.web.controller.firstleg.mode.command.validator;

import com.renpho.erp.tms.domain.firstleg.mode.FirstLegModeId;
import com.renpho.erp.tms.domain.firstleg.mode.FirstLegModeLookup;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.Optional;
import java.util.function.Predicate;


/**
 * <AUTHOR>
 * @since 2025/4/23
 */
public abstract class FirstLegModeValidator<T> implements ConstraintValidator<FirstLegModeExist, T> {
    @Resource
    protected FirstLegModeLookup firstLegModeLookup;
    protected Predicate<T> predicate;

    @Override
    public boolean isValid(T value, ConstraintValidatorContext context) {
        return firstLegModeLookup == null || predicate.test(value);

//        return firstLegModeLookup.findById(new FirstLegModeId(id)).isPresent();
    }

    public static class FirstLegModeIdExistValidator extends FirstLegModeValidator<Integer> {
        @Override
        public void initialize(FirstLegModeExist constraintAnnotation) {
            predicate = id -> Optional.ofNullable(id)
                    .map(FirstLegModeId::new)
                    .flatMap(firstLegModeLookup::findById)
                    .isPresent();
        }
    }

    public static class FirstLegModeNameExistValidator extends FirstLegModeValidator<String> {
        @Override
        public void initialize(FirstLegModeExist constraintAnnotation) {
            predicate = modeName -> Optional.ofNullable(modeName)
                    .flatMap(firstLegModeLookup::findByName)
                    .isPresent();
        }
    }
}
