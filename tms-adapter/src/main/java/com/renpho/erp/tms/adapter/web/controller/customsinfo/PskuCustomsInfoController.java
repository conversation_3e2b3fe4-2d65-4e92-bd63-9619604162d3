/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.12.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.renpho.erp.tms.adapter.web.controller.customsinfo;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.fhs.core.trans.anno.TransMethodResult;
import com.fhs.trans.service.impl.TransService;
import com.renpho.erp.ftm.client.response.FileInfoResponse;
import com.renpho.erp.log.repository.annotation.LogRecord;
import com.renpho.erp.log.repository.support.LogRecordContextHolder;
import com.renpho.erp.security.annotation.Inner;
import com.renpho.erp.tms.adapter.web.controller.command.validator.*;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.*;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.converter.PskuCustomsInfoCommandConverter;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.converter.PskuCustomsInfoUploadListener;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.validator.CountryTimeZoneExist;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.validator.PskuCustomInfoNotExist;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.logger.PskuCustomsInfoLogger;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.vo.PskuCustomsInfoDownloadExcel;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.vo.PskuCustomsInfoVo;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.vo.converter.PskuCustomsInfoVoConverter;
import com.renpho.erp.tms.adapter.web.controller.vo.UploadResult;
import com.renpho.erp.tms.application.customsinfo.PskuCustomsInfoQueryService;
import com.renpho.erp.tms.application.customsinfo.PskuCustomsInfoService;
import com.renpho.erp.tms.client.customsinfo.RemoteCustomsInfoService;
import com.renpho.erp.tms.client.customsinfo.request.SubmitCustomsInfoRequest;
import com.renpho.erp.tms.domain.customsinfo.*;
import com.renpho.erp.tms.domain.exception.BizErrorCode;
import com.renpho.erp.tms.infrastructure.common.oplog.LogModule;
import com.renpho.erp.tms.infrastructure.persistence.customsinfo.PskuCustomsInfoConverter;
import com.renpho.erp.tms.infrastructure.remote.countryregion.repository.CountryRegionLookup;
import com.renpho.erp.tms.infrastructure.remote.currency.repository.CurrencyLookup;
import com.renpho.erp.tms.infrastructure.remote.file.RemoteFileFeign;
import com.renpho.erp.tms.infrastructure.remote.product.repository.ProductLookup;
import com.renpho.erp.tms.infrastructure.util.excel.I18nHeaderHandler;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import com.renpho.karma.exception.ApiException;
import com.renpho.karma.exception.ErrorMessageKit;
import com.renpho.karma.exception.error.PlatformErrorCode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Generated;
import jakarta.validation.Valid;
import jakarta.validation.Validator;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * PSKU 清关信息
 *
 * <AUTHOR>
 * @since 2025/4/23
 */
@RequiredArgsConstructor
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-04-22T12:09:54.179410400+08:00[Asia/Shanghai]", comments = "Generator version: 7.12.0")
@Tag(name = "customs info", description = "PSKU 清关信息")
@ShenyuSpringCloudClient("/customs/info/**")
@RestController
@RequestMapping("/customs/info")
@Validated
public class PskuCustomsInfoController implements RemoteCustomsInfoService {

    private final PskuCustomsInfoQueryService pskuCustomsInfoQueryService;
    private final PskuCustomsInfoService pskuCustomsInfoService;

    private final PskuCustomsInfoCommandConverter pskuCustomsInfoCommandConverter;
    private final PskuCustomsInfoVoConverter pskuCustomsInfoVoConverter;
    private final PskuCustomsInfoConverter pskuCustomsInfoConverter;

    private final Validator validator;
    private final ProductLookup productLookup;
    private final PskuCustomsInfoLookup pskuCustomsInfoLookup;
    private final RemoteFileFeign remoteFileFeign;
    private final CountryRegionLookup countryRegionLookup;
    private final CurrencyLookup currencyLookup;
    private final TransService transService;
    private final PskuCustomsInfoLogger pskuCustomsInfoLogger;

    /**
     * 分页查询 PSKU 清关信息
     *
     * <AUTHOR>
     * @since 2025/4/23
     */
    @Operation(operationId = "pageCustomsInfo", description = "分页查询 PSKU 清关信息", tags = {"customs info"})
    @PostMapping(value = "/page")
    @TransMethodResult
    @PreAuthorize("hasPermission('tms:pskuCustomsInfo:list')")
    public R<Paging<PskuCustomsInfoVo>> page(@RequestBody(required = false) ListQuery query) {
        PskuCustomsInfoQuery condition = pskuCustomsInfoCommandConverter.toQuery(query);
        Paging<PskuCustomsInfo> paging = pskuCustomsInfoQueryService.findPage(condition, query);
        return R.success(pskuCustomsInfoVoConverter.toPagesVo(paging));
    }

    /**
     * 查询 PSKU 清关信息详情
     *
     * <AUTHOR>
     * @since 2025/4/23
     */
    @Operation(operationId = "getCustomsInfoDetail", description = "查询 PSKU 清关信息详情", tags = {"customs info"})
    @PostMapping(value = "/detail")
    @TransMethodResult
    @PreAuthorize("hasPermission('tms:pskuCustomsInfo:list')")
    public R<PskuCustomsInfoVo> detail(@Valid @IdExist @RequestBody IdQuery query) {
        PskuCustomsInfoId id = pskuCustomsInfoCommandConverter.toQuery(query);
        PskuCustomsInfo domain = pskuCustomsInfoQueryService.findById(id).orElseThrow();
        return R.success(pskuCustomsInfoVoConverter.toVo(domain));
    }

    /**
     * 新增 PSKU 清关信息
     *
     * <AUTHOR>
     * @since 2025/4/23
     */
    @Operation(operationId = "addCustomsInfo", description = "新增 PSKU 清关信息", tags = {"customs info"})
    @PostMapping(value = "/add")
    @PreAuthorize("hasPermission('tms:pskuCustomsInfo:new')")
    @LogRecord(module = LogModule.CUSTOMS_INFO_LOG_MODULE, type = LogModule.CommonDesc.INSERT_OPERATOR, desc = LogModule.CommonDesc.INSERT_DESC)
    public R<Integer> add(@Valid @ProductExist @CountryRegionExist @CountryTimeZoneExist @CurrencyExist @ItemsExist @RequestBody @PskuCustomInfoNotExist AddCmd cmd) {
        PskuCustomsInfo command = pskuCustomsInfoCommandConverter.toCommand(cmd);
        PskuCustomsInfo domain = pskuCustomsInfoService.add(command);
        PskuCustomsInfoVo vo = pskuCustomsInfoQueryService.findById(domain.getId())
                .map(pskuCustomsInfoVoConverter::toVo)
                .orElseThrow(() -> new ApiException(BizErrorCode.DATA_DOES_NOT_EXIST));
        LogRecordContextHolder.putRecordData(String.valueOf(vo.getId()), null, vo);
        return R.success(vo.getId());
    }

    /**
     * 编辑 PSKU 清关信息
     *
     * <AUTHOR>
     * @since 2025/4/23
     */
    @Operation(operationId = "updateCustomsInfo", description = "编辑 PSKU 清关信息", tags = {"customs info"})
    @PostMapping(value = "/update")
    @PreAuthorize("hasPermission('tms:pskuCustomsInfo:edit')")
    @LogRecord(module = LogModule.CUSTOMS_INFO_LOG_MODULE, type = LogModule.CommonDesc.EDIT_OPERATOR, desc = LogModule.CommonDesc.EDIT_DESC)
    public R<Integer> update(@Valid @IdExist @ProductExist @CountryRegionExist @CountryTimeZoneExist @CurrencyExist @ItemsExist @PskuCustomInfoNotExist @RequestBody UpdateCmd cmd) {
        PskuCustomsInfo command = pskuCustomsInfoCommandConverter.toCommand(cmd);
        PskuCustomsInfoVo old = pskuCustomsInfoQueryService.findById(command.getId())
                .map(pskuCustomsInfoVoConverter::toVo)
                .orElseThrow(() -> new ApiException(BizErrorCode.DATA_DOES_NOT_EXIST));
        PskuCustomsInfo domain = pskuCustomsInfoService.update(command);
        PskuCustomsInfoVo vo = pskuCustomsInfoQueryService.findById(domain.getId())
                .map(pskuCustomsInfoVoConverter::toVo)
                .orElseThrow(() -> new ApiException(BizErrorCode.DATA_DOES_NOT_EXIST));
        LogRecordContextHolder.putRecordData(String.valueOf(vo.getId()), old, vo);
        return R.success(vo.getId());
    }

    /**
     * 修改 PSKU 清关信息状态
     *
     * <AUTHOR>
     * @since 2025/4/23
     */
    @Operation(operationId = "updateCustomsInfoStatus", description = "修改 PSKU 清关信息状态", tags = {"customs info"})
    @PostMapping(value = "/status/update")
    @PreAuthorize("hasPermission('tms:pskuCustomsInfo:editstatus')")
    @LogRecord(module = LogModule.CUSTOMS_INFO_LOG_MODULE, type = LogModule.CommonDesc.EDIT_OPERATOR, desc = LogModule.CommonDesc.EDIT_DESC)
    public R<List<Integer>> updateStatus(@Valid @IdsExist @RequestBody UpdateStatusCmd cmd) {
        List<PskuCustomsInfoId> commands = pskuCustomsInfoCommandConverter.toCommands(cmd);
        List<PskuCustomsInfoVo> olds = pskuCustomsInfoVoConverter.toVos(pskuCustomsInfoQueryService.findByIds(commands));
        List<PskuCustomsInfoId> ids = pskuCustomsInfoService.updateStatus(commands, cmd.getStatus());
        List<PskuCustomsInfoVo> vos = pskuCustomsInfoVoConverter.toVos(pskuCustomsInfoQueryService.findByIds(ids));
        for (int i = 0; i < vos.size(); i++) {
            PskuCustomsInfoVo old = olds.get(i);
            PskuCustomsInfoVo vo = vos.get(i);
            LogRecordContextHolder.putRecordData(String.valueOf(vo.getId()), old, vo);
        }
        return R.success(pskuCustomsInfoConverter.toIds(ids));
    }

    /**
     * 下载 PSKU 清关信息导入模板
     *
     * <AUTHOR>
     * @since 2025/4/23
     */
    @Operation(operationId = "uploadTemplate", description = "下载 PSKU 清关信息导入模板", tags = {"purchase request"})
    @PostMapping(path = "/upload/template", produces = {"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"})
    @PreAuthorize("hasPermission('tms:pskuCustomsInfo:import')")
    public Resource uploadTemplate() {
        List<PskuCustomsInfoUploadExcel> empty = List.of(new PskuCustomsInfoUploadExcel());
        try (ByteArrayOutputStream output = new ByteArrayOutputStream()) {
            EasyExcel.write(output)
                    .head(PskuCustomsInfoUploadExcel.class)
                    .registerWriteHandler(new I18nHeaderHandler())
                    .registerWriteHandler(new PskuCustomsInfoUploadExcel.StatusDropdownSheetWriteHandler())
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet()
                    .doWrite(empty);
            return new ByteArrayResource(output.toByteArray());
        } catch (IOException e) {
            throw new ApiException(PlatformErrorCode.SYSTEM_EXCEPTION, e);
        }
    }

    /**
     * 导入 PSKU 清关信息
     *
     * <AUTHOR>
     * @since 2025/4/23
     */
    @Operation(operationId = "uploadCustomsInfo", description = "导入 PSKU 清关信息", tags = {"customs info"})
    @PostMapping(value = "/upload", produces = {"application/json"}, consumes = {"multipart/form-data"})
    @PreAuthorize("hasPermission('tms:pskuCustomsInfo:import')")
    public R<UploadResult> upload(@RequestPart(value = "file") MultipartFile file) {
        try {
            PskuCustomsInfoUploadListener listener = new PskuCustomsInfoUploadListener(validator, productLookup, countryRegionLookup, currencyLookup, pskuCustomsInfoLookup, pskuCustomsInfoCommandConverter);
            EasyExcel.read(file.getInputStream())
                    .head(PskuCustomsInfoUploadExcel.class)
                    .registerReadListener(listener)
                    .sheet().doRead();
            if (listener.isError()) {
                List<PskuCustomsInfoUploadExcelWithError> errors = ListUtils.emptyIfNull(listener.getLines());
                if (errors.stream().map(PskuCustomsInfoUploadExcelWithError::getError).anyMatch(StringUtils::isNotBlank)) {
                    // 导出错误结果
                    return exportError(file.getOriginalFilename(), errors);
                }
            }
            Collection<PskuCustomsInfo> commands = listener.getUploads().values();
            List<PskuCustomsInfo> domains = pskuCustomsInfoService.addBatch(commands);
            List<PskuCustomsInfoVo> vos = pskuCustomsInfoVoConverter.toVos(domains);
            // 日志
            pskuCustomsInfoLogger.logUploadRecords(vos);
            return R.success(UploadResult.success());
        } catch (IOException e) {
            throw new ApiException(e, PlatformErrorCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 将导入失败的 PR 单记录上传到 FTM
     *
     * <AUTHOR>
     * @since 2025/4/11
     */
    private R<UploadResult> exportError(String filename, List<PskuCustomsInfoUploadExcelWithError> uploads) throws IOException {
        try (ByteArrayOutputStream output = new ByteArrayOutputStream()) {
            EasyExcel.write(output)
                    .head(PskuCustomsInfoUploadExcelWithError.class)
                    .registerWriteHandler(new I18nHeaderHandler())
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet()
                    .doWrite(uploads);
            byte[] bytes = output.toByteArray();
            List<FileInfoResponse> results = remoteFileFeign.uploadPartitionByDate(filename, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", bytes, "PMS_UPLOAD_ERROR_FILES");
            if (CollectionUtils.isEmpty(results)) {
                return R.fail(ErrorMessageKit.getMessage(PlatformErrorCode.SYSTEM_EXCEPTION));
            } else {
                return R.success(UploadResult.error(results.get(0).getId()));
            }
        }
    }

    /**
     * 导出 PSKU 清关信息
     *
     * <AUTHOR>
     * @since 2025/4/23
     */
    @Operation(operationId = "downloadCustomsInfo", description = "导出 PSKU 清关信息", tags = {"customs info"})
    @PostMapping(value = "/download")
    @PreAuthorize("hasPermission('tms:pskuCustomsInfo:export')")
    public Resource download(@RequestBody(required = false) ListQuery query) {
        try (ByteArrayOutputStream ous = new ByteArrayOutputStream()) {
            PskuCustomsInfoQuery condition = pskuCustomsInfoCommandConverter.toQuery(query);
            List<PskuCustomsInfo> domains = pskuCustomsInfoQueryService.findAll(condition);
            for (PskuCustomsInfo domain : CollectionUtils.emptyIfNull(domains)) {
                CollectionUtils.emptyIfNull(domain.getAdditionalDuties())
                        .removeIf(additionalDuty -> additionalDuty.getStatus() == PskuAdditionalDutyStatus.EXPIRED);
            }
            List<PskuCustomsInfoDownloadExcel> vos = pskuCustomsInfoVoConverter.toExcels(domains);
            transService.transBatch(vos);
            // 日志
            pskuCustomsInfoLogger.logDownloadRecords(query, vos);
            EasyExcel.write(ous)
                    .head(PskuCustomsInfoDownloadExcel.class)
                    .registerWriteHandler(new I18nHeaderHandler())
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet()
                    .doWrite(vos);
            return new ByteArrayResource(ous.toByteArray());
        } catch (IOException e) {
            throw new ApiException(e, PlatformErrorCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 时区转换
     *
     * <AUTHOR>
     * @since 2025/4/23
     */
    @Operation(operationId = "convertTimeZone", description = "时区转换", tags = {"customs info"})
    @PostMapping(value = "/timezone/convert")
    @TransMethodResult
    public R<PskuCustomsInfoVo> convertTimeZone(@Valid @RequestBody CountryTimeZoneQuery query) {
        PskuCustomsInfo domain = pskuCustomsInfoCommandConverter.toDomain(query);
        pskuCustomsInfoLookup.findCountryCodeAssociations(List.of(domain));
        domain.updateEffectiveStatus();
        PskuCustomsInfoVo vo = pskuCustomsInfoVoConverter.toVo(domain);
        return R.success(vo);
    }

    /**
     * 推送 PSKU 清关信息
     *
     * <AUTHOR>
     * @since 2025/4/28
     */
    @Inner
    @LogRecord(module = LogModule.CUSTOMS_INFO_LOG_MODULE, type = LogModule.CommonDesc.EDIT_OPERATOR, desc = LogModule.CommonDesc.EDIT_DESC)
    public R<List<Integer>> commit(@Valid @RequestBody SubmitCustomsInfoRequest request) {
        PskuCustomsInfoQuery query = pskuCustomsInfoCommandConverter.toCommitQuery(request.getItems());

        Map<Tuple2<String, String>, List<PskuCustomsInfoVo>> olds = pskuCustomsInfoQueryService.findAll(query)
                .stream()
                .map(pskuCustomsInfoVoConverter::toVo)
                .collect(Collectors.groupingBy(d -> Tuples.of(d.getPsku(), d.getCountryCode())));

        List<PskuCustomsInfo> commands = pskuCustomsInfoCommandConverter.toBatchCommitCommands(request.getItems());
        List<PskuCustomsInfo> domains = pskuCustomsInfoService.commit(commands);

        List<PskuCustomsInfoVo> vos = pskuCustomsInfoQueryService.findByIds(domains.stream().map(PskuCustomsInfo::getId).toList())
                .stream()
                .map(pskuCustomsInfoVoConverter::toVo)
                .toList();
        List<Integer> ids = new ArrayList<>(vos.size());
        for (PskuCustomsInfoVo vo : vos) {
            PskuCustomsInfoVo old = CollectionUtils.emptyIfNull(olds.get(Tuples.of(vo.getPsku(), vo.getCountryCode()))).stream().findFirst().orElse(null);
            Integer id = vo.getId();
            ids.add(id);
            Optional<PskuCustomsInfoVo> oldOpt = Optional.ofNullable(old);
            if (oldOpt.isEmpty() || oldOpt.map(PskuCustomsInfoVo::getStatus).filter(s -> !Objects.equals(s, vo.getStatus())).isPresent()) {
                String desc = oldOpt.map(o -> LogModule.CommonDesc.EDIT_DESC).orElse(LogModule.CommonDesc.INSERT_DESC);
                LogRecordContextHolder.putRecordData(String.valueOf(id), old, vo, desc);
            }
        }
        return R.success(ids);
    }

}
