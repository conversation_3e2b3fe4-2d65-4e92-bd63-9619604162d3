package com.renpho.erp.tms.adapter.web.controller.firstleg.transittime.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/4/25
 */
@Data
public class FirstLegCountryRegionExcelExportVO implements VO {
    @ExcelIgnore
    private  Integer id;

    /**
     * 头程方式
     */
    @ExcelProperty(value = "export.first-leg.transit-time.mode", index = 0)
    private String firstLegMode;

    /**
     * 国家(地区)编码
     */
    @ExcelProperty(value = "export.first-leg.transit-time.country", index = 1)
    private String countryCode;

    /**
     * 国家(地区)
     */
    @ExcelIgnore
    private String country;

    /**
     * 区域
     */
    @ExcelProperty(value = "export.first-leg.transit-time.area", index = 2)
    private String area;

    /**
     * 最小时效(天)
     */
    @ExcelProperty(value = "export.first-leg.transit-time.min", index = 3)
    private Integer minTransitTime;

    /**
     * 最大时效(天)
     */
    @ExcelProperty(value = "export.first-leg.transit-time.max", index = 4)
    private Integer maxTransitTime;

    @ExcelIgnore
    @Trans(type = TransType.DICTIONARY, key = "status_dict", ref = "statusName")
    private Integer status;

    /**
     * 状态
     */
    @ExcelProperty(value = "export.first-leg.transit-time.status", index = 5)
    private String statusName;

    /**
     * 备注
     */
    @ExcelProperty(value = "export.first-leg.transit-time.remark", index = 6)
    private String remark;

}
