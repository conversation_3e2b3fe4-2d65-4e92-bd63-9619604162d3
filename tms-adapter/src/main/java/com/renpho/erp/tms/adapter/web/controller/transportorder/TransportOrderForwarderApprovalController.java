package com.renpho.erp.tms.adapter.web.controller.transportorder;

import com.fhs.core.trans.anno.TransMethodResult;
import com.renpho.erp.log.repository.annotation.LogRecord;
import com.renpho.erp.log.repository.support.LogRecordContextHolder;
import com.renpho.erp.tms.adapter.web.controller.command.validator.IdExist;
import com.renpho.erp.tms.adapter.web.controller.command.validator.IdsExist;
import com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.CalculateShippingFeeCmd;
import com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.TransportOrderForwarderApprovalCmd;
import com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.TransportOrderForwarderApprovalIdConfirmCmd;
import com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.TransportOrderForwarderApprovalIdQuery;
import com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.converter.TransportOrderFreightQuoteCommandConverter;
import com.renpho.erp.tms.adapter.web.controller.transportorder.vo.ShippingFeeFormulaCalculatorResultVo;
import com.renpho.erp.tms.adapter.web.controller.transportorder.vo.TransportOrderForwarderQuoteConfirmVo;
import com.renpho.erp.tms.adapter.web.controller.transportorder.vo.converter.TransportOrderFreightQuoteVoConverter;
import com.renpho.erp.tms.application.basicconfig.VolumetricFactorService;
import com.renpho.erp.tms.application.transportorder.TransportOrderForwarderApprovalService;
import com.renpho.erp.tms.application.transportorder.TransportOrderForwarderQueryApprovalService;
import com.renpho.erp.tms.application.transportorder.TransportOrderQueryService;
import com.renpho.erp.tms.domain.shippingfeeformula.ShippingFeeFormulaCalculator;
import com.renpho.erp.tms.domain.shippingfeeformula.ShippingFeeFormulaCalculatorResult;
import com.renpho.erp.tms.domain.transportorder.TransportOrder;
import com.renpho.erp.tms.domain.transportorder.TransportOrderId;
import com.renpho.erp.tms.domain.transportorderforwarderapproval.TransportOrderFreightQuote;
import com.renpho.erp.tms.domain.volumetricfactor.dto.VolumetricFactorConfigResponse;
import com.renpho.erp.tms.infrastructure.common.oplog.LogModule;
import com.renpho.karma.dto.R;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * TO单货代审批接口.
 *
 * <AUTHOR>
 * @since 2025/6/13
 */
@RestController
@RequestMapping("/transport/order/forwarder/approval")
@ShenyuSpringCloudClient("/transport/order/forwarder/approval/**")
@RequiredArgsConstructor
@Tag(name = "transport order forwarder approval", description = "TO单货代审批接口")
public class TransportOrderForwarderApprovalController {

    private final TransportOrderForwarderQueryApprovalService transportOrderForwarderQueryApprovalService;
    private final TransportOrderFreightQuoteCommandConverter transportOrderFreightQuoteCommandConverter;
    private final TransportOrderFreightQuoteVoConverter transportOrderFreightQuoteVoConverter;
    private final TransportOrderForwarderApprovalService transportOrderForwarderApprovalService;
    private final VolumetricFactorService volumetricFactorService;
    private final TransportOrderQueryService transportOrderQueryService;

    /**
     * 计算费用
     *
     * <AUTHOR>
     * @since 2025/6/19
     */
    @PostMapping("/calculate")
    public R<List<ShippingFeeFormulaCalculatorResultVo>> calculate(@RequestBody @Valid @IdsExist CalculateShippingFeeCmd cmd) {
        List<ShippingFeeFormulaCalculator> commands = transportOrderFreightQuoteCommandConverter.toCalculateCommands(cmd);
        List<ShippingFeeFormulaCalculatorResult> results = transportOrderForwarderApprovalService.calculate(commands);
        List<ShippingFeeFormulaCalculatorResultVo> vos = transportOrderFreightQuoteVoConverter.toCalculateResultVos(results);
        return R.success(vos);
    }

    /**
     * 提交 TO 单货代审批
     *
     * <AUTHOR>
     * @since 2025/6/13
     */
    @PostMapping("/submit")
    @LogRecord(module = LogModule.TRANSPORT_ORDER, type = LogModule.CommonDesc.SUBMIT_OPERATOR, desc = LogModule.CommonDesc.SUBMIT_DESC)
    public R<String> submit(@RequestBody @Valid @IdsExist TransportOrderForwarderApprovalCmd cmd) {
        Map<Integer, Integer> volumetrics = CollectionUtils.emptyIfNull(volumetricFactorService.findAllNotDeleted())
                .stream()
                .collect(Collectors.toMap(VolumetricFactorConfigResponse::getFirstLegModeId, VolumetricFactorConfigResponse::getVolumetricFactor));
        for (TransportOrder to : CollectionUtils.emptyIfNull(cmd.getTos())) {
            to.setVolumetricFactor(volumetrics.get(to.getFirstLegMode().getId().id()));
            to.toChargeWeight();
        }
        List<TransportOrderFreightQuote> commands = transportOrderFreightQuoteCommandConverter.toCommands(cmd);
        Set<TransportOrderId> toIds = commands.stream().filter(Objects::nonNull).map(TransportOrderFreightQuote::getToId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<TransportOrderId, TransportOrder> olds = transportOrderQueryService.findByIds(toIds);
        transportOrderForwarderApprovalService.submit(commands);
        Map<TransportOrderId, TransportOrder> news = transportOrderQueryService.findByIds(toIds);
        for (TransportOrderId id : toIds) {
            LogRecordContextHolder.putRecordData(String.valueOf(id.id()), olds.get(id), news.get(id));
        }
        return R.success();
    }

    /**
     * 查询货代审批提交详情(待提交审批用)
     *
     * <AUTHOR>
     * @since 2025/6/13
     */
    @PostMapping("/view")
    @TransMethodResult
    public R<List<TransportOrderForwarderQuoteConfirmVo>> detail(@RequestBody @Valid @IdExist TransportOrderForwarderApprovalIdQuery cmd) {
        List<TransportOrderFreightQuote> domains = transportOrderForwarderQueryApprovalService.findByProcessInstanceId(cmd.getProcessInstanceId());
        Map<TransportOrderId, List<TransportOrderFreightQuote>> toIds = domains.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(TransportOrderFreightQuote::getToId));
        Map<TransportOrderId, List<TransportOrderForwarderQuoteConfirmVo>> vos = transportOrderFreightQuoteVoConverter.toConfirmVos(domains);
        Map<TransportOrderId, Boolean> isToResubmit = transportOrderForwarderQueryApprovalService.isToResubmit(toIds.keySet(), cmd.getProcessInstanceId());
        for (Map.Entry<TransportOrderId, List<TransportOrderForwarderQuoteConfirmVo>> entry : vos.entrySet()) {
            entry.getValue().forEach(vo -> vo.setIsResubmit(isToResubmit.getOrDefault(entry.getKey(), false)));
        }
        return R.success(vos.values().stream().flatMap(Collection::stream).toList());
    }

    /**
     * 选择货代报价
     *
     * <AUTHOR>
     * @since 2025/6/24
     */
    @PostMapping("/confirm")
    public R<String> confirm(@RequestBody @Valid @IdExist TransportOrderForwarderApprovalIdConfirmCmd cmd) {
        List<TransportOrderFreightQuote> commands = transportOrderFreightQuoteVoConverter.toCommands(cmd);
        transportOrderForwarderApprovalService.confirm(commands);
        return R.success();
    }

}
