package com.renpho.erp.tms.adapter.web.controller.transportorder;

import com.renpho.erp.log.repository.annotation.LogRecord;
import com.renpho.erp.log.repository.support.LogRecordContextHolder;
import com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.TransportOrderListCmd;
import com.renpho.erp.tms.infrastructure.common.oplog.LogModule;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/15
 */
@Component
public class TransportOrderControllerLogger {

    @LogRecord(module = LogModule.TRANSPORT_ORDER, type = LogModule.CommonDesc.DOWNLOAD_OPERATOR, desc = LogModule.CommonDesc.DOWNLOAD_DESC)
    public void logDownloadRecords(TransportOrderListCmd cmd, List<Integer> ids) {
        for (Integer id : ids) {
            LogRecordContextHolder.putRecordData(String.valueOf(id), null, null);
        }
    }

    @LogRecord(module = LogModule.TRANSPORT_ORDER, type = LogModule.CommonDesc.DOWNLOAD_OPERATOR, desc = LogModule.TransportOrder.EXPORT_PACKING_LIST_DESC)
    public void logDownloadPackingListRecords(String id) {
        LogRecordContextHolder.putRecordData(id, null, null);
    }

    @LogRecord(module = LogModule.TRANSPORT_ORDER, type = LogModule.CommonDesc.DOWNLOAD_OPERATOR, desc = LogModule.TransportOrder.EXPORT_INVOICE_DESC)
    public void logDownloadInvoiceRecords(String id) {
        LogRecordContextHolder.putRecordData(id, null, null);
    }
}
