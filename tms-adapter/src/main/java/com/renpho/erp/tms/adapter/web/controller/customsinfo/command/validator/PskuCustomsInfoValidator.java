package com.renpho.erp.tms.adapter.web.controller.customsinfo.command.validator;

import com.renpho.erp.tms.adapter.web.controller.command.container.IdsContainer;
import com.renpho.erp.tms.adapter.web.controller.command.container.ItemsContainer;
import com.renpho.erp.tms.adapter.web.controller.command.validator.IdExist;
import com.renpho.erp.tms.adapter.web.controller.command.validator.IdsExist;
import com.renpho.erp.tms.adapter.web.controller.command.validator.ItemsExist;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.AddCmd;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.AdditionalDutyCmd;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.UpdateCmd;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.container.PskuCustomInfoIdContainer;
import com.renpho.erp.tms.domain.customsinfo.PskuCustomsInfo;
import com.renpho.erp.tms.domain.customsinfo.PskuCustomsInfoId;
import com.renpho.erp.tms.domain.customsinfo.PskuCustomsInfoLookup;
import com.renpho.erp.tms.domain.customsinfo.PskuCustomsInfoQuery;
import com.renpho.erp.tms.domain.product.ProductId;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.annotation.Annotation;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/25
 */
public abstract class PskuCustomsInfoValidator<A extends Annotation, T> implements ConstraintValidator<A, T> {

    protected Predicate<T> predicate;

    @Resource
    protected PskuCustomsInfoLookup pskuCustomsInfoLookup;

    @Override
    public boolean isValid(T value, ConstraintValidatorContext context) {
        return pskuCustomsInfoLookup == null || predicate.test(value);
    }


    public static class IsIdExist extends PskuCustomsInfoValidator<IdExist, PskuCustomInfoIdContainer> {
        @Override
        public void initialize(IdExist constraintAnnotation) {
            predicate = cmd -> Optional.ofNullable(cmd)
                    .map(PskuCustomInfoIdContainer::getId)
                    .map(PskuCustomsInfoId::new)
                    .flatMap(pskuCustomsInfoLookup::findById)
                    .isPresent();
        }
    }

    public static class IsIdsExist extends PskuCustomsInfoValidator<IdsExist, IdsContainer<Collection<Integer>, Integer>> {
        @Override
        public void initialize(IdsExist constraintAnnotation) {
            predicate = cmd -> {
                List<PskuCustomsInfoId> ids = Optional.ofNullable(cmd)
                        .map(IdsContainer::getIds)
                        .stream()
                        .flatMap(Collection::stream)
                        .map(PskuCustomsInfoId::new)
                        .toList();
                List<PskuCustomsInfo> domains = pskuCustomsInfoLookup.findByIds(ids);
                if (CollectionUtils.isEmpty(domains)) {
                    return false;
                }
                return domains.stream().map(PskuCustomsInfo::getId).collect(Collectors.toSet()).containsAll(ids);
            };
        }
    }

    public static class IsItemsExist extends PskuCustomsInfoValidator<ItemsExist, ItemsContainer<List<AdditionalDutyCmd>, AdditionalDutyCmd>> {
        @Override
        public void initialize(ItemsExist constraintAnnotation) {
            predicate = cmd -> {
                if (cmd == null) {
                    return false;
                }
                List<AdditionalDutyCmd> items = cmd.getItems();
                if (CollectionUtils.isEmpty(items)) {
                    return true;
                }
                return items.stream().noneMatch(a -> StringUtils.isBlank(a.getName()) || a.getDutyRate() == null || a.getEffectiveTime() == null);
            };
        }
    }

    public static class IsPskuCustomInfoNotExist extends PskuCustomsInfoValidator<PskuCustomInfoNotExist, AddCmd> {
        @Override
        public void initialize(PskuCustomInfoNotExist constraintAnnotation) {
            predicate = cmd -> {
                Optional<AddCmd> optional = Optional.ofNullable(cmd);
                Optional<String> countryCode = optional.map(AddCmd::getCountryCode).filter(StringUtils::isNotBlank);
                Optional<ProductId> productId = optional.map(AddCmd::getProductId).map(ProductId::new);
                if (countryCode.isEmpty() || productId.isEmpty()) {
                    return false;
                }
                PskuCustomsInfoQuery query = new PskuCustomsInfoQuery();
                query.setCountryCode(countryCode.get());
                query.setProductId(productId.get());
                optional.filter(c -> c instanceof UpdateCmd).map(UpdateCmd.class::cast)
                        .map(UpdateCmd::getId).map(PskuCustomsInfoId::new).ifPresent(query::setNotId);
                return CollectionUtils.isEmpty(pskuCustomsInfoLookup.findAll(query));
            };
        }
    }
}
