package com.renpho.erp.tms.adapter.web.controller.firstleg.mode.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/4/21
 */
@Data
public class FirstLegModeListVO implements VO {
    private Integer id;

    /**
     * 中文名称
     */
    private String nameCn;

    /**
     * 英文名称
     */
    private String nameEn;

    /**
     * 名称（会根据语言国际化）
     */
    private String name;

    /**
     * 备注
     */
    private String remark;

    @Trans(type = TransType.DICTIONARY, key = "status_dict", ref = "statusName")
    private Integer status;

    /**
     * 状态
     */
    private String statusName;

}
