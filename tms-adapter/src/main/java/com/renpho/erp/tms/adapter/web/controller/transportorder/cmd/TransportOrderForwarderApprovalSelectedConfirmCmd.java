package com.renpho.erp.tms.adapter.web.controller.transportorder.cmd;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.renpho.erp.tms.adapter.web.controller.command.container.LogisticsSupplierIdContainer;
import com.renpho.erp.tms.adapter.web.controller.transportorder.cmd.container.TransportOrderIdContainer;
import com.renpho.erp.tms.domain.supplier.LogisticsSupplier;
import com.renpho.erp.tms.domain.transportorder.TransportOrder;
import com.renpho.karma.dto.Command;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2025/6/25
 */
@Getter
@Setter
public class TransportOrderForwarderApprovalSelectedConfirmCmd extends Command implements LogisticsSupplierIdContainer, TransportOrderIdContainer<Integer> {

    @Serial
    private static final long serialVersionUID = -8232436242239883840L;

    /**
     * 货代公司 ID(物流供应商 ID)
     */
    @NotNull
    private Integer logisticsSupplierId;

    @JsonIgnore
    private LogisticsSupplier logisticsSupplier;

    /**
     * 是否包含税
     */
    @NotNull
    private Boolean isIncludeTax;

    /**
     * TO 单 ID
     */
    @NotNull
    private Integer toId;

    @JsonIgnore
    private TransportOrder to;

    @Override
    @JsonIgnore
    public Integer getId() {
        return toId;
    }

}
