package com.renpho.erp.tms.adapter.web.controller.shippingompany;

import com.renpho.erp.tms.adapter.web.controller.shippingompany.vo.ShippingCompanyVO;
import com.renpho.erp.tms.adapter.web.controller.shippingompany.vo.converter.ShippingCompanyVOConverter;
import com.renpho.erp.tms.application.shippingcompany.ShippingCompanyService;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 船司.
 * <AUTHOR>
 * @since 2025/6/16
 */
@RestController
@RequestMapping("/shipping/company")
@ShenyuSpringCloudClient("/shipping/company/**")
@RequiredArgsConstructor
public class ShippingCompanyController {
    private final ShippingCompanyService shippingCompanyService;
    private final ShippingCompanyVOConverter shippingCompanyVOConverter;

    /**
     * 查询船司列表.
     * @return 船司列表
     */
    @PostMapping("/list")
    public R<List<ShippingCompanyVO>> findAll(){
        return R.success(shippingCompanyVOConverter.toVOs(shippingCompanyService.findAll()));
    }
}
