package com.renpho.erp.tms.adapter.web.controller.command.validator;

import com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.command.validator.ShippingFeeFormulaValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * <AUTHOR>
 * @since 2025/6/12
 */
@Documented
@Constraint(validatedBy = {ShippingFeeFormulaValidator.IsNameNotExist.class})
@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
@Retention(RUNTIME)
public @interface NameNotExist {

    String message() default "{name.duplicated}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
