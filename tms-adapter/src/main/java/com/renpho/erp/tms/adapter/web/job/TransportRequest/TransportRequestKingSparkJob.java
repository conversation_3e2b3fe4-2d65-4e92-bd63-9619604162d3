package com.renpho.erp.tms.adapter.web.job.TransportRequest;

import com.renpho.erp.tms.application.transportrequest.job.kingspark.TransportRequestKingSparkAddService;
import com.renpho.erp.tms.application.transportrequest.job.kingspark.TransportRequestKingSparkCancelService;
import com.renpho.erp.tms.application.transportrequest.job.kingspark.TransportRequestKingSparkDeliveryService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * TR-目的仓为KingSpark的定时器.
 * <AUTHOR>
 * @since 2025.07.25
 */
@SuppressWarnings("unused")
@Slf4j
@Component
@RequiredArgsConstructor
public class TransportRequestKingSparkJob {

    private final TransportRequestKingSparkAddService transportRequestKingSparkAddService;
    private final TransportRequestKingSparkCancelService transportRequestKingSparkCancelService;
    private final TransportRequestKingSparkDeliveryService transportRequestKingSparkDeliveryService;

    /**
     * TR-目的仓为KingSpark的推送任务生成-Add.
     * <br/>执行频率：
     */
    @XxlJob("createInboundKingSparkAdd")
    public void createInboundKingSparkAdd() throws Exception {
        transportRequestKingSparkAddService.createInboundTask();
    }

    /**
     * 执行: TR-目的仓为KingSpark的入库单任务-Add.
     * <br/>执行频率：
     * @param trNoList tr单号集合，用于异常重试的参数触发机制
     */
    @XxlJob("doingInboundKingSparkAdd")
    public void doingInboundKingSparkAdd(List<String> trNoList) throws Exception {
        transportRequestKingSparkAddService.doingInboundKingSpark(trNoList);
    }

    /**
     * 执行: TR-目的仓为KingSpark的箱唛任务-Add.
     * <br/>执行频率：
     * @param trNoList tr单号集合，用于异常重试的参数触发机制
     */
    @XxlJob("doingPalletKingSparkAdd")
    public void doingPalletKingSparkAdd(List<String> trNoList) throws Exception {
        transportRequestKingSparkAddService.doingPalletKingSpark(trNoList);
    }

    /**
     * TR-目的仓为KingSpark的推送任务生成-Cancel.
     * <br/>执行频率：
     */
    @XxlJob("createInboundKingSparkCancel")
    public void createInboundKingSparkCancelTask() throws Exception {
        transportRequestKingSparkCancelService.createInboundCancelTask();
    }

    /**
     * 执行: TR-目的仓为KingSpark的入库单任务-Cancel.
     * <br/>执行频率：
     * @param trNoList tr单号集合，用于异常重试的参数触发机制
     */
    @XxlJob("doingInboundKingSparkCancel")
    public void doingInboundKingSparkCancel(List<String> trNoList) throws Exception {
        transportRequestKingSparkCancelService.doingInboundKingSpark(trNoList);
    }

    /**
     * TR-目的仓为KingSpark的推送任务生成-Delivery.
     * <br/>执行频率：
     */
    @XxlJob("createInboundKingSparkDelivery")
    public void createInboundKingSparkTaskDelivery() throws Exception {
        transportRequestKingSparkDeliveryService.createInboundDeliveryTask();
    }

    /**
     * 执行: TR-目的仓为KingSpark的入库单任务-Delivery.
     * <br/>执行频率：
     * @param trNoList tr单号集合，用于异常重试的参数触发机制
     */
    @XxlJob("doingInboundKingSparkDelivery")
    public void doingInboundKingSparkDelivery(List<String> trNoList) throws Exception {
        transportRequestKingSparkDeliveryService.doingInboundKingSpark(trNoList);
    }

}
