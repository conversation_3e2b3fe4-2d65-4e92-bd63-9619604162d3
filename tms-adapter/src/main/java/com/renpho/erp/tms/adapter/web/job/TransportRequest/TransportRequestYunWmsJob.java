package com.renpho.erp.tms.adapter.web.job.TransportRequest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.renpho.erp.tms.application.transportrequest.YunWmsService;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestId;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * TR单-极智佳Job
 * <AUTHOR>
 * @since 2025/7/18
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TransportRequestYunWmsJob {
    private final YunWmsService yunWmsService;

    /**
     * 极智佳-中转-创建转运单
     */
    @XxlJob("creatTransferOrder")
    public void creatTransferOrder(){

        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jo = JSON.parseObject(jobParam);
        List<String> trNos = jo.getJSONArray("trNos").toJavaList(String.class);
        List<Integer> trIds = jo.getJSONArray("trIds").toJavaList(Integer.class);
        List<TransportRequestId> ids = CollectionUtils.emptyIfNull(trIds).stream().map(TransportRequestId::new).toList();

        yunWmsService.creatAsn(trNos, ids, true);

    }



    /**
     * 极智佳-中转-获取箱唛（获取Amazon平台TR单箱唛）
     */
    @XxlJob("syncTransferOrderBoxLabel")
    public void syncTransferOrderBoxLabel(){
        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jo = JSON.parseObject(jobParam);
        List<String> trNos = jo.getJSONArray("trNos").toJavaList(String.class);
        List<Integer> trIds = jo.getJSONArray("trIds").toJavaList(Integer.class);
        List<TransportRequestId> ids = CollectionUtils.emptyIfNull(trIds).stream().map(TransportRequestId::new).toList();

        yunWmsService.syncTransferOrderBoxLabel(trNos, ids);

    }


    /**
     * 极智佳-中转-拉取转运单（Amazon平台）
     */
    @XxlJob("pullTransferOrder")
    public void pullTransferOrder(){
        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jo = JSON.parseObject(jobParam);
        List<String> trNos = jo.getJSONArray("trNos").toJavaList(String.class);
        List<Integer> trIds = jo.getJSONArray("trIds").toJavaList(Integer.class);
        List<TransportRequestId> ids = CollectionUtils.emptyIfNull(trIds).stream().map(TransportRequestId::new).toList();

        yunWmsService.pullTransferOrder(trNos, ids);

    }


    /**
     * 极智佳-中转-作废转运单（Amazon平台）
     */
    @XxlJob("cancelTransferOrder")
    public void cancelTransferOrder(){
        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jo = JSON.parseObject(jobParam);
        List<String> trNos = jo.getJSONArray("trNos").toJavaList(String.class);
        List<Integer> trIds = jo.getJSONArray("trIds").toJavaList(Integer.class);
        List<TransportRequestId> ids = CollectionUtils.emptyIfNull(trIds).stream().map(TransportRequestId::new).toList();

        yunWmsService.cancelTransferOrder(trNos, ids);

    }



    /**
     * 极智佳-一件代发-创建入库单
     */
    @XxlJob("creatAsn")
    public void creatAsn(){
        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jo = JSON.parseObject(jobParam);
        List<String> trNos = jo.getJSONArray("trNos").toJavaList(String.class);
        List<Integer> trIds = jo.getJSONArray("trIds").toJavaList(Integer.class);
        List<TransportRequestId> ids = CollectionUtils.emptyIfNull(trIds).stream().map(TransportRequestId::new).toList();

        yunWmsService.creatAsn(trNos, ids, false);

    }


    /**
     * 极智佳-一件代发-获取入库单箱唛
     */
    @XxlJob("syncYplBoxLabel")
    public void syncYplBoxLabel(){
        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jo = JSON.parseObject(jobParam);
        List<String> trNos = jo.getJSONArray("trNos").toJavaList(String.class);
        List<Integer> trIds = jo.getJSONArray("trIds").toJavaList(Integer.class);
        List<TransportRequestId> ids = CollectionUtils.emptyIfNull(trIds).stream().map(TransportRequestId::new).toList();

        yunWmsService.syncYplBoxLabel(trNos, ids);

    }


    /**
     * 极智佳-一件代发-取消入库单（非Amazon平台）
     */
    @XxlJob("cancelAsn")
    public void cancelAsn(){
        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jo = JSON.parseObject(jobParam);
        List<String> trNos = jo.getJSONArray("trNos").toJavaList(String.class);
        List<Integer> trIds = jo.getJSONArray("trIds").toJavaList(Integer.class);
        List<TransportRequestId> ids = CollectionUtils.emptyIfNull(trIds).stream().map(TransportRequestId::new).toList();

        yunWmsService.cancelAsn(trNos, ids);

    }




    /**
     * 极智佳-一件代发-拉取入库单（非Amazon平台）
     */
    @XxlJob("pullAsnList")
    public void pullAsnList(){
        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jo = JSON.parseObject(jobParam);
        List<String> trNos = jo.getJSONArray("trNos").toJavaList(String.class);
        List<Integer> trIds = jo.getJSONArray("trIds").toJavaList(Integer.class);
        List<TransportRequestId> ids = CollectionUtils.emptyIfNull(trIds).stream().map(TransportRequestId::new).toList();

        yunWmsService.pullAsnList(trNos, ids);

    }



}
