package com.renpho.erp.tms.adapter.web.controller.firstleg.mode;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrBuilder;
import com.fhs.core.trans.anno.TransMethodResult;
import com.fhs.trans.service.impl.TransService;
import com.renpho.erp.security.annotation.Inner;
import com.renpho.erp.tms.adapter.web.controller.firstleg.mode.command.*;
import com.renpho.erp.tms.adapter.web.controller.firstleg.mode.command.converter.FirstLegModeCmdConverter;
import com.renpho.erp.tms.adapter.web.controller.firstleg.mode.vo.FirstLegModeExcelExportVO;
import com.renpho.erp.tms.adapter.web.controller.firstleg.mode.vo.FirstLegModeListVO;
import com.renpho.erp.tms.adapter.web.controller.firstleg.mode.vo.FirstLegModePageVO;
import com.renpho.erp.tms.adapter.web.controller.firstleg.mode.vo.converter.FirstLegModeVOConverter;
import com.renpho.erp.tms.application.firstleg.FirstLegModeService;
import com.renpho.erp.tms.domain.exception.BusinessException;
import com.renpho.erp.tms.domain.firstleg.mode.FirstLegMode;
import com.renpho.erp.tms.infrastructure.util.excel.Excel18nTools;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 头程方式.
 *
 * <AUTHOR>
 * @since 2025/4/21
 */
@RestController
@RequestMapping("/firstleg/mode")
@ShenyuSpringCloudClient("/firstleg/mode/**")
@RequiredArgsConstructor
public class FirstLegModeController {

    private final FirstLegModeService firstLegModeService;
    private final FirstLegModeCmdConverter firstLegModeCmdConverter;
    private final FirstLegModeVOConverter firstLegModeVOConverter;
    private final TransService transService;

    /**
     * 新增
     *
     * @param cmd 参数
     * @return id
     */
    @PostMapping("/add")
    @PreAuthorize("hasPermission('tms:firstlegmode:new')")
    public R<Integer> add(@Valid @RequestBody FirstLegModeAddCmd cmd) {

        FirstLegMode domain = firstLegModeCmdConverter.toDomain(cmd);
        domain = firstLegModeService.add(domain);
        return R.success(domain.getId().id());
    }


    /**
     * 编辑
     *
     * @param cmd 参数
     * @return id
     */
    @PostMapping("/edit")
    @PreAuthorize("hasPermission('tms:firstlegmode:edit')")
    public R<Integer> edit(@RequestBody FirstLegModeEditCmd cmd) {

        FirstLegMode domain = firstLegModeCmdConverter.toDomain(cmd);
        domain = firstLegModeService.update(domain);
        Integer id = domain.getId().id();

        return R.success(id);
    }


    /**
     * 分页查询
     *
     * @param cmd 参数
     * @return 列表
     */
    @TransMethodResult
    @PostMapping("/pageList")
    @PreAuthorize("hasPermission('tms:firstlegmode:list')")
    public R<Paging<FirstLegModePageVO>> pageList(@RequestBody FirstLegModePageCmd cmd) {
        FirstLegMode domain = firstLegModeCmdConverter.toDomain(cmd);
        Paging<FirstLegMode> paging = firstLegModeService.findListByPage(domain, cmd);

        return R.success(firstLegModeVOConverter.toPageVOs(paging));
    }


    /**
     * 单个查询（无权限限制）
     * @param cmd 参数
     * @return 单个
     */
    @Inner
    @TransMethodResult
    @PostMapping("/findOne")
    public R<FirstLegModeListVO> findOne(@RequestBody FirstLegModeRemoteRequestCmd cmd) {
        FirstLegMode domain = firstLegModeCmdConverter.toDomain(cmd);
        List<FirstLegMode> list = firstLegModeService.findAll(domain);

        FirstLegModeListVO vo = null;
        Optional<FirstLegMode> one = list.stream().findFirst();
        if (one.isPresent()) {
            vo = firstLegModeVOConverter.toListVO(one.get());
        }
        return R.success(vo);

    }



    /**
     * 查询列表（无权限限制）
     * @param cmd 参数
     * @return 单个
     */
    @Inner
    @TransMethodResult
    @PostMapping("/findList")
    public R<List<FirstLegModeListVO>> findList(@RequestBody FirstLegModeRemoteRequestCmd cmd) {
        FirstLegMode domain = firstLegModeCmdConverter.toDomain(cmd);
        List<FirstLegMode> list = firstLegModeService.findAll(domain);

        return R.success(firstLegModeVOConverter.toListVOs(list));
    }

    /**
     * 导出
     *
     * @param cmd      参数
     * @param response 响应
     */
    @PostMapping("/export")
    @PreAuthorize("hasPermission('tms:firstlegmode:export')")
    public void export(@RequestBody FirstLegModeExportCmd cmd, HttpServletResponse response) {

        FirstLegMode domain = firstLegModeCmdConverter.toDomain(cmd);
        List<FirstLegMode> list = firstLegModeService.findAll(domain);
        List<FirstLegModeExcelExportVO> dataList = firstLegModeVOConverter.toExportVOs(list);

        String filename = new StrBuilder().append("头程方式").append("_")
                .append(DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN))
                .append(".xlsx").toStringAndReset();

        try {
            transService.transBatch(dataList);
            ByteArrayOutputStream ous = Excel18nTools.handleExcelFile(dataList, FirstLegModeExcelExportVO.class);
            Excel18nTools.writeExcelFile(filename, ous, response);

        } catch (IOException e) {
            throw new BusinessException("SYSTEM_EXCEPTION");
        }

    }

}
