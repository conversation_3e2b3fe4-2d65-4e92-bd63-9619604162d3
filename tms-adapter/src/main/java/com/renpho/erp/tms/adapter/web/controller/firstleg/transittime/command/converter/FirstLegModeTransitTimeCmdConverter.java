package com.renpho.erp.tms.adapter.web.controller.firstleg.transittime.command.converter;

import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.IdQuery;
import com.renpho.erp.tms.adapter.web.controller.firstleg.transittime.command.*;
import com.renpho.erp.tms.adapter.web.controller.firstleg.transittime.vo.FirstLegCountryRegionExcelUploadErrorVO;
import com.renpho.erp.tms.domain.firstleg.mode.FirstLegMode;
import com.renpho.erp.tms.domain.firstleg.transittime.FirstLegCountryRegion;
import com.renpho.erp.tms.domain.firstleg.transittime.FirstLegCountryRegionTransitTime;
import com.renpho.erp.tms.domain.firstleg.transittime.FirstLegCountryRegionTransitTimeId;
import com.renpho.erp.tms.domain.site.CountryRegion;
import com.renpho.erp.tms.infrastructure.common.converter.StatusConverter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/23
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE,
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {StatusConverter.class})
public interface FirstLegModeTransitTimeCmdConverter {

    @Mapping(target = "firstLegMode.id.id", source = "modeId")
    @Mapping(target = "countryRegion.code", source = "countryCode")
    FirstLegCountryRegion toDomain(FirstLegModeTransitTimeAddCmd cmd);

    @Mapping(target = "id.id", source = "id")
    @Mapping(target = "firstLegMode.id.id", source = "modeId")
    @Mapping(target = "countryRegion.code", source = "countryCode")
    FirstLegCountryRegion toDomain(FirstLegModeTransitTimeEditCmd cmd);

    @Mapping(target = "firstLegMode.id.id", source = "modeId")
    @Mapping(target = "countryRegion.code", source = "countryCode")
    FirstLegCountryRegion toDomain(FirstLegModeTransitTimePageCmd cmd);

    @Mapping(target = "id.id", source = "id")
    FirstLegCountryRegion toDomain(IdQuery cmd);

    @Mapping(target = "firstLegMode.id.id", source = "modeId")
    @Mapping(target = "countryRegion.code", source = "countryCode")
    FirstLegCountryRegion toDomain(FirstLegModeTransitTimeExportCmd cmd);


    @Mapping(target = "firstLegMode.id.id", source = "modeId")
    @Mapping(target = "countryRegion.code", source = "countryCode")
    @Mapping(target = "status", source = "statusName")
    FirstLegCountryRegion toDomain(FirstLegCountryRegionExcelUploadErrorVO uploadData);

    List<FirstLegCountryRegion> toDomains(Collection<FirstLegCountryRegionExcelUploadErrorVO> uploadDatas);

    default List<FirstLegCountryRegion> groupAndMerge(List<FirstLegCountryRegionExcelUploadErrorVO> originalList) {
        // 定义分组键
        record GroupKey(FirstLegMode firstLegMode, CountryRegion countryRegion) {
        }

        return toDomains(originalList).stream()
                .collect(Collectors.groupingBy(flcr -> new GroupKey(flcr.getFirstLegMode()
                        , flcr.getCountryRegion())))
                .values().stream()
                .map(this::mergeGroup)
                .collect(Collectors.toList());
    }

    private FirstLegCountryRegion mergeGroup(List<FirstLegCountryRegion> group) {
        if (group.isEmpty()) {
            throw new IllegalArgumentException("Group is empty");
        }

        FirstLegCountryRegion first = group.get(0);

        // 收集所有TransitTime对象
        List<FirstLegCountryRegionTransitTime> transitTimes = group.stream()
                .map(flcr -> {
                    // 假设存在方法生成新的TransitTimeId，这里使用示例构造
                    FirstLegCountryRegionTransitTimeId transitTimeId = new FirstLegCountryRegionTransitTimeId(null);
                    return new FirstLegCountryRegionTransitTime(
                            transitTimeId,
                            flcr.getArea(),
                            flcr.getMinTransitTime(),
                            flcr.getMaxTransitTime()
                    );
                })
                .collect(Collectors.toList());

        // 创建合并后的对象
        first.setArea(null); // 清空原字段
        first.setMinTransitTime(null);
        first.setMaxTransitTime(null);
        first.setTransitTimes(transitTimes);

        return first;
    }

    @Mapping(target = "id.id", source = "id")
    FirstLegCountryRegionTransitTime toCountryRegionTransitTimeDomain(FirstLegCountryRegionTransitTimeCmd transitTime);

    List<FirstLegCountryRegionTransitTime> toCountryRegionTransitTimeDomains(Collection<FirstLegCountryRegionTransitTimeCmd> transitTimes);

}
