package com.renpho.erp.tms.adapter.web.controller.transportrequest.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.github.houbb.sensitive.annotation.Sensitive;
import com.renpho.erp.data.sensitive.SensitiveDynamicCondition;
import com.renpho.erp.data.sensitive.SensitiveDynamicStrategy;
import com.renpho.erp.tms.adapter.web.controller.vo.serializer.BigDecimalScale3RoundingSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/11
 */
@Data
public class TransportRequestDetailTOVO implements VO {
    /**
     * TR单Id
     */
    private Integer id;
    /**
     * TR出运状态。字典: TO_STATUS。可选值:  1-询价中 2-订舱中 3-已订舱 4-货交承运人 5-已离港 6-已到港 7-已派送 8-已签收 9-已完成 10-已作废
     */
    @Trans(type = TransType.DICTIONARY, key = "TO_STATUS", ref = "shipStatusName")
    private Integer shipStatus;

    private String shipStatusName;

    /**
     * 运单号
     */
    private String trackingNo;

    /**
     * TR单号
     */
    private String trNo;

    /**
     * TO单号
     */
    private String toNo;
    /**
     * Pd单ID
     */
    private Integer pdId;

    /**
     * 交付单号（PD单号）
     */
    private String pdNo;

    /**
     * PO单号
     */
    private String poNo;
    /**
     * PO单ID
     */
    private Integer poId;

    /**
     * ReferenceId
     */
    private String referenceId;

    /**
     * 平台 ID
     */
    private Integer salesChannelId;


    /**
     * 平台名称
     */
    private String salesChannelName;


    /**
     * 货主 ID
     */
    private Integer ownerId;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 产品ID
     */
    private Integer productId;

    /**
     * SKU
     */
    private String psku;

    /**
     * 名称(英文)
     */
    private String nameEn;


    /**
     * fnSku
     */
    private String fnSku;

    /**
     * 敏感属性(选中的),多个用`,`号隔开，说明: 0 锂电池、1 干电池、2 纯电、3 液体、4 凝胶、5 粉末、6 营养颗粒、7 活性炭，egg: 0,1
     */
    private List<String> hazardousPros;

    /**
     * 品类名称
     */
    private String categoryName;

    /**
     * 发运与入库字典值, 字典: ship_warehouse_type
     */
    @Trans(type = TransType.DICTIONARY, key = "ship_warehouse_type", ref = "shipWarehouseMethodName")
    private String shipWarehouseMethod;

    /**
     * 发运与入库
     */
    private String shipWarehouseMethodName;

    /**
     * 服务商类型字典值, 字典: logistics_type
     */
    @Trans(type = TransType.DICTIONARY, key = "logistics_type", ref = "carrierTypeName")
    private String carrierType;

    /**
     * 服务商类型
     */
    private String carrierTypeName;

    /**
     * 工厂交期
     */
    private LocalDate latestDeliveryDate;

    /**
     * 交货时间
     */
    private LocalDate deliveryTime;

    /**
     * 实际离港时间
     */
    private LocalDate actualDepartureTime;

    /**
     * 实际到港时间
     */
    private LocalDate actualArrivalTime;

    /**
     * 实际派送时间
     */
    private LocalDate actualDeliveryTime;

    /**
     * 采购供应商 ID
     */
    private String purchaseSupplierId;

    /**
     * 采购供应商名称
     */
    @Sensitive(permission = "SUPPLIER:NAME", condition = SensitiveDynamicCondition.class,
            strategy = SensitiveDynamicStrategy.class)
    private String purchaseSupplierName;

    /**
     * 采购供应商编码
     */
    private String purchaseSupplierCode;

    /**
     * 采购供应商简称
     */
    @Sensitive(permission = "SUPPLIER:NAME", condition = SensitiveDynamicCondition.class,
            strategy = SensitiveDynamicStrategy.class)
    private String purchaseSupplierShortName;
    /**
     * ShipmentId
     */
    private String shipmentId;

    /**
     * 头程方式 ID
     */
    private String firstLegModeId;

    /**
     * 头程方式
     */
    private String firstLegModeName;

    /**
     * 总数量
     */
    private Integer totalQty;

    /**
     * 总箱数
     */
    private Integer totalBoxQty;

    /**
     * 总毛重 kg
     */
    private BigDecimal totalGrossWeight;

    /**
     * 总体积 m³
     */
    private BigDecimal totalVolume;

    /**
     * 预估运费
     */
    @JsonSerialize(using = BigDecimalScale3RoundingSerializer.class)
    private BigDecimal estimatedFreight;

    /**
     * 预估运费币种
     */
    private String estimatedFreightCurrencyCode;

    /**
     * 预估税费
     */
    @JsonSerialize(using = BigDecimalScale3RoundingSerializer.class)
    private BigDecimal estimatedTax;

    /**
     * 预估税费币种
     */
    private String estimatedTaxCurrencyCode;

    /**
     * 计划出货日期-开始
     */
    private LocalDate plannedShipStartDate;

    /**
     * 计划出货日期-结束
     */
    private LocalDate plannedShipEndDate;

    /**
     * 起运港字典值, 字典: trade_terms_ship_to
     */
    private String shippingPort;

    /**
     * 起运港
     */
    private String shippingPortName;

    /**
     * 贸易条款, 字典: trade_terms
     */
    private String tradeTerms;

    /**
     * 目的国/地区  国家(地区)
     */
    private String destCountry;

    /**
     * 目的国/地区  国家(地区)
     */
    private String destCountryCode;

    /**
     * 目的仓 ID
     */
    private String destWarehouseId;

    /**
     * 目的仓名称
     */
    private String destWarehouseName;

    /**
     * 目的仓编码
     */
    private String destWarehouseCode;

    /**
     * 目的地
     */
    private String destination;


}
