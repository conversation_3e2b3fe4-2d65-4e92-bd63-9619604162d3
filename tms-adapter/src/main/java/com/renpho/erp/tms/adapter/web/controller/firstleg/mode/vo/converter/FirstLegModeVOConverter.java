package com.renpho.erp.tms.adapter.web.controller.firstleg.mode.vo.converter;

import com.renpho.erp.tms.adapter.web.controller.firstleg.mode.vo.FirstLegModeExcelExportVO;
import com.renpho.erp.tms.adapter.web.controller.firstleg.mode.vo.FirstLegModeListVO;
import com.renpho.erp.tms.adapter.web.controller.firstleg.mode.vo.FirstLegModePageVO;
import com.renpho.erp.tms.domain.firstleg.mode.FirstLegMode;
import com.renpho.erp.tms.infrastructure.common.converter.StatusConverter;
import com.renpho.erp.tms.infrastructure.remote.user.converter.OperatorConverter;
import com.renpho.karma.dto.Paging;
import org.mapstruct.*;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/21
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE,
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {StatusConverter.class, OperatorConverter.class})
public interface FirstLegModeVOConverter {
    @Named("toPageVO")
    @Mapping(target = "id", source = "id.id")
    @Mapping(target = "createBy", source = "created.operatorId.id")
    @Mapping(target = "creator", source = "created", qualifiedByName = "concatOperatorInfo")
    @Mapping(target = "createTime", source = "created.operateTime")
    @Mapping(target = "updateBy", source = "updated.operatorId.id")
    @Mapping(target = "updater",source = "updated", qualifiedByName = "concatOperatorInfo")
    @Mapping(target = "updateTime", source = "updated.operateTime")
    FirstLegModePageVO toVO(FirstLegMode domain);


    @Named("toListVO")
    @Mapping(target = "id", source = "id.id")
    FirstLegModeListVO toListVO(FirstLegMode domain);

    @Mapping(target = "id", source = "id.id")
    @Mapping(target = "createBy", source = "created.operatorId.id")
    @Mapping(target = "creator", source = "created", qualifiedByName = "concatOperatorInfo")
    @Mapping(target = "createTime", source = "created.operateTime")
    @Mapping(target = "updateBy", source = "updated.operatorId.id")
    @Mapping(target = "updater",source = "updated", qualifiedByName = "concatOperatorInfo")
    @Mapping(target = "updateTime", source = "updated.operateTime")
    FirstLegModeExcelExportVO toExportVO(FirstLegMode domain);

    @IterableMapping(qualifiedByName = "toListVO")
    List<FirstLegModeListVO> toListVOs(Collection<FirstLegMode> list);

    List<FirstLegModeExcelExportVO> toExportVOs(Collection<FirstLegMode> list);

    @IterableMapping(qualifiedByName = "toPageVO")
    List<FirstLegModePageVO> toVOs(Collection<FirstLegMode> list);
    Paging<FirstLegModePageVO> toPageVOs(Paging<FirstLegMode> paging);

}
