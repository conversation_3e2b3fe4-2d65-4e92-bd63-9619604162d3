package com.renpho.erp.tms.adapter.web.controller.firstleg.transittime.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/23
 */
@Data
public class FirstLegCountryRegionDetailVO implements VO {
    private  Integer id;

    /**
     * 头程类型ID
     */
    private Integer modeId;

    /**
     * 头程方式
     */
    private String firstLegMode;

    /**
     * 国家(地区)编码
     */
    private String countryCode;

    /**
     * 国家(地区)
     */
    private String country;

    /**
     * 备注
     */
    private String remark;

    /**
     * 时效列表
     */
    private List<FirstLegCountryRegionTransitTimeVO> countryRegionVOS;

    @Trans(type = TransType.DICTIONARY, key = "status_dict", ref = "statusName")
    private Integer status;

    /**
     * 状态
     */
    private String statusName;

}
