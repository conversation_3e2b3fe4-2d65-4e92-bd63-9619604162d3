package com.renpho.erp.tms.adapter.web.controller.transportorder.cmd;

import com.renpho.erp.tms.adapter.web.controller.command.validator.PortExist;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/12
 */
@Getter
@Setter
public class TransportOrderHandoverCmd {

    /**
     * TO单ID
     */
    @NotNull
    private Integer toId;

    /**
     * 目的港，取港口配置的启用的港口EN名称
     */
    @NotBlank
    @PortExist
    private String destPort;

    /**
     * 中转港，取港口配置的启用的港口EN名称
     */
    private String transPort;

    /**
     * 预计离港时间
     */
    private LocalDate estimatedDepartureDate;

    /**
     * 预计到港时间
     */
    private LocalDate estimatedArrivalDate;

    /**
     * 是否保险，0=未投保，1=已投保
     */
    @NotNull
    private Boolean isInsured;

    /**
     * 柜型，字典类型：CONTAINER_TYPE。取值如下：20GP、40GP、40HQ、45HQ
     */
    private String containerModel;

    /**
     * 柜号
     */
    private String containerNo;

    /**
     * 是否打托，0=否，1=是
     */
    @NotNull
    private Boolean isPalletized;

    /**
     * 托盘数
     */
    private Integer palletQty;

    /**
     * 批注
     */
    @Size(max = 200, message = "error.to.comment-length-exceeded")
    private String comment;

    private List<TransportRequestDeliveryItem> trItems;


    @Getter
    @Setter
    public static class TransportRequestDeliveryItem {
        /**
         * TR单ID
         */
        private Integer trId;
        /**
         * 运单号
         */
        private String trackingNo;
        /**
         * 交货时间
         */
        private LocalDate deliveryTime;
    }
}
