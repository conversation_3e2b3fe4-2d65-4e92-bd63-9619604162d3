package com.renpho.erp.tms.adapter.web.controller.transportorder.cmd;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.renpho.erp.tms.adapter.web.controller.command.container.ProcessInstanceIdContainer;
import com.renpho.erp.tms.domain.processinstance.ProcessInstance;
import com.renpho.karma.dto.Query;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

/**
 * 货代审批 ID
 *
 * <AUTHOR>
 * @since 2025/6/13
 */
@Getter
@Setter
public class TransportOrderForwarderApprovalIdQuery extends Query implements ProcessInstanceIdContainer {

    @Serial
    private static final long serialVersionUID = 3005444301841077347L;

    /**
     * 审批 ID
     */
    @NotBlank
    private String processInstanceId;

    @JsonIgnore
    private ProcessInstance processInstance;

}
