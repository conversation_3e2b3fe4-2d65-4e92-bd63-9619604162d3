package com.renpho.erp.tms.adapter.web.job.TransportRequest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.renpho.erp.tms.application.transportrequest.PlatformWarehouseShipmentService;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestId;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * TR单-平台仓Job
 * <AUTHOR>
 * @since 2025/7/18
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TransportRequestOrderPlatformWarehouseJob {
    private final PlatformWarehouseShipmentService platformWarehouseShipmentService;

    /**
     * 拉取FBA仓货件信息
     */
    @XxlJob("pullShipmentsFromFBA")
    public void pullShipmentsFromFBA(){

        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jo = JSON.parseObject(jobParam);
        List<String> trNos = jo.getJSONArray("trNos").toJavaList(String.class);
        List<Integer> trIds = jo.getJSONArray("trIds").toJavaList(Integer.class);
        List<TransportRequestId> ids = CollectionUtils.emptyIfNull(trIds).stream().map(TransportRequestId::new).toList();

        platformWarehouseShipmentService.pullShipmentsFromFBA(trNos, ids);

    }


    /**
     * 拉取AWD仓货件信息
     */
    @XxlJob("pullShipmentsFromAWD")
    public void pullShipmentsFromAWD(){

        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jo = JSON.parseObject(jobParam);
        List<String> trNos = jo.getJSONArray("trNos").toJavaList(String.class);
        List<Integer> trIds = jo.getJSONArray("trIds").toJavaList(Integer.class);
        List<TransportRequestId> ids = CollectionUtils.emptyIfNull(trIds).stream().map(TransportRequestId::new).toList();

        platformWarehouseShipmentService.pullShipmentsFromAWD(trNos, ids);

    }


    /**
     * 拉取TikTok仓货件信息
     */
    @XxlJob("pullShipmentsFromFBT")
    public void pullShipmentsFromFBT(){

        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jo = JSON.parseObject(jobParam);
        List<String> trNos = jo.getJSONArray("trNos").toJavaList(String.class);
        List<Integer> trIds = jo.getJSONArray("trIds").toJavaList(Integer.class);
        List<TransportRequestId> ids = CollectionUtils.emptyIfNull(trIds).stream().map(TransportRequestId::new).toList();

        platformWarehouseShipmentService.pullShipmentsFromFBT(trNos, ids);

    }


    /**
     * 拉取Walmart仓货件信息
     */
    @XxlJob("pullShipmentsFromWalmart")
    public void pullShipmentsFromWalmart(){

        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jo = JSON.parseObject(jobParam);
        List<String> trNos = jo.getJSONArray("trNos").toJavaList(String.class);
        List<Integer> trIds = jo.getJSONArray("trIds").toJavaList(Integer.class);
        List<TransportRequestId> ids = CollectionUtils.emptyIfNull(trIds).stream().map(TransportRequestId::new).toList();

        platformWarehouseShipmentService.pullShipmentsFromWalmart(trNos, ids);

    }
}
