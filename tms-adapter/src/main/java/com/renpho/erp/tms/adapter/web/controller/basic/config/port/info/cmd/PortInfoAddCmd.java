package com.renpho.erp.tms.adapter.web.controller.basic.config.port.info.cmd;

import com.renpho.erp.tms.domain.common.LanguageEnum;
import com.renpho.erp.tms.domain.exception.BusinessException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 港口配置-新增参数
 *
 * <AUTHOR>
 * @since 2025/6/11
 */
@Data
public class PortInfoAddCmd {

    /**
     * 港口英文名,校验: 必填,全局唯一，只能填写字母
     */
//    @NotNull(message = "港口英文名不能为空")
//    @Pattern(regexp = "^[A-Za-z]+$", message = "港口英文名只能包含英文字母")
//    @Size(max = 30, message = "港口英文名不能超过30个字符")
    private String portNameEn;

    /**
     * 港口中文名,校验: 必填,全局唯一
     */
//    @NotNull(message = "港口中文名不能为空")
//    @Size(max = 30, message = "港口中文名不能超过30个字符")
    private String portNameCn;

    /**
     * 港口代码,校验: 必填,全局唯一
     */
//    @NotNull(message = "港口代码不能为空")
//    @Size(max = 30, message = "港口代码不能超过30个字符")
    private String portCode;

    /**
     * 区域，校验: 非必填
     */
//    @Size(max = 30, message = "区域不能超过30个字符")
    private String district;

    /**
     * 港口类型，字典类型：PORT_TYPE，海港：SEAPORT，内陆港：INLAND_PORT，空港：AIRPORT
     * 校验: 必填
     */
//    @NotNull(message = "港口类型不能为空")
    private String portType;

    /**
     * 状态，0=无效，1=有效
     * 校验: 必填
     */
//    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 校验方法，根据语言类型返回不同的错误信息
     * @param language 语言类型
     */
    public void validate(LanguageEnum language) {
        if (isBlank(portNameEn)) {
            //throwValidationError("港口英文名不能为空", "Port name (EN) is required", language);
            throw new BusinessException("PORT_INFO_NAME_EN_NULL_ERROR");
        } else{
            portNameEn = portNameEn.trim();
        }
        if (!portNameEn.matches("^[A-Za-z ]+$")) {
            //throwValidationError("港口英文名只能包含英文字母", "Port name (EN) must contain only letters", language);
            throw new BusinessException("PORT_INFO_NAME_EN_ERROR");
        }
        if (portNameEn.length() > 30) {
            //throwValidationError("港口英文名不能超过30个字符", "Port name (EN) must be <= 30 characters", language);
            throw new BusinessException("PORT_INFO_NAME_EN_LENGTH_ERROR");
        }

        if (isBlank(portNameCn)) {
            //throwValidationError("港口中文名不能为空", "Port name (CN) is required", language);
            throw new BusinessException("PORT_INFO_NAME_CN_NULL_ERROR");
        } else{
            portNameCn = portNameCn.trim();
        }
        if (portNameCn.length() > 30) {
            //throwValidationError("港口中文名不能超过30个字符", "Port name (CN) must be <= 30 characters", language);
            throw new BusinessException("PORT_INFO_NAME_CN_LENGTH_ERROR");
        }

        if (isBlank(portCode)) {
            //throwValidationError("港口代码不能为空", "Port code is required", language);
            throw new BusinessException("PORT_INFO_PORT_CODE_NULL_ERROR");
        } else{
            portCode = portCode.trim();
        }
        if (portCode.length() > 30) {
            //throwValidationError("港口代码不能超过30个字符", "Port code must be <= 30 characters", language);
            throw new BusinessException("PORT_INFO_PORT_CODE_LENGTH_ERROR");
        }

        if(StringUtils.isNotBlank(district)){
            district = district.trim();
        }
        if (district != null && district.length() > 30) {
            //throwValidationError("区域不能超过30个字符", "District must be <= 30 characters", language);
            throw new BusinessException("PORT_INFO_DISTRICT_LENGTH_ERROR");
        }

        if (isBlank(portType)) {
            //throwValidationError("港口类型不能为空", "Port type is required", language);
            throw new BusinessException("PORT_INFO_PORT_TYPE_NULL_ERROR");
        } else {
            portType = portType.trim();
        }

        if (status == null) {
            //throwValidationError("状态不能为空", "Status is required", language);
            throw new BusinessException("PORT_INFO_STATUS_NULL_ERROR");
        } else if (status != 0 && status != 1) {
            //throwValidationError("状态必须为0或1", "Status must be 0 or 1", language);
            throw new BusinessException("PORT_INFO_STATUS_ERROR");
        }
    }

//    private void throwValidationError(String cnMsg, String enMsg, LanguageEnum language) {
//        String message = (language == LanguageEnum.China) ? cnMsg : enMsg;
//        throw new IllegalArgumentException(message); // 或自定义异常
//    }

    private boolean isBlank(String s) {
        return s == null || s.trim().isEmpty();
    }

}
