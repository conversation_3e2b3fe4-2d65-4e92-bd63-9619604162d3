package com.renpho.erp.tms.adapter.web.controller.customsinfo.command;

import com.renpho.erp.tms.domain.customsinfo.PskuAdditionalDutyStatus;
import com.renpho.karma.dto.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Generated;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * 查询参数
 *
 * <AUTHOR>
 * @since 2025/4/22
 */
@Getter
@Setter
@Schema(name = "ListCustomsInfoQuery", description = "查询参数")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-04-22T12:09:54.179410400+08:00[Asia/Shanghai]", comments = "Generator version: 7.12.0")
public class ListQuery extends PageQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 3888176583176151272L;

    /**
     * 查询 PSKU
     */
    @Schema(name = "psku", description = "查询 PSKU", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String psku;

    /**
     * 批量查询 PSKU
     */
    @Schema(name = "pskus", description = "批量查询 PSKU", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Set<String> pskus;

    /**
     * 海关编码
     */
    @Schema(name = "hsCode", description = "海关编码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String hsCode;

    /**
     * 批量查询国家编码
     */
    @Schema(name = "countryCodes", description = "批量查询国家编码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Set<String> countryCodes;

    /**
     * 状态, 字典: psku_customs_info_status
     */
    @Schema(name = "status", description = "状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer status;

    /**
     * 附加关税生效状态, 字典: psku_customs_info_additional_duty_status
     */
    @Schema(name = "additionalDutyStatus", description = "附加关税生效状态, 字典: psku_customs_info_additional_duty_status", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private PskuAdditionalDutyStatus additionalDutyStatus;

    /**
     * 创建人 ID
     */
    @Schema(name = "createBy", description = "创建人 ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer createBy;

    /**
     * 批量查询创建人 ID
     */
    @Schema(name = "createBys", description = "批量查询创建人 ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Set<Integer> createBys;

    /**
     * 更新人 ID
     */
    @Schema(name = "updateBy", description = "更新人 ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer updateBy;

    /**
     * 批量查询更新人 ID
     */
    @Schema(name = "updateBys", description = "批量查询更新人 ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Set<Integer> updateBys;

    /**
     * 创建时间起始区间
     */
    @Schema(name = "createTimeStart", description = "创建时间起始区间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束区间
     */
    @Schema(name = "createTimeEnd", description = "创建时间结束区间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime createTimeEnd;

    /**
     * 更新时间起始区间
     */
    @Schema(name = "updateTimeStart", description = "更新时间起始区间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime updateTimeStart;

    /**
     * 更新时间结束区间
     */
    @Schema(name = "updateTimeEnd", description = "更新时间结束区间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime updateTimeEnd;

}

