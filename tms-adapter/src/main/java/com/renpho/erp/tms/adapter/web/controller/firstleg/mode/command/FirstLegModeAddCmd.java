package com.renpho.erp.tms.adapter.web.controller.firstleg.mode.command;

import com.renpho.karma.dto.Command;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/4/21
 */
@Getter
@Setter
public class FirstLegModeAddCmd extends Command implements Serializable {

    /**
     * 中文名称
     */
    @NotBlank(message = "validation.nameZh.required")
    @Size(max = 30, message = "validation.mode.maxLength")
    private String nameCn;

    /**
     * 英文名称
     */
    @NotBlank(message = "validation.nameEn.required")
    @Size(max = 30, message = "validation.mode.maxLength")
    private String nameEn;

    /**
     * 备注
     */
    @Size(max = 100, message = "validation.remark.maxLength")
    private String remark;

    /**
     * 状态 false-禁用 true-启用
     */
    @NotNull(message = "validation.status.required")
    private Integer status;
}
