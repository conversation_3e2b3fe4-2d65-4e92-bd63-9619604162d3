package com.renpho.erp.tms.adapter.web.controller.basic.config.port.info.cmd;

import com.renpho.erp.tms.domain.common.LanguageEnum;
import com.renpho.erp.tms.domain.exception.BusinessException;
import lombok.Data;

import java.util.List;

/**
 * 港口配置-新增参数-集合
 *
 * <AUTHOR>
 * @since 2025/6/11
 */
@Data
public class PortInfoAddListCmd {

    /**
     * 国家/地区
     */
//    @NotNull(message = "国家/地区编码不能为空")
    private String countryCode;

    /**
     * 港口配置-新增参数-集合
     */
//    @Valid
//    @NotNull(message = "港口配置-新增参数-集合不能为空")
//    @Size(min = 1, message = "港口配置-新增参数-至少有一行")
    private List<PortInfoAddCmd> portInfoAddList;

    /**
     * 校验方法，根据语言类型返回不同的错误信息
     * @param language 语言类型
     */
    public void validate(LanguageEnum language) {
        if (isBlank(countryCode)) {
            //throwValidationError("国家/地区编码不能为空", "Country code is required", language);
            throw new BusinessException("PORT_INFO_COUNTRY_CODE_NULL_ERROR");
        } else {
            countryCode = countryCode.trim();
        }

        if (portInfoAddList == null || portInfoAddList.isEmpty()) {
            //throwValidationError("港口配置不能为空", "Port configuration list must not be empty", language);
            throw new BusinessException("PORT_INFO_CONFIG_NULL_ERROR");
        }

        for (PortInfoAddCmd cmd : portInfoAddList) {
            cmd.validate(language);
        }
    }

//    private void throwValidationError(String cnMsg, String enMsg, LanguageEnum language) {
//        String message = (language == LanguageEnum.China) ? cnMsg : enMsg;
//        throw new IllegalArgumentException(message); // 可替换为业务异常
//    }

    private boolean isBlank(String s) {
        return s == null || s.trim().isEmpty();
    }

}
