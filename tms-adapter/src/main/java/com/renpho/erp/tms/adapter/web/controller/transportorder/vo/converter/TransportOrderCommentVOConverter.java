package com.renpho.erp.tms.adapter.web.controller.transportorder.vo.converter;

import com.renpho.erp.tms.adapter.web.controller.transportorder.vo.TransportOrderCommentVO;
import com.renpho.erp.tms.domain.transportorder.TransportOrderComment;
import com.renpho.erp.tms.infrastructure.remote.user.converter.OperatorConverter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @since 2025/6/11
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE,
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {OperatorConverter.class})
public interface TransportOrderCommentVOConverter {
    @Mapping(source = "id.id", target = "id")
    @Mapping(target = "creator", source = "created", qualifiedByName = "concatOperatorInfo")
    @Mapping(target = "createTime", source = "created.operateTime")
    TransportOrderCommentVO toVO(TransportOrderComment domain);

}
