package com.renpho.erp.tms.adapter.web.controller.orderfile.cmd.converter;

import cn.hutool.core.collection.CollUtil;
import com.renpho.erp.tms.adapter.web.controller.orderfile.cmd.OrderFileCmd;
import com.renpho.erp.tms.adapter.web.controller.orderfile.cmd.OrderFileInfoCmd;
import com.renpho.erp.tms.domain.file.FileId;
import com.renpho.erp.tms.domain.orderfile.BusinessTypeEnum;
import com.renpho.erp.tms.domain.orderfile.FileTypeEnum;
import com.renpho.erp.tms.domain.orderfile.OrderFile;
import com.renpho.erp.tms.domain.orderfile.OrderFileInfo;
import org.mapstruct.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/10
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE,
        componentModel = MappingConstants.ComponentModel.SPRING
)
public interface OrderFileCmdConverter {

    @Mapping(target = "businessType", source = "businessType", qualifiedByName = "toBusinessTypeEnum")
    @Mapping(target = "fileType", source = "fileType", qualifiedByName = "toFileTypeEnum")
    OrderFile toDomain(OrderFileCmd cmd);

    @Mapping(target = "fileId.id", source = "fileId")
    OrderFileInfo toInfoDomain(OrderFileInfoCmd cmd);

    List<OrderFileInfo> toInfoDomain(List<OrderFileInfoCmd> cmdList);

    @Named("toBusinessTypeEnum")
    default BusinessTypeEnum toBusinessTypeEnum(String type) {
        return BusinessTypeEnum.valueOf(type);
    }

    @Named("toFileTypeEnum")
    default FileTypeEnum toFileTypeEnum(String type) {
        return FileTypeEnum.valueOf(type);
    }

}
