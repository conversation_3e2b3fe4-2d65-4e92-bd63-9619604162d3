package com.renpho.erp.tms.adapter.web.controller.firstleg.transittime.command;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.renpho.erp.tms.adapter.web.controller.firstleg.transittime.vo.FirstLegCountryRegionExcelUploadErrorVO;
import com.renpho.erp.tms.adapter.web.controller.firstleg.transittime.vo.FirstLegCountryRegionExcelUploadVO;
import com.renpho.erp.tms.domain.firstleg.mode.FirstLegMode;
import com.renpho.erp.tms.domain.firstleg.mode.FirstLegModeId;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/25
 */
@RequiredArgsConstructor
public class FirstLegCountryRegionExcelUploadListener extends AnalysisEventListener<FirstLegCountryRegionExcelUploadVO> {

    private final Validator validator;
    private final Map<String, FirstLegMode> allModeMap;
    private boolean hasError = false;
    @Getter
    private final List<FirstLegCountryRegionExcelUploadErrorVO> excelDataWithErrorList = new ArrayList<>();


    @Override
    public void invoke(FirstLegCountryRegionExcelUploadVO data, AnalysisContext context) {
        Set<ConstraintViolation<FirstLegCountryRegionExcelUploadVO>> violations = validator.validate(data);
        FirstLegCountryRegionExcelUploadErrorVO excelDataWithError = new FirstLegCountryRegionExcelUploadErrorVO(data);
        if (CollectionUtils.isNotEmpty(violations)) {
            String error = violations.stream().map(ConstraintViolation::getMessage).collect(Collectors.joining(""));
            excelDataWithError.setError(error);
            hasError = true;
        }

        excelDataWithErrorList.add(excelDataWithError);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        Set<String> uniqueKey = new HashSet<>();

        for (FirstLegCountryRegionExcelUploadErrorVO vo : excelDataWithErrorList) {
            vo.setArea(StringUtils.defaultIfBlank(vo.getArea(), ""));
            if ("US".equalsIgnoreCase(vo.getCountryCode())) {
                if (StringUtils.isBlank(vo.getArea())) {
                    vo.setError(StringUtils.defaultIfBlank(vo.getError(), "") + "美国的必填；");
                    hasError = true;
                }
            } else {
                if (StringUtils.isBlank(vo.getArea())) {
                    vo.setArea(vo.getCountryCode());
                }
            }
            if (StringUtils.isNotBlank(vo.getMinTransitTime()) && !NumberUtil.isInteger(vo.getMinTransitTime())) {
                vo.setError(StringUtils.defaultIfBlank(vo.getError(), "") + "最小时效必须为正整数；");
                hasError = true;
            }
            if (StringUtils.isNotBlank(vo.getMaxTransitTime()) && !NumberUtil.isInteger(vo.getMaxTransitTime())) {
                vo.setError(StringUtils.defaultIfBlank(vo.getError(), "") + "最大时效必须为正整数；");
                hasError = true;
            }
            if (NumberUtil.isInteger(vo.getMinTransitTime()) && NumberUtil.isInteger(vo.getMaxTransitTime())
                    && Integer.parseInt(vo.getMinTransitTime()) > Integer.parseInt(vo.getMaxTransitTime())) {
                vo.setError(StringUtils.defaultIfBlank(vo.getError(), "") + "最小时效不能大于最大时效；");
                hasError = true;
            }

            Optional<Integer> modeIdOpt = Optional.ofNullable(allModeMap.get(vo.getFirstLegMode())).map(FirstLegMode::getId).map(FirstLegModeId::id);
            if (allModeMap.containsKey(vo.getFirstLegMode()) && modeIdOpt.isPresent()) {
                vo.setModeId(modeIdOpt.get());
            }

            //校验同一个头程方式+国家下自定义名称需唯一
            String key = String.join(",", vo.getFirstLegMode(), vo.getCountryCode(), vo.getArea());
            if (uniqueKey.contains(key)) {
                vo.setError(StringUtils.defaultIfBlank(vo.getError(), "") + "同一个头程方式+国家下自定义名称需唯一；");
                hasError = true;
            }
            uniqueKey.add(key);
        }

    }

    public boolean hasError() {
        return hasError;
    }
}
