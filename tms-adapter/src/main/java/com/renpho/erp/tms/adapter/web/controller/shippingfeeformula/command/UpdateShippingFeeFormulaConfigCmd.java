package com.renpho.erp.tms.adapter.web.controller.shippingfeeformula.command;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.renpho.karma.dto.Command;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/6/12
 */
@Getter
@Setter
public class UpdateShippingFeeFormulaConfigCmd extends Command implements Serializable {

    @Serial
    private static final long serialVersionUID = 7447721880367686494L;

    /**
     * 散货
     */
    @NotNull
    @JsonProperty(value = "LCL")
    @Valid
    private UpdateShippingFeeFormulaConfigItemCmd LCL;

    /**
     * 整柜
     */
    @NotNull
    @JsonProperty(value = "FCL")
    @Valid
    private UpdateShippingFeeFormulaConfigItemCmd FCL;
}
