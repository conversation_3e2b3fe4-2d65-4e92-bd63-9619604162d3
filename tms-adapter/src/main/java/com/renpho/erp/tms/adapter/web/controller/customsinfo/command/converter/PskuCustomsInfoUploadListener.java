package com.renpho.erp.tms.adapter.web.controller.customsinfo.command.converter;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.PskuCustomsInfoUploadExcel;
import com.renpho.erp.tms.adapter.web.controller.customsinfo.command.PskuCustomsInfoUploadExcelWithError;
import com.renpho.erp.tms.domain.currency.Currency;
import com.renpho.erp.tms.domain.customsinfo.PskuCustomsInfo;
import com.renpho.erp.tms.domain.customsinfo.PskuCustomsInfoLookup;
import com.renpho.erp.tms.domain.product.Product;
import com.renpho.erp.tms.domain.site.CountryRegion;
import com.renpho.erp.tms.infrastructure.remote.countryregion.repository.CountryRegionLookup;
import com.renpho.erp.tms.infrastructure.remote.currency.repository.CurrencyLookup;
import com.renpho.erp.tms.infrastructure.remote.product.repository.ProductLookup;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/25
 */
@RequiredArgsConstructor
public class PskuCustomsInfoUploadListener extends AnalysisEventListener<PskuCustomsInfoUploadExcel> {

    private final Validator validator;
    private final ProductLookup productLookup;
    private final CountryRegionLookup countryRegionLookup;
    private final CurrencyLookup currencyLookup;
    private final PskuCustomsInfoLookup pskuCustomsInfoLookup;
    private final PskuCustomsInfoCommandConverter converter;

    @Getter
    private final Map<Tuple2<String, String>, PskuCustomsInfo> uploads = new LinkedHashMap<>();

    @Getter
    private final List<PskuCustomsInfoUploadExcelWithError> lines = new ArrayList<>();

    @Getter
    private boolean error = false;

    @Override
    public void invoke(PskuCustomsInfoUploadExcel data, AnalysisContext context) {
        PskuCustomsInfoUploadExcelWithError uploaded = converter.toError(data);
        this.lines.add(uploaded);
        // 判断是否有字段格式错误
        validateFields(uploaded);
        // 判断是否有重复
        validateDuplicate(uploaded);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        Map<String, List<PskuCustomsInfoUploadExcelWithError>> pskus = new HashMap<>();
        Map<String, List<PskuCustomsInfoUploadExcelWithError>> countryCodes = new HashMap<>();
        Map<String, List<PskuCustomsInfoUploadExcelWithError>> currencyCodes = new HashMap<>();
        for (PskuCustomsInfoUploadExcelWithError upload : this.lines) {
            pskus.computeIfAbsent(upload.getPsku(), k -> new ArrayList<>()).add(upload);
            countryCodes.computeIfAbsent(upload.getCountryCode(), k -> new ArrayList<>()).add(upload);
            currencyCodes.computeIfAbsent(upload.getCurrencyCode(), k -> new ArrayList<>()).add(upload);
        }
        validatePsku(pskus);
        validateCountryCode(countryCodes);
        validateCurrencyCode(currencyCodes);
    }

    private void validateFields(PskuCustomsInfoUploadExcelWithError uploaded) {
        Set<ConstraintViolation<PskuCustomsInfoUploadExcel>> violations = validator.validate(uploaded);
        if (CollectionUtils.isNotEmpty(violations)) {
            String error = violations.stream().map(ConstraintViolation::getMessage).collect(Collectors.joining(""));
            invalidate(uploaded, error);
        }
        if (StringUtils.isNotBlank(uploaded.getAdditionalDutyEffectiveTime())) {
            try {
                LocalDateTime.parse(uploaded.getAdditionalDutyEffectiveTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } catch (Exception e) {
                invalidate(uploaded, "附加关税生效时间（国家时区）必须是一个正确的时间; ");
            }
        }
    }

    /**
     * 判断上传清关信息数据行是否有重复
     *
     * <AUTHOR>
     * @since 2025/4/27
     */
    private void validateDuplicate(PskuCustomsInfoUploadExcelWithError uploaded) {
        if (isPskuOrCountryCodeBlank(uploaded)) {
            return;
        }
        String t = uploaded.getAdditionalDutyEffectiveTime();
        if (StringUtils.isNotBlank(t)) {
            uploaded.setAdditionalDutyEffectiveTime(t.trim().replaceAll(" ", "T").replaceAll("/", "-"));
        }
        PskuCustomsInfo command = converter.toCommand(uploaded);
        uploaded.setAdditionalDutyEffectiveTime(t);
        Tuple2<String, String> key = key(uploaded);
        // 判断上传文件中是否已有相同清关信息的数据
        if (uploads.containsKey(key)) {
            // 上传文件中存在相同清关信息, 尝试合并相同数据
            PskuCustomsInfo exist = uploads.get(key);
            // 清关信息不完全相同
            boolean isCompletelySame = exist.isCompletelySame(command);
            if (!isCompletelySame) {
                invalidate(uploaded, "上传文件中已包含相同PSKU: 【" + key.getT1() + "】和国家地区: 【" + key.getT2() + "】但其他信息冲突的清关信息");
            }
            boolean containsSameAdditionalDuties = exist.containsSameAdditionalDuties(command.getAdditionalDuties());
            if (containsSameAdditionalDuties) {
                invalidate(uploaded, "上传文件中已包含相同PSKU: 【" + key.getT1() + "】和国家地区: 【" + key.getT2() + "】的附加关税");
            }
            if (isCompletelySame && !containsSameAdditionalDuties) {
                exist.merge(command);
            }
        } else {
            // 上传文件中不存在相同清关信息, 新增一条清关信息数据
            uploads.put(key, command);
        }
        List<PskuCustomsInfo> exists = pskuCustomsInfoLookup.findAll(converter.toQuery(uploaded));
        // 判断数据库是否已有相同 (PSKU + 国家) 的数据
        if (CollectionUtils.isNotEmpty(exists)) {
            if (exists.stream().anyMatch(d -> d.isSame(command))) {
                // 数据库中存在相同数据，记录错误
                invalidate(uploaded, "系统中已存在相同清关信息或附加关税");
            }
        }
    }

    private void validatePsku(Map<String, List<PskuCustomsInfoUploadExcelWithError>> pskus) {
        Map<String, Product> products = productLookup.findByPskus(pskus.keySet());
        for (String psku : pskus.keySet()) {
            if (StringUtils.isBlank(psku)) {
                continue;
            }

            // psku 存在, 且产品审核状态为已通过
            if (products.containsKey(psku) && Objects.equals(products.get(psku).getReviewStatus(), 2)) {
                Product product = products.get(psku);
                for (PskuCustomsInfoUploadExcelWithError uploaded : pskus.get(psku)) {
                    if (!isPskuOrCountryCodeBlank(uploaded)) {
                        Tuple2<String, String> key = key(uploaded);
                        this.uploads.get(key).setProduct(product);
                    }
                }
            } else {
                for (PskuCustomsInfoUploadExcelWithError uploaded : pskus.get(psku)) {
                    invalidate(uploaded, "PSKU " + uploaded.getPsku() + " 不存在");
                }
            }
        }
    }

    private void validateCountryCode(Map<String, List<PskuCustomsInfoUploadExcelWithError>> countryCodes) {
        Map<String, CountryRegion> countryRegions = countryRegionLookup.findByCodes(countryCodes.keySet());
        for (String countryCode : countryCodes.keySet()) {
            if (StringUtils.isBlank(countryCode)) {
                continue;
            }

            // 国家编码存在且状态为启用
            if (countryRegions.containsKey(countryCode) && Objects.equals(countryRegions.get(countryCode).getStatus(), 1)) {
                CountryRegion countryRegion = countryRegions.get(countryCode);
                if (countryRegion.getTimezone() == null) {
                    for (PskuCustomsInfoUploadExcelWithError uploaded : countryCodes.get(countryCode)) {
                        invalidate(uploaded, "国家 " + countryCode + " 未维护对应时区");
                    }
                } else {
                    for (PskuCustomsInfoUploadExcelWithError uploaded : countryCodes.get(countryCode)) {
                        if (!isPskuOrCountryCodeBlank(uploaded)) {
                            Tuple2<String, String> key = key(uploaded);
                            this.uploads.get(key).setCountryRegion(countryRegion);
                        }
                    }
                }
            } else {
                for (PskuCustomsInfoUploadExcelWithError uploaded : countryCodes.get(countryCode)) {
                    invalidate(uploaded, "国家编码 " + uploaded.getCountryCode() + " 不存在");
                }
            }
        }
    }

    private void validateCurrencyCode(Map<String, List<PskuCustomsInfoUploadExcelWithError>> currencyCodes) {
        Map<String, Currency> currencies = currencyLookup.findByCodes(currencyCodes.keySet());
        for (String currencyCode : currencyCodes.keySet()) {
            if (StringUtils.isBlank(currencyCode)) {
                continue;
            }

            // 货币存在, 且状态为已启用
            if (currencies.containsKey(currencyCode) && Objects.equals(currencies.get(currencyCode).getStatus(), 1)) {
                Currency currency = currencies.get(currencyCode);
                for (PskuCustomsInfoUploadExcelWithError uploaded : currencyCodes.get(currencyCode)) {
                    if (!isPskuOrCountryCodeBlank(uploaded)) {
                        Tuple2<String, String> key = key(uploaded);
                        this.uploads.get(key).setCurrency(currency);
                    }
                }
            } else {
                for (PskuCustomsInfoUploadExcelWithError uploaded : currencyCodes.get(currencyCode)) {
                    invalidate(uploaded, "币种 " + uploaded.getCurrencyCode() + " 不存在");
                }
            }
        }
    }

    private void invalidate(PskuCustomsInfoUploadExcelWithError uploaded, String error) {
        uploaded.addError(error);
        setContainsError();
    }

    private Tuple2<String, String> key(PskuCustomsInfoUploadExcelWithError uploaded) {
        return Tuples.of(uploaded.getPsku(), uploaded.getCountryCode());
    }


    private static boolean isPskuOrCountryCodeBlank(PskuCustomsInfoUploadExcelWithError uploaded) {
        return StringUtils.isAnyBlank(uploaded.getPsku(), uploaded.getCountryCode());
    }

    private void setContainsError() {
        this.error = true;
    }

}
