package com.renpho.erp.tms.adapter.web.controller.command.validator;

import com.renpho.erp.tms.domain.portinfo.PortInfoId;
import com.renpho.erp.tms.domain.portinfo.PortInfoLookup;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.Optional;
import java.util.function.Predicate;


/**
 * <AUTHOR>
 * @since 2025/4/23
 */
public abstract class PortValidator<T> implements ConstraintValidator<PortExist, T> {
    @Resource
    protected PortInfoLookup portInfoLookup;
    protected Predicate<T> predicate;

    @Override
    public boolean isValid(T value, ConstraintValidatorContext context) {
        return portInfoLookup == null || predicate.test(value);

//        return firstLegModeLookup.findById(new FirstLegModeId(id)).isPresent();
    }

    public static class PortIdExistValidator extends PortValidator<Integer> {
        @Override
        public void initialize(PortExist constraintAnnotation) {
            predicate = id -> Optional.ofNullable(id)
                    .map(PortInfoId::new)
                    .flatMap(portInfoLookup::findById)
                    .isPresent();
        }
    }

    public static class PortNameExistValidator extends PortValidator<String> {
        @Override
        public void initialize(PortExist constraintAnnotation) {
            predicate = modeName -> Optional.ofNullable(modeName)
                    .flatMap(portInfoLookup::findByName)
                    .isPresent();
        }
    }
}
