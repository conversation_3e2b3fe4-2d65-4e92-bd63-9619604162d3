package com.renpho.erp.tms.adapter.web.job.TransportRequest;

import com.renpho.erp.tms.application.transportrequest.job.leyu.TransportRequestLeYuAddService;
import com.renpho.erp.tms.application.transportrequest.job.leyu.TransportRequestLeYuCancelService;
import com.renpho.erp.tms.application.transportrequest.job.leyu.TransportRequestLeYuDeliveryService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * TR-目的仓为LeYu的定时器.
 * <AUTHOR>
 * @since 2025.07.25
 */
@SuppressWarnings("unused")
@Slf4j
@Component
@RequiredArgsConstructor
public class TransportRequestLeYuJob {

    private final TransportRequestLeYuAddService transportOrderLeYuService;
    private final TransportRequestLeYuCancelService transportOrderLeYuCancelService;
    private final TransportRequestLeYuDeliveryService transportOrderLeYuDeliveryService;

    /**
     * TR-目的仓为LeYu的推送任务生成-Add.
     * <br/>执行频率：
     */
    @XxlJob("createInboundLeYuAdd")
    public void createInboundLeYuAdd() throws Exception {
        transportOrderLeYuService.createInboundLeYu();
    }

    /**
     * 执行: TR-目的仓为LeYu的入库单任务-Add.
     * <br/>执行频率：
     * @param trNoList tr单号集合，用于异常重试的参数触发机制
     */
    @XxlJob("doingInboundLeYuAdd")
    public void doingInboundLeYuAdd(List<String> trNoList) throws Exception {
        transportOrderLeYuService.doingInboundLeYu(trNoList);
    }

    /**
     * 执行: TR-目的仓为LeYu的箱唛任务-Add.
     * <br/>执行频率：
     * @param trNoList tr单号集合，用于异常重试的参数触发机制
     */
    @XxlJob("doingPalletLeYuAdd")
    public void doingPalletLeYuAdd(List<String> trNoList) throws Exception {
        transportOrderLeYuService.doingPalletLeYu(trNoList);
    }

    /**
     * TR-目的仓为LeYu的推送任务生成-Cancel.
     * <br/>执行频率：
     */
    @XxlJob("createInboundLeYuCancel")
    public void createInboundLeYuCancelTask() throws Exception {
        transportOrderLeYuCancelService.createInboundLeYuCancelTask();
    }

    /**
     * 执行: TR-目的仓为LeYu的入库单任务-Cancel.
     * <br/>执行频率：
     * @param trNoList tr单号集合，用于异常重试的参数触发机制
     */
    @XxlJob("doingInboundLeYuCancel")
    public void doingInboundLeYuCancel(List<String> trNoList) throws Exception {
        transportOrderLeYuCancelService.doingInboundLeYu(trNoList);
    }

    /**
     * TR-目的仓为LeYu的推送任务生成-Delivery.
     * <br/>执行频率：
     */
    @XxlJob("createInboundLeYuDelivery")
    public void createInboundLeYuTaskDelivery() throws Exception {
        transportOrderLeYuDeliveryService.createInboundLeYu();
    }

    /**
     * 执行: TR-目的仓为LeYu的入库单任务-Delivery.
     * <br/>执行频率：
     * @param trNoList tr单号集合，用于异常重试的参数触发机制
     */
    @XxlJob("doingInboundLeYuDelivery")
    public void doingInboundLeYuDelivery(List<String> trNoList) throws Exception {
        transportOrderLeYuDeliveryService.doingInboundLeYu(trNoList);
    }

}
