<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.renpho.erp</groupId>
        <artifactId>tms-parent</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>tms-adapter</artifactId>
    <name>${project.artifactId}</name>
    <description>接口层、模块</description>

    <dependencies>
        <!-- ==================================== -->
        <!-- 内部依赖 -->
        <!-- ==================================== -->
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>tms-application</artifactId>
        </dependency>
        <dependency>
            <groupId>com.renpho.erp</groupId>
            <artifactId>tms-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.renpho.erp</groupId>
            <artifactId>pms-model</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.renpho.erp</groupId>
            <artifactId>erp-stream</artifactId>
        </dependency>
        <dependency>
            <groupId>com.renpho.erp</groupId>
            <artifactId>qms-model</artifactId>
            <version>${revision}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
