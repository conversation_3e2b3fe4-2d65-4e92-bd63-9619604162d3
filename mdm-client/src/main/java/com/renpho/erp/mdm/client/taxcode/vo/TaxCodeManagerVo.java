package com.renpho.erp.mdm.client.taxcode.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @author: Wyatt
 * @description: 税码实体
 * @date: 2024/11/21 17:42
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TaxCodeManagerVo implements VO, Serializable {



    @Schema(name = "id", description = "税码唯一id")
    private Integer id;

    @Schema(name = "codeName", description = "税码,模糊搜索")
    private String codeName;

    @Schema(name = "typeId", description = "税种id")
    private Integer typeId;

    @Schema(name = "typeName", description = "税种")
    private String typeName;

    @Schema(name = "accountId", description = "税务科目")
    private Long accountId;

    @Schema(name = "accountName", description = "税务科目")
    private String accountName;

    @Schema(name = "rateMoney", description = "税率 0.00")
    private BigDecimal rateMoney;

    @Schema(name = "category", description = "税务类别字典 名称")
	@Trans(type = TransType.DICTIONARY, key = "category_type", ref = "categoryName")
    private String categoryType;
    private String categoryName;

    @Schema(name = "country", description = "国家字典 名称")
	@Trans(type = TransType.DICTIONARY, key = "registered_region", ref = "countryName")
    private String country;
    private String countryName;

    /**
     * 取整策略
     */
    @Schema(name = "roundingType", description="取整策略")
	@Trans(type = TransType.DICTIONARY, key = "rounding_type", ref = "roundingName")
    private String roundingType;
    private String roundingName;

    /**
     * 取整计算方式
     */
    @Schema(name = "roundingMethod", description="取整计算方式")
	@Trans(type = TransType.DICTIONARY, key = "rounding_method_type", ref = "roundingMethodName")
    private String roundingMethod;
    private String roundingMethodName;


    @Schema(name = "effectiveTime", description = "生效时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime effectiveTime;

    @Schema(name = "status", description = "状态")
    private String status;

    @Schema(name = "note", description = "备注")
    private String note;

}
