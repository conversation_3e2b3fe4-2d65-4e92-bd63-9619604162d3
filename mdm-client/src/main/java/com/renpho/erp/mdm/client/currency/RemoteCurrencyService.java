package com.renpho.erp.mdm.client.currency;

import com.renpho.erp.mdm.client.currency.command.CurrencyQuery;
import com.renpho.erp.mdm.client.currency.vo.CurrencyVo;
import com.renpho.erp.openfeign.annotation.NoToken;
import com.renpho.karma.dto.R;
import com.renpho.karma.openfeign.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 店铺管理 feign client
 *
 * <AUTHOR>
 * @since 2024/9/20
 */
@FeignClient(name = "erp-mdm", configuration = FeignConfiguration.class)
public interface RemoteCurrencyService {

	@NoToken
	@PostMapping("/currency/getList")
	R<List<CurrencyVo>> getList(@RequestBody CurrencyQuery request);

}
