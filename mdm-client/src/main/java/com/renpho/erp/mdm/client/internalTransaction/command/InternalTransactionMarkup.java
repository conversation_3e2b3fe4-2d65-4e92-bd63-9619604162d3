package com.renpho.erp.mdm.client.internalTransaction.command;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class InternalTransactionMarkup {

	/**
	 * 卖方公司主键ID
	 */
	@NotNull(message = "卖方公司主键ID必填")
	@Schema(name = "sellerCompanyId", description = "卖方公司主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
	private Integer sellerCompanyId;

	/**
	 * 买方公司主键ID
	 */
	@NotNull(message = "买方公司主键ID必填")
	@Schema(name = "buyerCompanyId", description = "买方公司主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
	private Integer buyerCompanyId;

	/**
	 * 业务发生时间
	 */
	@NotNull(message = "业务发生时间必填，格式：yyyy-MM-dd HH:mm:ss")
	@Schema(name = "businessTime", description = "业务发生时间，格式：yyyy-MM-dd HH:mm:ss",
			requiredMode = Schema.RequiredMode.REQUIRED)
	private LocalDateTime businessTime;

}
