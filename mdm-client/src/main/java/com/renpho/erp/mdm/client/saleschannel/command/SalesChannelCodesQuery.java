package com.renpho.erp.mdm.client.saleschannel.command;

import com.renpho.karma.dto.Query;
import jakarta.annotation.Generated;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/18
 */
@Setter
@Getter
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen",
        date = "2024-09-13T18:23:33.399975700+08:00[Asia/Shanghai]", comments = "Generator version: 7.8.0")
public class SalesChannelCodesQuery extends Query {
    @NotEmpty
    private List<@NotNull String> codes;

}
