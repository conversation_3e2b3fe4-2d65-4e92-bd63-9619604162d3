package com.renpho.erp.mdm.client.company;

import java.util.List;

import com.renpho.erp.mdm.client.company.command.CompanyIdsQuery;
import com.renpho.erp.mdm.client.company.command.CompanyNamesQuery;
import com.renpho.erp.mdm.client.company.command.CompanyQuery;
import com.renpho.erp.mdm.client.company.command.CompanyShortNamesQuery;
import com.renpho.erp.mdm.client.company.vo.CompanyVo;
import com.renpho.erp.openfeign.annotation.NoToken;
import com.renpho.karma.dto.R;
import com.renpho.karma.openfeign.FeignConfiguration;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * MDM 公司 Feign Client.
 *
 * <AUTHOR>
 * @since 2024/9/26
 */
@FeignClient(name = "erp-mdm", configuration = FeignConfiguration.class)
public interface RemoteCompanyService {

	@NoToken
	@PostMapping("/company/findByIds")
	R<List<CompanyVo>> findByIds(@RequestBody CompanyIdsQuery request);

	@NoToken
	@PostMapping("/company/findAll")
	R<List<CompanyVo>> findAll(@RequestBody CompanyIdsQuery request);


	@NoToken
	@PostMapping("/company/findByNames")
	R<List<CompanyVo>> findByNames(@RequestBody CompanyNamesQuery request);

	@NoToken
	@PostMapping("/company/findByShortNames")
	R<List<CompanyVo>> findByShortNames(@RequestBody CompanyShortNamesQuery request);

	@NoToken
	@PostMapping("/company/query")
	R<List<CompanyVo>> query(@RequestBody CompanyQuery request);
}
