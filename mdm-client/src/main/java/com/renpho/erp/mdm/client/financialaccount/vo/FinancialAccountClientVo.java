package com.renpho.erp.mdm.client.financialaccount.vo;

import com.renpho.erp.mdm.client.vo.BaseVo;
import com.renpho.erp.mdm.client.vo.LanguageNameVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 会计科目
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FinancialAccountClientVo extends BaseVo {

    private Integer id;

    /**
     * NS ID
     */
    private Long nsId;

    /**
     * NS推送状态
     */
    private Integer nsSyncStatus;

    /**
     * 财务系统类型
     */
    private String nsType;

    /**
     * 科目编码
     */
    private String accountCode;

    /**
     * 科目名称（多语言）, 格式如下：[{"name": "External Payables", "language": "en-US"}, {"name": "外部应付", "language": "zh-CN"}]
     */
    private List<LanguageNameVo> names;

    /**
     * 科目名称
     */
    private String accountName;

    /**
     * 父级科目的主键id
     */
    private Integer parentId;

    private String parentName;

    private String parentCode;

    /**
     * 子科目数组
     */
    private List<FinancialAccountClientVo> children;

    /**
     * 子科目数量
     */
    private Long childCount;

    /**
     * 科目所属公司主键id
     */
    private Integer companyId;

    /**
     * 科目所属公司名
     */
    private String companyName;

    /**
     * 是否货币重估，0-否 1-是
     */
    private String isCurrencyRevaluation;

    /**
     * 是否为汇总科目，0-否 1-是
     */
    private String isSummaryAccount;

    /**
     * 币种id
     */
    private Integer currencyId;

    /**
     * 币种
     */
    private String currencyCode;

    /**
     * 是否可变更（科目已产生数据时为 0，否则为 1）
     */
    private String isMutable;

    /**
     * 备注
     */
	private String remark;

}
