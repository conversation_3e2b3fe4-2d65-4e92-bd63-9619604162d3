package com.renpho.erp.mdm.client.saleschannel.vo;

import com.renpho.erp.mdm.client.store.vo.StoreVo;
import com.renpho.erp.mdm.client.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Generated;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * SalesChannelVo.
 *
 * <AUTHOR>
 * @since 2024/9/13
 */
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen",
		date = "2024-09-13T18:23:33.399975700+08:00[Asia/Shanghai]", comments = "Generator version: 7.8.0")
@Setter
@Getter
public class SalesChannelVo extends BaseVo {

	@Schema(name = "id")
	private Integer id;

	@Schema(name = "渠道名称")
	private String channelName;

	/**
	 * 渠道代码
	 */
	@Schema(name = "channelCode", description = "渠道代码")
	private String channelCode;

	/**
	 * 可用站点
	 */
	@Schema(name = "availableSites", description = "可用站点id")
	private List<Integer> availableSites;

	/**
	 * 可用站点
	 */
	@Schema(name = "availableSiteCodes", description = "可用站点编码")
	private List<SalesChannelSiteVo> availableSiteCodes;

	@Schema(name = "availableStoreCount", description = "可用店铺数")
	private Integer availableStoreCount;

	@Schema(name = "allStoreCount", description = "总店铺数")
	private Integer allStoreCount;

	@Schema(name = "allStoreCount", description = "总店铺数")
	private List<StoreVo> stores;

	/**
	 * 渠道 logo link
	 */
	@Schema(name = "logo", description = "渠道 logo link")
	private String logo;

}
