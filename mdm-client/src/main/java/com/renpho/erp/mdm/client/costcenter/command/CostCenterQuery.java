package com.renpho.erp.mdm.client.costcenter.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.renpho.erp.mdm.client.costcenter.vo.CostCenterLanguageVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class CostCenterQuery {

	@Schema(description = "成本中心id")
	private Integer id;

	/**
	 * 父主键
	 */
	@Schema(description = "成本中心父id")
	private Integer parentId;

	/**
	 * 编码
	 */
	@Schema(description = "成本中心编码")
	private String code;

	/**
	 * 编码全路径
	 */
	@Schema(description = "编码全路径")
	private String codeRoute;

	@Schema(description = "名称全路径")
	private String nameRoute;

	/**
	 * 负责人ID，来源OUM的用户ID
	 */
	@Schema(description = "负责人ID")
	private Integer ownerId;

	@Schema(description = "负责人名称")
	private String ownerName;

	@Schema(description = "操作人名称")
	private String updateName;

	@Schema(description = "创建人")
	private Integer createBy;

	@Schema(description = "创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime createTime;

	@Schema(description = "修改人")
	private Integer updateBy;

	@Schema(description = "修改时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime updateTime;

	@Schema(description = "删除状态")
	private Integer deleted;

	@Schema(description = "成本中心名称")
	private String name;

	@Schema(description = "状态 0未激活 1激活")
	private Integer status;

	private List<CostCenterLanguageVo> languages;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime startTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime endTime;

	private Integer pageIndex = 1;

	private Integer pageSize = 20;

}
