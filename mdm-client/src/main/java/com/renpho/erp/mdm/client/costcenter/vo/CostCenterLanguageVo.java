package com.renpho.erp.mdm.client.costcenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;

/**
 * MdmCurrencyLanguage实体类
 */
@Getter
@Setter
@NoArgsConstructor
@Valid
@Validated
public class CostCenterLanguageVo {

	/**
	 * 主键
	 */
	@Schema(description = "语言id")
	private Integer id;

	/**
	 */
	@Schema(description = "成本中心id")
	private Integer costCenterId;

	/**
	 * 语言
	 */
	@Schema(description = "语言标识")
	@NotBlank(message = "语言不能为空")
	private String language;

	/**
	 * 名称
	 */
	@Schema(description = "名称")
	@Size(max = 50, message = "名称,长度限制50个字符以内")
	@NotBlank(message = "名称不能为空")
	private String name;

	/**
	 * 创建人ID
	 */
	@Schema(description = "创建人ID")
	private Integer createBy;

	/**
	 * 创建时间
	 */
	@Schema(description = "创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime createTime;

	/**
	 * 更新人ID
	 */
	@Schema(description = "更新人ID")
	private Integer updateBy;

	/**
	 * 更新时间
	 */
	@Schema(description = "更新时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime updateTime;

	/**
	 * 是否删除：0 否、1 是；默认 0
	 */
	@Schema(description = "是否删除：0 否、1 是；默认 0")
	private Integer deleted;

}
