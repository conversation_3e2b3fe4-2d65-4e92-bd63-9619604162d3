package com.renpho.erp.mdm.client.currency.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class CurrencyVo {

    @Schema(description = "币种id")
    private Integer id;

    @Schema(description = "币种编码")
    private String code;

    @Schema(description = "币种符号")
    private String symbol;

    @Schema(description = "币种状态")
    private Integer status;

    private String statusName;

    @Schema(description = "ns编码")
    private String nsId;

    @Schema(description = "币种创建人")
    private Integer createBy;

    @Schema(description = "币种创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "币种修改人")
    private Integer updateBy;

    @Schema(description = "币种修改人")
    private String updateName;

    @Schema(description = "币种修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "币种删除标识 0 未删除1删除")
    private Integer deleted;

    @Schema(description = "币种名称")
    private String name;

    @Schema(description = "币种语言")
    private List<CurrencyLanguageVo> languages;

}
