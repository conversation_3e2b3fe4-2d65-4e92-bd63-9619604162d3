package com.renpho.erp.mdm.client.currency.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CurrencyLanguageVo {

	/**
	 * 主键
	 */
	@Schema(description = "币种语言id")
	private Integer id;

	/**
	 * 币种ID
	 */
	@Schema(description = "币种ID")
	private Integer currencyId;

	/**
	 * 语言
	 */
	@Schema(description = "币种语言")
	private String language;

	/**
	 * 名称
	 */
	@Schema(description = "币种名称")
	private String name;

	/**
	 * 创建人ID
	 */
	@Schema(description = "币种创建人id")
	private Integer createBy;

	/**
	 * 创建时间
	 */
	@Schema(description = "币种创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime createTime;

	/**
	 * 更新人ID
	 */
	@Schema(description = "币种修改人")
	private Integer updateBy;

	/**
	 * 更新时间
	 */
	@Schema(description = "币种更新时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime updateTime;

	/**
	 * 是否删除：0 否、1 是；默认 0
	 */
	@Schema(description = " 是否删除：0 否、1 是；默认 0")
	private Integer deleted;

}
