package com.renpho.erp.mdm.client.costcenter;

import com.renpho.erp.mdm.client.costcenter.command.CostCenterQuery;
import com.renpho.erp.mdm.client.costcenter.vo.CostCenterVo;
import com.renpho.erp.openfeign.annotation.NoToken;
import com.renpho.karma.dto.R;
import com.renpho.karma.openfeign.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 店铺管理 feign client
 *
 * <AUTHOR>
 * @since 2024/9/20
 */
@FeignClient(name = "erp-mdm", configuration = FeignConfiguration.class)
public interface RemoteCostCenterService {

	@NoToken
	@PostMapping("/costcenter/getList")
	R<List<CostCenterVo>> getList(@RequestBody CostCenterQuery request);

	@NoToken
	@PostMapping("/costcenter/getListByName")
	R<List<CostCenterVo>> getListByName(@RequestBody List<String> name);
}
