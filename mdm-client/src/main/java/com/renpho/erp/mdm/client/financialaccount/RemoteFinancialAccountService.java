package com.renpho.erp.mdm.client.financialaccount;

import com.renpho.erp.mdm.client.financialaccount.command.FinancialAccountClientQuery;
import com.renpho.erp.mdm.client.financialaccount.vo.FinancialAccountClientVo;
import com.renpho.erp.openfeign.annotation.NoToken;
import com.renpho.karma.dto.R;
import com.renpho.karma.openfeign.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 会计科目
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@FeignClient(name = "erp-mdm", configuration = FeignConfiguration.class)
public interface RemoteFinancialAccountService {

    /**
     * 根据条件查询会计科目
     *
     * @param request 条件
     * @return 会计科目
     */
    @NoToken
    @PostMapping("/financialaccount/inner/list")
    R<List<FinancialAccountClientVo>> getList(@RequestBody FinancialAccountClientQuery request);

}
