package com.renpho.erp.mdm.client.internalTransaction;

import com.renpho.erp.mdm.client.internalTransaction.command.InternalTransactionMarkup;
import com.renpho.erp.openfeign.annotation.NoToken;
import com.renpho.karma.dto.R;
import com.renpho.karma.openfeign.FeignConfiguration;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;

@FeignClient(name = "erp-mdm", configuration = FeignConfiguration.class)
public interface InternalTransactionService {

	@NoToken
	@PostMapping("/internalTransaction/getMarkup")
	R<BigDecimal> getMarkup(@Valid @RequestBody InternalTransactionMarkup markupParam);

}
