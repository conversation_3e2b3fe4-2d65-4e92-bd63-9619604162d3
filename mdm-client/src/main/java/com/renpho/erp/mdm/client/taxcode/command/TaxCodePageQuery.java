package com.renpho.erp.mdm.client.taxcode.command;


import com.renpho.karma.dto.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * @author: <PERSON>
 * @description: 税码查询实体
 * @date: 2024/11/21 10:14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TaxCodePageQuery extends PageQuery {


    @Schema(name = "typeId", description = "税种id")
    private Integer typeId;

    @Schema(name = "codeName", description = "税码,模糊搜索")
    private String codeName;

    @Schema(name = "category", description = "税务类别字典")
    private String category;

    @Schema(name = "country", description = "国家字典类型")
    private String country;

    @Schema(name = "timeStatus", description = "时间类型 0-创建时间, 1-更新时间")
    private Integer timeStatus;

    @Schema(name = "startTime", description = "开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(name = "endTime", description = "结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(name = "exportStatus", description = "导出状态 0-按条件，1-导出全部")
    private Integer exportStatus;

}
