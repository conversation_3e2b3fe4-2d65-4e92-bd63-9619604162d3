package com.renpho.erp.mdm.client.currency.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Collection;

@Data
public class CurrencyQuery {

	/**
	 * 编码
	 */
	private String code;

	private Collection<String> codes;

	/**
	 * 编码全路径
	 */
	private String name;

	/**
	 * 负责人ID，来源OUM的用户ID
	 */
	private Integer ownerId;

	/**
	 * 状态：0 禁用；1 启用
	 */
	private Integer status;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime startTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime endTime;

	private Integer pageIndex = 1;

	private Integer pageSize = 20;

	private String updateName;

}
