package com.renpho.erp.mdm.client.saleschannel;

import com.renpho.erp.mdm.client.saleschannel.command.SalesChannelCodesQuery;
import com.renpho.erp.mdm.client.saleschannel.command.SalesChannelIdsQuery;
import com.renpho.erp.mdm.client.saleschannel.command.SalesChannelNamesQuery;
import com.renpho.erp.mdm.client.saleschannel.vo.SalesChannelVo;
import com.renpho.erp.openfeign.annotation.NoToken;
import com.renpho.karma.dto.R;
import com.renpho.karma.openfeign.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * MDM 销售渠道管理 Feign Client
 *
 * <AUTHOR>
 * @since 2024/10/17
 */
@FeignClient(name = "erp-mdm", configuration = FeignConfiguration.class)
public interface RemoteSalesChannelService {

    @NoToken
    @PostMapping("/saleschannel/findAll")
    R<List<SalesChannelVo>> findAll();

    @NoToken
    @PostMapping("/saleschannel/findByNames")
    R<List<SalesChannelVo>> findByNames(@RequestBody SalesChannelNamesQuery request);

    @NoToken
    @PostMapping("/saleschannel/findByCodes")
    R<List<SalesChannelVo>> findByCodes(@RequestBody SalesChannelCodesQuery request);

    @NoToken
    @PostMapping("/saleschannel/findByIds")
    R<List<SalesChannelVo>> findByIds(@RequestBody SalesChannelIdsQuery request);


}
