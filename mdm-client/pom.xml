<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.renpho.erp</groupId>
        <artifactId>mdm-parent</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>mdm-client</artifactId>
    <name>${project.artifactId}</name>
    <description>客户端API、模块</description>

    <dependencies>
        <!-- ==================================== -->
        <!-- the 3rd part -->
        <!-- ==================================== -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        </dependency>

        <!-- ==================================== -->
        <!-- 内部依赖 -->
        <!-- ==================================== -->
        <dependency>
            <groupId>com.renpho.karma</groupId>
            <artifactId>karma-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>erp-data-trans</artifactId>
        </dependency>
        <dependency>
            <groupId>com.renpho.karma</groupId>
            <artifactId>karma-dto</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>erp-openfeign-sdk</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
