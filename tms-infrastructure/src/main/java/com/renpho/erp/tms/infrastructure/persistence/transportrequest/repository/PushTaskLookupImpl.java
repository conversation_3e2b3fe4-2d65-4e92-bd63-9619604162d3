package com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.tms.domain.inbound.BizPskuWarehouseContainer;
import com.renpho.erp.tms.domain.inbound.InboundBusinessType;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.mapper.PushTaskMapper;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.po.PushTaskPo;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.po.converter.PushTaskConverter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 物流计划推送任务表 Lookup 实现
 *
 * <AUTHOR>
 * @since 2025/7/14
 */
@Repository
@RequiredArgsConstructor
public class PushTaskLookupImpl extends ServiceImpl<PushTaskMapper, PushTaskPo> implements PushTaskLookup {

    private final PushTaskConverter pushTaskConverter;

    @Override
    public Optional<PushTask> findById(PushTaskId id) {
        return Optional.ofNullable(id)
                .map(PushTaskId::id)
                .flatMap(this::getOptById)
                .map(pushTaskConverter::toDomain);
    }

    @Override
    public List<PushTask> findByIds(Collection<PushTaskId> historyIds) {
        Set<Integer> ids = CollectionUtils.emptyIfNull(historyIds)
                .stream().filter(Objects::nonNull)
                .map(PushTaskId::id)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        List<PushTaskPo> pos = listByIds(ids);
        return pushTaskConverter.toDomains(pos);
    }

    @Override
    public List<PushTask> findByBizNo(String bizNo) {
        if (StringUtils.isBlank(bizNo)) {
            return List.of();
        }

        List<PushTaskPo> pos = lambdaQuery().eq(PushTaskPo::getBizNo, bizNo).list();
        return pushTaskConverter.toDomains(pos);
    }

    @Override
    public Map<String, List<PushTask>> findByBizNos(Collection<String> bizNo) {
        Set<String> nos = CollectionUtils.emptyIfNull(bizNo).stream().filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(nos)) {
            return Map.of();
        }

        List<PushTaskPo> pos = lambdaQuery().in(PushTaskPo::getBizNo, bizNo).list();
        return pushTaskConverter.toDomains(pos)
                .stream()
                .collect(Collectors.groupingBy(PushTask::getBizNo));
    }

    @Override
    public List<PushTask> findByBizNoAndPskuAndTaskType(String bizNo, Collection<String> pskus, Collection<PushTaskType> taskTypes) {
        return findByBizNoAndPskuAndTaskType(bizNo, pskus, taskTypes, null);
    }

    public List<PushTask> findByBizNoAndPskuAndTaskType(String bizNo, Collection<String> pskus, Collection<PushTaskType> taskTypes, PushTaskStatus taskStatus) {
        if (StringUtils.isBlank(bizNo)) {
            return List.of();
        }
        pskus = CollectionUtils.emptyIfNull(pskus).stream().filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Set<String> types = CollectionUtils.emptyIfNull(taskTypes).stream().filter(Objects::nonNull).map(PushTaskType::name).collect(Collectors.toSet());
        Optional<Integer> status = Optional.ofNullable(taskStatus).map(PushTaskStatus::getValue);

        List<PushTaskPo> pos = lambdaQuery()
                .in(PushTaskPo::getBizNo, bizNo)
                .in(CollectionUtils.isNotEmpty(pskus), PushTaskPo::getPsku, pskus)
                .in(CollectionUtils.isNotEmpty(types), PushTaskPo::getTaskType, types)
                .in(status.isPresent(), PushTaskPo::getStatus, status.orElse(null))
                .list();
        return pushTaskConverter.toDomains(pos);
    }

    @Override
    public List<PushTask> findByTaskType(InboundBusinessType bizType, WarehouseProviderType warehouseType, PushTaskType taskType, PushTaskStatus pushTaskStatus) {
        if (bizType == null) {
            return List.of();
        }
        if (warehouseType == null) {
            return List.of();
        }
        if (taskType == null) {
            return List.of();
        }

        // 小于指定次数
        List<PushTaskPo> pos = lambdaQuery()
                .eq(PushTaskPo::getBizType, bizType)
                .eq(PushTaskPo::getWarehouseType, warehouseType)
                .eq(PushTaskPo::getTaskType, taskType)
                .eq(PushTaskPo::getStatus, pushTaskStatus)
                .list();
        return pushTaskConverter.toDomains(pos);
    }

    @Override
    public boolean isTasksExist(String trNo, Collection<PushTaskType> taskTypes) {
        return isTasksExist(trNo, List.of(), taskTypes);
    }

    @Override
    public boolean isTasksExist(String trNo, Collection<PushTaskType> taskTypes, PushTaskStatus taskStatus) {
        return isTasksExist(trNo, List.of(), taskTypes, taskStatus);
    }

    @Override
    public boolean isTasksExist(String trNo, Collection<String> pskus, Collection<PushTaskType> taskTypes) {
        return isTasksExist(trNo, pskus, taskTypes, null);
    }

    /**
     * 根据业务单号、产品SKU、任务类型、推送状态判断任务是否存在
     *
     * @param bizNo      业务单号, 必填
     * @param taskTypes  任务类型, 必填
     * @param pskus      产品SKU, 为空则查询所有SKU
     * @param taskStatus 推送状态, 为空则查询所有状态
     * <AUTHOR>
     * @since 2025/7/18
     */
    @Override
    public boolean isTasksExist(String bizNo, Collection<String> pskus, Collection<PushTaskType> taskTypes, PushTaskStatus taskStatus) {
        if (StringUtils.isBlank(bizNo)) {
            return false;
        }
        Set<String> types = CollectionUtils.emptyIfNull(taskTypes).stream().filter(Objects::nonNull).map(PushTaskType::name).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(types)) {
            return false;
        }
        pskus = CollectionUtils.emptyIfNull(pskus).stream().filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Optional<Integer> status = Optional.ofNullable(taskStatus).map(PushTaskStatus::getValue);
        return lambdaQuery()
                .eq(PushTaskPo::getBizNo, bizNo)
                .in(PushTaskPo::getTaskType, types)
                .in(CollectionUtils.isNotEmpty(pskus), PushTaskPo::getPsku, pskus)
                .eq(status.isPresent(), PushTaskPo::getStatus, status.orElse(null))
                .exists();

    }

    @Override
    public <T extends BizPskuWarehouseContainer> Map<String, PushTask> findLastFailureByBizNo(Collection<T> bizNos) {
        Collection<String> nos = CollectionUtils.emptyIfNull(bizNos)
                .stream().filter(Objects::nonNull)
                .map(BizPskuWarehouseContainer::getBizNo).filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(nos)) {
            return Map.of();
        }
        List<Integer> ids = baseMapper.findLastFailureByBizNo(nos);
        if (CollectionUtils.isEmpty(ids)) {
            return Map.of();
        }
        List<PushTaskPo> pos = listByIds(ids);
        return pos.stream()
                .map(pushTaskConverter::toDomain)
                .collect(Collectors.toMap(PushTask::getBizNo, Function.identity()));
    }

    @Override
    public List<PushTask> findByTaskTypeAndBizNo(InboundBusinessType inboundBusinessType, List<String> bizNoList, PushTaskType pushTaskType, PushTaskStatus pushTaskStatus) {
        if(bizNoList == null || bizNoList.isEmpty()){
            return List.of();
        }

        // 小于指定次数
        List<PushTaskPo> pos = lambdaQuery()
                .eq(PushTaskPo::getBizType, inboundBusinessType)
                .eq(PushTaskPo::getTaskType, pushTaskType)
                .eq(PushTaskPo::getStatus, pushTaskStatus)
                .in(PushTaskPo::getBizNo, bizNoList)
                .list();
        return pushTaskConverter.toDomains(pos);
    }

}
