package com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.security.util.SecurityUtils;
import com.renpho.erp.tms.domain.exception.BusinessException;
import com.renpho.erp.tms.domain.operator.Operator;
import com.renpho.erp.tms.domain.operator.OperatorId;
import com.renpho.erp.tms.domain.transportorder.TransportOrderStatusEnum;
import com.renpho.erp.tms.domain.transportrequest.TransportRequest;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestId;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestItem;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestStatus;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.mapper.TransportRequestOrderMapper;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.po.TransportRequestOrderPo;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.po.converter.TransportRequestConverter;
import com.renpho.erp.tms.infrastructure.remote.user.repository.OperatorLookup;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * {@inheritDoc}
 *
 * <AUTHOR>
 * @since 2025/5/21
 */
@Service
@RequiredArgsConstructor
public class TransportRequestOrderRepositoryImpl extends ServiceImpl<TransportRequestOrderMapper, TransportRequestOrderPo> implements TransportRequestOrderRepository {

    private final TransportRequestConverter transportRequestConverter;
    private final TransportRequestOrderItemRepository transportRequestOrderItemRepository;
    private final OperatorLookup operatorLookup;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TransportRequestId add(TransportRequest condition) {
        Operator operator = operatorLookup.findSystemUser();
        condition.setCreated(operator);
        condition.setUpdated(operator);
        TransportRequestOrderPo po = transportRequestConverter.toPo(condition);
        save(po);
        TransportRequest domain = transportRequestConverter.toDomain(po);
        TransportRequestItem item = condition.getItem();
        item.setTrId(domain.getId());
        item.setTrNo(domain.getTrNo());
        item.setCreated(operator);
        item.setUpdated(operator);
        domain.setItem(item);
        transportRequestOrderItemRepository.add(domain);
        return domain.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TransportRequestId updateById(TransportRequest domain) {
        Operator operator = operatorLookup.findSystemUser();
        domain.setUpdated(operator);
        TransportRequestOrderPo po = transportRequestConverter.toPo(domain);
        updateById(po);
        return domain.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearShipmentIdById(Integer id) {
        Integer updateBy = Optional.ofNullable(operatorLookup.findSystemUser()).map(Operator::getOperatorId).map(OperatorId::id).orElse(0);
        return  lambdaUpdate().eq(TransportRequestOrderPo::getId, id)
                .set(TransportRequestOrderPo::getShipmentId, "")
                .set(TransportRequestOrderPo::getUpdateBy, updateBy)
                .set(TransportRequestOrderPo::getUpdateTime, LocalDateTime.now())
                .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TransportRequestId updateByPd(TransportRequest command) {
        Optional<TransportRequest> optional = Optional.ofNullable(command);
        Integer id = optional.map(TransportRequest::getId)
                .map(TransportRequestId::id)
                .map(this::getById)
                .map(TransportRequestOrderPo::getId)
                .orElseThrow(() -> new BusinessException("id.not.exist"));

        Optional<String> shipmentId = optional.map(TransportRequest::getShipmentId).filter(StringUtils::isNotBlank);
        Optional<String> referenceId = optional.map(TransportRequest::getReferenceId).filter(StringUtils::isNotBlank);
        Optional<LocalDate> latestDeliveryDate = optional.map(TransportRequest::getLatestDeliveryDate);

        if (Stream.of(shipmentId, referenceId, latestDeliveryDate).anyMatch(Optional::isPresent)) {
            lambdaUpdate().eq(TransportRequestOrderPo::getId, id)
                    .set(shipmentId.isPresent(), TransportRequestOrderPo::getShipmentId, shipmentId.orElse(null))
                    .set(referenceId.isPresent(), TransportRequestOrderPo::getReferenceId, referenceId.orElse(null))
                    .set(latestDeliveryDate.isPresent(), TransportRequestOrderPo::getLatestDeliveryDate, latestDeliveryDate.orElse(null))
                    .set(shipmentId.isPresent() && referenceId.isPresent(), TransportRequestOrderPo::getStatus, TransportRequestStatus.PENDING_CONSOLIDATION.getValue())
                    .update();
        }

        return command.getId();
    }

    @Override
    public TransportRequestId updateQcResult(TransportRequest domain) {
        lambdaUpdate().eq(TransportRequestOrderPo::getId, domain.getId().id())
                .set(TransportRequestOrderPo::getQcResult, domain.getQcResult().getValue())
                .set(TransportRequestOrderPo::getQcId, domain.getQcId())
                .set(TransportRequestOrderPo::getQcNo, domain.getQcNo())
                .set(TransportRequestOrderPo::getUpdateBy, operatorLookup.findSystemUser().getOperatorId().id())
                .set(TransportRequestOrderPo::getUpdateTime, LocalDateTime.now())
                .update();
        return domain.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TransportRequestId cancelByPd(TransportRequestId trId) {
        lambdaUpdate().eq(TransportRequestOrderPo::getId, trId.id())
                .in(TransportRequestOrderPo::getStatus, TransportRequestStatus.PENDING.getValue(), TransportRequestStatus.PENDING_CONSOLIDATION.getValue())
                .set(TransportRequestOrderPo::getStatus, TransportRequestStatus.CANCEL.getValue())
                .set(TransportRequestOrderPo::getUpdateBy, operatorLookup.findSystemUser().getOperatorId().id())
                .set(TransportRequestOrderPo::getUpdateTime, LocalDateTime.now())
                .update();
        return trId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TransportRequestId updateInboundOrder(TransportRequest command) {
        lambdaUpdate().eq(TransportRequestOrderPo::getId, command.getId().id())
                .set(TransportRequestOrderPo::getStatus, TransportRequestStatus.PENDING_CONSOLIDATION.getValue())
                .set(TransportRequestOrderPo::getCartonLabelFileIds, JSON.toJSONString(command.getCartonLabelFileIds()))
                .set(TransportRequestOrderPo::getShipmentId, command.getShipmentId())
                .set(TransportRequestOrderPo::getUpdateBy, SecurityUtils.getUserId())
                .set(TransportRequestOrderPo::getUpdateTime, LocalDateTime.now())
                .update();
        return command.getId();
    }

    @Override
    public void updateInspectionByTrackingNo(TransportRequest domain) {
        lambdaUpdate().eq(TransportRequestOrderPo::getTrackingNo, domain.getTrackingNo())
                .set(TransportRequestOrderPo::getExportInspection, domain.getExportInspection())
                .set(TransportRequestOrderPo::getImportInspection, domain.getImportInspection())
                .set(StringUtils.isNotBlank(domain.getCustomsClearanceRemark()),
                        TransportRequestOrderPo::getCustomsClearanceRemark, domain.getCustomsClearanceRemark())
                .update();
    }

    @Override
    public void batchUpdate(List<TransportRequest> transportRequestList) {
        if (CollectionUtils.isEmpty(transportRequestList)) {
            return;
        }
        List<TransportRequestOrderPo> pos = transportRequestList.stream().map(transportRequestConverter::toPo).toList();
        SpringUtil.getBean(this.getClass()).updateBatchById(pos);
    }

    @Override
    public void batchUpdateTax(List<TransportRequest> transportRequestList) {
        if (CollectionUtils.isNotEmpty(transportRequestList)) {
            for (TransportRequest tr : transportRequestList) {
                lambdaUpdate().eq(TransportRequestOrderPo::getId, tr.getId().id())
                        .set(TransportRequestOrderPo::getEstimatedTax, tr.getEstimatedTax())
                        .set(TransportRequestOrderPo::getTaxCurrency, tr.getEstimatedTaxCurrency().getCode())
                        .update();
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void clearTOValue(List<TransportRequestId> trIds) {
        Set<Integer> ids = trIds.stream().map(TransportRequestId::id).collect(Collectors.toSet());
        //清除TO相关字段值
        lambdaUpdate().in(TransportRequestOrderPo::getId, ids)
                .set(TransportRequestOrderPo::getStatus, TransportRequestStatus.PENDING_CONSOLIDATION.getValue())
                .set(TransportRequestOrderPo::getToId, 0)
                .set(TransportRequestOrderPo::getToNo, "")
                .set(TransportRequestOrderPo::getShipStatus, TransportOrderStatusEnum.DEFAULT.getValue())
                .set(TransportRequestOrderPo::getTrackingNo, "")
                .update();

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TransportRequestId> consolidation(Collection<TransportRequestId> commands,
                                                  String toNo,
                                                  Integer toId,
                                                  TransportOrderStatusEnum status) {
        Set<Integer> ids = commands.stream().map(TransportRequestId::id).collect(Collectors.toSet());
        lambdaUpdate().in(TransportRequestOrderPo::getId, ids)
                .set(TransportRequestOrderPo::getStatus, TransportRequestStatus.CONSOLIDATION.getValue())
                .set(TransportRequestOrderPo::getToId, toId)
                .set(TransportRequestOrderPo::getToNo, toNo)
                .set(TransportRequestOrderPo::getShipStatus, status.getValue())
                .set(TransportRequestOrderPo::getUpdateBy, SecurityUtils.getUserId())
                .set(TransportRequestOrderPo::getUpdateTime, LocalDateTime.now())
                .update();
        return new ArrayList<>(CollectionUtils.emptyIfNull(commands));
    }

}
