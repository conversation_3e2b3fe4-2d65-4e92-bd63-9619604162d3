package com.renpho.erp.tms.infrastructure.persistence.transportrequest.po.converter;

import com.renpho.erp.tms.domain.inbound.BizPskuContainer;
import com.renpho.erp.tms.domain.inbound.BizPskuWarehouseContainer;
import com.renpho.erp.tms.domain.inbound.InboundBusinessType;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.operator.Operator;
import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.po.PushTaskPo;
import com.renpho.erp.tms.infrastructure.remote.user.converter.OperatorConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.*;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 物流计划推送任务表 Converter
 *
 * <AUTHOR>
 * @since 2025/7/14
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING,
        imports = {InboundBusinessType.class, PushTaskStatus.class, Optional.class, WarehouseProviderType.class, LocalDateTime.class, WarehouseProviderType.class},
        uses = {TransportRequestConverter.class, OperatorConverter.class})
public interface PushTaskConverter {

    @Mapping(target = "created.operatorId", source = "createBy")
    @Mapping(target = "created.operateTime", source = "createTime")
    @Mapping(target = "updated.operatorId", source = "updateBy")
    @Mapping(target = "updated.operateTime", source = "updateTime")
    PushTask toDomain(PushTaskPo po);

    @InheritInverseConfiguration(name = "toDomain")
    PushTaskPo toPo(PushTask domain);

    @IterableMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    List<PushTask> toDomains(Collection<PushTaskPo> pos);

    @IterableMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    List<PushTaskPo> toPos(Collection<PushTask> domains);

    @Mapping(target = "id", source = "id")
    PushTaskId toId(Integer id);

    default Integer toId(PushTaskId id) {
        return Optional.ofNullable(id).map(PushTaskId::id).orElse(null);
    }

    default Integer toStatus(PushTaskStatus status) {
        return Optional.ofNullable(status).map(PushTaskStatus::getValue).orElse(null);
    }

    default PushTaskStatus toStatus(Integer status) {
        return PushTaskStatus.fromValue(status);
    }

    default List<PushTaskAddCmd> toAddCmds(BizPskuWarehouseContainer tr, Operator operator) {
        return PushTaskType.initializable()
                .stream()
                .map(taskType -> toAddCmd(tr, taskType, operator))
                .toList();
    }

    @Mapping(target = "bizId", source = "biz.bizId")
    @Mapping(target = "bizNo", source = "biz.bizNo")
    @Mapping(target = "bizType", source = "biz.bizType")
    @Mapping(target = "fnSku", source = "biz.fnSku")
    @Mapping(target = "taskType", source = "taskType")
    @Mapping(target = "targetSystem", source = "taskType.targetSystem")
    @Mapping(target = "warehouseType", expression = "java(WarehouseProviderType.NOT_EXIST)")
    @Mapping(target = "pushTime", expression = "java(LocalDateTime.now())")
    @Mapping(target = "created", source = "operator")
    @Mapping(target = "updated", source = "operator")
    PushTaskAddCmd toAddCmd(BizPskuContainer biz, PushTaskType taskType, Operator operator);

    @InheritConfiguration(name = "toAddCmd")
    @Mapping(target = "warehouseType", source = "biz.warehouseType", defaultExpression = "java(WarehouseProviderType.NOT_EXIST)")
    PushTaskAddCmd toAddCmd(BizPskuWarehouseContainer biz, PushTaskType taskType, Operator operator);

    @Mapping(target = "retryCount", source = "retryCount")
    @Mapping(target = "errMsg", source = "errMsg")
    void updateRetryCountAndErrMsg(@MappingTarget PushTaskAddCmd cmd, int retryCount, String errMsg);

    @Mapping(target = "status", source = "status", defaultExpression = "java(PushTaskStatus.PENDING.getValue())")
    @Mapping(target = "createBy", source = "command.created.operatorId.id", defaultExpression = "java(0)")
    @Mapping(target = "updateBy", source = "command.updated.operatorId.id", defaultExpression = "java(0)")
    PushTaskPo toAddPo(PushTaskAddCmd command, PushTaskStatus status);

    default List<PushTaskPo> toAddPos(Collection<PushTaskAddCmd> commands) {
        return CollectionUtils.emptyIfNull(commands)
                .stream()
                .filter(Objects::nonNull)
                .map(cmd -> toAddPo(cmd, cmd.getStatus()))
                .toList();
    }

    @Mapping(target = "bizId", source = "biz.bizId")
    @Mapping(target = "bizNo", source = "biz.bizNo")
    @Mapping(target = "bizType", source = "biz.bizType")
    @Mapping(target = "fnSku", source = "biz.fnSku")
    @Mapping(target = "taskType", source = "taskType")
    @Mapping(target = "targetSystem", source = "taskType.targetSystem")
    @Mapping(target = "warehouseType", source = "warehouseType")
    @Mapping(target = "pushTime", expression = "java(LocalDateTime.now())")
    @Mapping(target = "retryCount", source = "retryCount")
    @Mapping(target = "created", source = "operator")
    @Mapping(target = "updated", source = "operator")
    PushTaskAddCmd toAddCmd(BizPskuContainer biz, PushTaskType taskType, WarehouseProviderType warehouseType, Integer retryCount, Operator operator);
}
