package com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.tms.domain.inbound.BizPskuContainer;
import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.mapper.PushTaskMapper;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.po.PushTaskPo;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.po.converter.PushTaskConverter;
import com.renpho.erp.tms.infrastructure.remote.user.repository.OperatorLookup;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * 物流计划推送任务表 Repository 实现
 *
 * <AUTHOR>
 * @since 2025/7/14
 */
@Repository
@RequiredArgsConstructor
public class PushTaskRepositoryImpl extends ServiceImpl<PushTaskMapper, PushTaskPo> implements PushTaskRepository {

    private final PushTaskConverter pushTaskConverter;
    private final OperatorLookup operatorLookup;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer add(PushTask task) {
        PushTaskPo po = pushTaskConverter.toPo(task);
        // 系统用户，拿不到session信息
        po.setCreateBy(0);
        po.setUpdateBy(0);
        baseMapper.insert(po);
        return po.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PushTask add(PushTaskAddCmd cmd, PushTaskStatus status) {
        PushTaskPo po = pushTaskConverter.toAddPo(cmd, status);
        save(po);
        return pushTaskConverter.toDomain(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PushTask update(PushTask task) {
        PushTaskPo po = pushTaskConverter.toPo(task);
        updateById(po);
        return pushTaskConverter.toDomain(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PushTask> addBatch(Collection<PushTaskAddCmd> commands) {
        List<PushTaskPo> pos = pushTaskConverter.toAddPos(commands);
        saveBatch(pos);
        return pushTaskConverter.toDomains(pos);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Optional<PushTask> update(BizPskuContainer biz, PushTaskType taskType, PushTaskStatus status, Integer retryCount, String errMsg) {
        lambdaUpdate().eq(PushTaskPo::getBizNo, biz.getBizNo())
                .eq(PushTaskPo::getPsku, biz.getPsku())
                .eq(PushTaskPo::getTaskType, taskType.name())
                .set(PushTaskPo::getStatus, status.getValue())
                .set(PushTaskPo::getRetryCount, retryCount)
                .set(PushTaskPo::getErrMsg, errMsg)
                .set(PushTaskPo::getUpdateBy, findUpdateBy())
                .update();
        List<PushTaskPo> pos = lambdaQuery().eq(PushTaskPo::getBizNo, biz.getBizNo())
                .eq(PushTaskPo::getPsku, biz.getPsku())
                .eq(PushTaskPo::getTaskType, taskType.name())
                .list();
        return CollectionUtils.emptyIfNull(pos).stream()
                .max(Comparator.comparing(PushTaskPo::getUpdateTime).thenComparing(PushTaskPo::getId))
                .map(pushTaskConverter::toDomain);
    }

    private Integer findUpdateBy() {
        return operatorLookup.findSystemUser().getOperatorId().id();
    }

}
