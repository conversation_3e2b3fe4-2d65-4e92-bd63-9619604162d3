package com.renpho.erp.tms.infrastructure.remote.inventory.repository;

import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.ims.client.feign.inventory.command.RemoteInventoryChangeCommand;
import com.renpho.erp.ims.client.feign.inventory.common.DocumentNodeTypeEnum;
import com.renpho.erp.ims.client.feign.inventory.vo.RemoteInventoryChangeVo;
import com.renpho.erp.tms.domain.inbound.InboundAllocation;
import com.renpho.erp.tms.domain.inbound.InboundRecord;
import com.renpho.erp.tms.domain.inbound.InboundRecordId;
import com.renpho.erp.tms.domain.transportorder.TransportOrder;
import com.renpho.erp.tms.domain.transportrequest.TransportRequest;
import com.renpho.erp.tms.infrastructure.remote.inventory.RemoteInventoryService;
import com.renpho.erp.tms.infrastructure.remote.inventory.converter.InventoryConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jmolecules.ddd.types.Repository;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 库存变更 repository
 *
 * <AUTHOR>
 * @since 2025/7/23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InventoryRepository implements Repository<InboundRecord, InboundRecordId> {

    private final RemoteInventoryService remoteInventoryService;
    private final InventoryConverter inventoryConverter;

    /**
     * 交货 IMS
     *
     * <AUTHOR>
     * @since 2025/7/24
     */
    @Lock4j(name = "InventoryRepository.handover", keys = {"#tr.trNo", "#tr.psku"}, expire = 60000, acquireTimeout = 60000)
    public List<String> handover(TransportRequest tr, TransportOrder to) {
        RemoteInventoryChangeCommand cmd = inventoryConverter.toCommand(null, tr, to, null, null);
        for (RemoteInventoryChangeCommand.Document document : CollectionUtils.emptyIfNull(cmd.getDocumentList())) {
            for (RemoteInventoryChangeCommand.Document.RelatedOrderDetail orderDetail : CollectionUtils.emptyIfNull(document.getRelatedOrderDetailList())) {
                orderDetail.setDocumentNodeType(DocumentNodeTypeEnum.TR_CARGO_TO_CARRIER);
                orderDetail.setBizTime(tr.getDeliveryTime().atStartOfDay());
            }
        }
        List<RemoteInventoryChangeVo> vos = execute(cmd);
        return CollectionUtils.emptyIfNull(vos).stream().map(RemoteInventoryChangeVo::getDocumentNumber).toList();
    }

    /**
     * 离港 IMS
     *
     * <AUTHOR>
     * @since 2025/7/24
     */
    @Lock4j(name = "InventoryRepository.departure", keys = {"#tr.trNo", "#tr.psku"}, expire = 60000, acquireTimeout = 60000)
    public List<String> departure(TransportRequest tr, TransportOrder to) {
        RemoteInventoryChangeCommand cmd = inventoryConverter.toCommand(null, tr, to, null, null);
        for (RemoteInventoryChangeCommand.Document document : CollectionUtils.emptyIfNull(cmd.getDocumentList())) {
            for (RemoteInventoryChangeCommand.Document.RelatedOrderDetail orderDetail : CollectionUtils.emptyIfNull(document.getRelatedOrderDetailList())) {
                orderDetail.setDocumentNodeType(DocumentNodeTypeEnum.TR_SHIPMENT_DEPARTED);
                orderDetail.setBizTime(tr.getActualDepartureTime().atStartOfDay());
            }
        }
        List<RemoteInventoryChangeVo> vos = execute(cmd);
        return CollectionUtils.emptyIfNull(vos).stream().map(RemoteInventoryChangeVo::getDocumentNumber).toList();
    }

    /**
     * 入库/上架 IMS
     *
     * <AUTHOR>
     * @since 2025/7/24
     */
    @Lock4j(name = "InventoryRepository.inbound", keys = {"#tr.trNo", "#tr.psku"}, expire = 60000, acquireTimeout = 60000)
    public List<String> inbound(InboundRecord inbound, TransportRequest tr, TransportOrder to, InboundAllocation freight, InboundAllocation tax) {
        RemoteInventoryChangeCommand cmd = inventoryConverter.toCommand(inbound, tr, to, freight, tax);
        List<RemoteInventoryChangeVo> vos = execute(cmd);
        return CollectionUtils.emptyIfNull(vos).stream().map(RemoteInventoryChangeVo::getDocumentNumber).toList();
    }

    private List<RemoteInventoryChangeVo> execute(RemoteInventoryChangeCommand cmd) {
        try {
            return remoteInventoryService.execute(cmd);
        } catch (Exception e) {
            log.error("调用 IMS 库存变更异常, 入参: [{}]", JSON.toJSONString(cmd), e);
            throw e;
        }
    }

}
