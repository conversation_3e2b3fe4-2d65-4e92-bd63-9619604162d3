<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.renpho.erp</groupId>
        <artifactId>tms-parent</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>tms-model</artifactId>
    <name>${project.artifactId}</name>
    <description>实体类层模块</description>


    <dependencies>
        <!-- ==================================== -->
        <!-- the 3rd part -->
        <!-- ==================================== -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>2.0.57</version>
        </dependency>
        <!-- ==================================== -->
        <!-- 内部依赖 -->
        <!-- ==================================== -->
    </dependencies>


    <build>
        <plugins>
            <plugin>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
