package com.renpho.erp.tms.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class TransportRequestOrderDTO implements Serializable {

	/**
	 * id
	 */
	private Integer id;

	/**
	 * 物流计划单号
	 */
	private String trNo;

	/**
	 * 运单号
	 */
	private String trackingNo;

	/**
	 * 总数量
	 */
	private Integer totalQty;

	/**
	 * 总箱数
	 */
	private BigDecimal totalBoxQty;

	/**
	 * 总毛重 kg
	 */
	private BigDecimal totalGrossWeight;

	/**
	 * 总净重 kg
	 */
	private BigDecimal totalNetWeight;

	/**
	 * 总体积 m³
	 */
	private BigDecimal totalVolume;

    /**
     * 货主id
     */
	private Integer ownerId;

    /**
     * 预估运费
     */
    private BigDecimal estimatedFreight;

    /**
     * 运费币种，如USD
     */
    private String freightCurrency;

    /**
     * 预估税费
     */
    private BigDecimal estimatedTax;

    /**
     * 税费币种
     */
    private String taxCurrency;

    /**
     * 交货时间
     */
    private LocalDate deliveryTime;

    /**
     * 实际离港时间
     */
    private LocalDate actualDepartureTime;

    /**
     * 实际到港时间
     */
    private LocalDate actualArrivalTime;

    /**
     * 实际派送时间
     */
    private LocalDate actualDeliveryTime;

    /**
     * 实际签收时间
     */
    private LocalDate actualReceivedTime;

}
