package com.renpho.erp.tms.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class TransportOrderDTO implements Serializable {

	/**
	 * id
	 */
	private Integer id;

	/**
	 * TO单号
	 */
	private String toNo;

	/**
	 * 总数量
	 */
	private Integer totalQty;

	/**
	 * 总箱数
	 */
	private Integer totalBoxQty;

	/**
	 * 总毛重
	 */
	private BigDecimal totalGrossWeight;

	/**
	 * 总净重
	 */
	private BigDecimal totalNetWeight;

	/**
	 * 总体积
	 */
	private BigDecimal totalVolume;

	/**
	 * 预估税费
	 */
	private BigDecimal estimatedTax;

	/**
	 * 税费币种
	 */
	private String taxCurrency;

	/**
	 * 货代公司ID，取SRM的物流供应商的启用的物流商id
	 */
	private Integer forwarderCompanyId;

	/**
	 * 货代公司Code，取SRM的物流供应商的启用的物流商code
	 */
	private String forwarderCompanyCode;

	/**
	 * 预估运费
	 */
	private BigDecimal estimatedFreight;

	/**
	 * 运费币种，如USD
	 */
	private String freightCurrency;

	/**
	 * 创建人
	 */
	private Integer createBy;

	/**
	 * 物流请款池明细
	 */
	private List<TransportRequestOrderDTO> requestOrderList;

}
