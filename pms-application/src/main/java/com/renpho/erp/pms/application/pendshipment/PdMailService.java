package com.renpho.erp.pms.application.pendshipment;

import cn.hutool.extra.spring.SpringUtil;
import com.renpho.erp.pms.Infrastructure.mail.PendShipmentPlanEmailService;
import com.renpho.erp.pms.application.shipplan.service.ShipmentPlanOrderQueryService;
import com.renpho.erp.pms.application.shipplan.service.ShipmentPlanOrderService;
import com.renpho.erp.pms.domain.purchaseorder.PurchaseOrderFile;
import com.renpho.erp.pms.domain.purchaseorder.PurchaseOrderFileLookup;
import com.renpho.erp.pms.domain.shipplan.ShipmentPlanId;
import com.renpho.erp.pms.domain.shipplan.ShipmentPlanOrder;
import com.renpho.erp.pms.domain.shipplan.ShipmentPlanOrderLookup;
import com.renpho.erp.pms.domain.shipplan.ShipmentPlanOrderRepository;
import com.renpho.erp.pms.domain.shipplan.enums.MailSendStatusEnum;
import com.renpho.erp.pms.domain.warehouse.enums.WarehouseTypeEnum;
import com.renpho.erp.pms.model.common.model.enums.PmsFileBusinessType;
import com.renpho.erp.pms.model.common.model.enums.PmsOrderFileType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * PD 邮件服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PdMailService {

    private final ShipmentPlanOrderLookup shipmentPlanOrderLookup;
    private final ShipmentPlanOrderQueryService queryService;

    private final PendShipmentPlanEmailService pendShipmentPlanEmailService;

    private final ShipmentPlanOrderRepository shipmentPlanOrderRepository;

    private final PurchaseOrderFileLookup orderFileLookup;


    /**
     * 发送邮件
     */
    @Transactional(rollbackFor = Exception.class)
    public void pdSendMail(List<String> pdNos,Boolean isValidPlatform) {

        List<ShipmentPlanOrder> pdList = new ArrayList<>(pdNos.size());
        for (String pdNo : pdNos) {
            ShipmentPlanOrder pdOrderInfoAggOnlyMysql = queryService.getPdOrderInfoAggOnlyMysql(pdNo);
            pdList.add(pdOrderInfoAggOnlyMysql);
        }
        if (CollectionUtils.isEmpty(pdList)){
            return;
        }
        Map<Integer, List<ShipmentPlanOrder>> poAndPdGroup = null;
        if (isValidPlatform){
            // 只有平台仓发送邮件
            poAndPdGroup = pdList.stream()
                    .filter(e -> e.getItem().getDestWarehouseType().equals(WarehouseTypeEnum.PLATFORM_WAREHOUSE.getCode()))
                    .collect(Collectors.groupingBy(ShipmentPlanOrder::getPoId));
        }else{
             poAndPdGroup = pdList.stream()
                    .collect(Collectors.groupingBy(ShipmentPlanOrder::getPoId));
        }


        poAndPdGroup.forEach((k, v) -> {
            List<Integer> partPdIds = v.stream().map(e -> e.getId().id()).distinct().toList();
            // 更新邮件--- 未发送中 ---
            shipmentPlanOrderRepository.updateMailFlag(partPdIds, MailSendStatusEnum.SENDING.getCode(), null);
        });

        // 事务提交后执行异步任务
        Map<Integer, List<ShipmentPlanOrder>> finalPoAndPdGroup = poAndPdGroup;
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                pdSendMailAsync(finalPoAndPdGroup);
            }
        });
    }

    @Async("mailExecutor")
    public void pdSendMailAsync(Map<Integer, List<ShipmentPlanOrder>> poAndPdGroup) {
        log.info("异步触发发送PD邮件");

        poAndPdGroup.forEach((k, v) -> {
            List<Integer> partPdIds = v.stream().map(e -> e.getId().id()).distinct().toList();

            // 前置校验，箱唛文件要存在，shipmentID 不能为空
            ArrayList<Integer> exeIds = new ArrayList<>(partPdIds.size());
            partPdIds.forEach(pdId -> {

                List<ShipmentPlanOrder> list = shipmentPlanOrderLookup.findByPdIds(List.of(new ShipmentPlanId(pdId)));
                ShipmentPlanOrder pd = list.get(0);

                if (StringUtils.isEmpty(pd.getShipmentID()) ){
                    String errorMsg = "邮件发送失败，请重试，失败原因为：shipmentID 为空"  ;
                    // 更新邮件发送标记为失败
                    shipmentPlanOrderRepository.updateMailFlag(List.of(pdId), MailSendStatusEnum.FAILED.getCode(), errorMsg);
                    SpringUtil.getBean(ShipmentPlanOrderService.class).sendMailLog(v,  MailSendStatusEnum.FAILED.getCode(), errorMsg);
                    return;
                }

                // 查询 文件
                List<PurchaseOrderFile> files = orderFileLookup.findByOrderNo(pd.getPdNo(), PmsFileBusinessType.PD_ORDER.getCode(), PmsOrderFileType.PD_ORDER_MARK.getCode());
                if (CollectionUtils.isEmpty(files) ){
                    String errorMsg = "邮件发送失败，请重试，失败原因为：箱唛文件 为空"  ;
                    // 更新邮件发送标记为失败
                    shipmentPlanOrderRepository.updateMailFlag(List.of(pdId), MailSendStatusEnum.FAILED.getCode(), errorMsg);
                    SpringUtil.getBean(ShipmentPlanOrderService.class).sendMailLog(v,  MailSendStatusEnum.FAILED.getCode(), errorMsg);
                    return;
                }
                exeIds.add(pdId);
            });


            try {
                if (CollectionUtils.isEmpty(exeIds)){
                    return;
                }
                pendShipmentPlanEmailService.sendMail(k, exeIds);
                log.info("分批邮件发送完成，pdIds={}", exeIds);
                //更新邮件发送标记
                shipmentPlanOrderRepository.updateMailFlag(exeIds, MailSendStatusEnum.SUCCESS.getCode(), null);
                SpringUtil.getBean(ShipmentPlanOrderService.class).sendMailLog(v, MailSendStatusEnum.SUCCESS.getCode(), null);

            } catch (Exception e) {
                log.error("邮件发送失败，pdIds={}, error={}", exeIds, e.getMessage(), e);

                String errorMsg = "邮件发送失败，请重试，失败原因为：" + e.getMessage();
                // 更新邮件发送标记为失败
                shipmentPlanOrderRepository.updateMailFlag(exeIds, MailSendStatusEnum.FAILED.getCode(), errorMsg);
                SpringUtil.getBean(ShipmentPlanOrderService.class).sendMailLog(v,  MailSendStatusEnum.FAILED.getCode(), errorMsg);
            }
        });
    }
}
