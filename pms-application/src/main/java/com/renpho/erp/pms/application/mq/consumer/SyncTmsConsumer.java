package com.renpho.erp.pms.application.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.renpho.erp.pms.application.shipplan.service.ShipmentPlanOrderService;
import com.renpho.erp.stream.StreamCommonConstants;
import com.renpho.erp.tms.model.common.TmsStreamConstants;
import com.renpho.erp.tms.model.dto.TrCartonFilesMsgVo;
import com.renpho.erp.tms.model.dto.TrDeliveryTimeMsgVo;
import com.renpho.erp.tms.model.dto.TrNoMsgVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;

import java.util.function.Consumer;

/**
 * PMS-同步TMS信息
 * <AUTHOR>
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class SyncTmsConsumer {

	private final ShipmentPlanOrderService shipmentPlanOrderService;

	@Bean
	public Consumer<Message<String>> pmsSyncTmsConsumer() {
		return msg -> {
			try {
				Object tags = msg.getHeaders().get(StreamCommonConstants.HEADER_TAGS);
				log.info("接收到TMS-TR单,Tags:{},消息: {}", tags, msg);
				if (tags == null) {
					log.error("TMS消息处理失败 - Tags信息为空, 消息头: {}", msg.getHeaders());
					return;
				}

				String payload = msg.getPayload();
				switch (String.valueOf(tags)) {
					case TmsStreamConstants.CREATE_TR:
						TrNoMsgVo trNoMsgVo = JSON.parseObject(payload, TrNoMsgVo.class);
						shipmentPlanOrderService.syncTrInfo(trNoMsgVo.getTrId(), trNoMsgVo.getTrNo(), trNoMsgVo.getPdNo());
						break;
					case TmsStreamConstants.CARTON_FILE:
						TrCartonFilesMsgVo trCartonFilesMsgVo = JSON.parseObject(payload, TrCartonFilesMsgVo.class);
						String fileId = null;
						if (CollectionUtils.isNotEmpty(trCartonFilesMsgVo.getCartonLabelFileIds())){
							fileId = trCartonFilesMsgVo.getCartonLabelFileIds().get(0);
						}
						shipmentPlanOrderService.syncTrShipmentIdAndBoxMarkFile(trCartonFilesMsgVo.getPdNo(), trCartonFilesMsgVo.getShipmentId(), fileId);
						break;
					case TmsStreamConstants.HANDOVER:
						TrDeliveryTimeMsgVo trDeliveryTimeMsgVo = JSON.parseObject(payload, TrDeliveryTimeMsgVo.class);
						shipmentPlanOrderService.syncTrDeliveryStatus(trDeliveryTimeMsgVo.getDeliveryTime(), trDeliveryTimeMsgVo.getPdNo());
						break;
					default:
						log.warn("未知的TMS消息类型 - Tags: {}", tags);
				}
			} catch (Exception e) {
				log.error("PMS消费TMS消息失败,错误：{}", e.getMessage());
				throw new RuntimeException(e);
			}
		};
	}
}