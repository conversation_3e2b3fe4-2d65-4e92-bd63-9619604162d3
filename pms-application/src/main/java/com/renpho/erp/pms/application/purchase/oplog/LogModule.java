package com.renpho.erp.pms.application.purchase.oplog;


public class LogModule {

    public static final String PURCHASE_ORDER_LOG_MODULE = "PO";
    public static final String PURCHASE_REQUEST_ORDER_LOG_MODULE = "PURCHASE_REQUEST";
    public static final String SHIPMENT_PLAN_LOG_MODULE = "PD";
    public static final String PURCHASE_CHANGE_ORDER_LOG_MODULE = "PCO";
    public static final String SHIPMENT_PLAN_CHANGE_ORDER_LOG_MODULE = "PCD";

    public interface CommonDesc {

        String INSERT_OPERATOR = "Insert";
        String INSERT_DESC = "新增";

        String SUBMIT_OPERATOR = "Submit";
        String SUBMIT_DESC = "提交";

        String EDIT_OPERATOR = "Edit";
        String EDIT_DESC = "编辑";

        String VOID_OPERATOR = "Void";
        String VOID_DESC = "作废";

        String WITHDRAW_OPERATOR = "Withdraw";
        String WITHDRAW_DESC = "撤回";

        String REVIEW_OPERATOR = "Review";
        String REVIEW_DESC = "审核";

        String UPLOAD_OPERATOR = "Import";
        String UPLOAD_DESC = "导入";

        String DOWNLOAD_OPERATOR = "Export";
        String DOWNLOAD_DESC = "导出";

        String CANCEL_OPERATOR = "Cancel";
        String CANCEL_DESC = "取消";

        String CONFIRM_OPERATOR = "Confirm";
        String CONFIRM_DESC = "确认";

        String RECEIVE_OPERATOR = "ReceiveGoods";
        String RECEIVE_DESC = "收货";

        String SEND_MAIL = "Send_Mail";
        String SEND_MAIL_DESC = "发送邮件";

    }

    public interface PurchaseOrderOperatorLogModule {
        String EXPORT_PC_OPERATOR = "Export_PC";
        String EXPORT_PC_DESC = "导出采购合同";

        String UPLOAD_SC_OPERATOR = "Upload_SC";
        String UPLOAD_SC_DESC = "上传回签合同";

        String BULK_UPLOAD_SC_OPERATOR = "Bulk_Upload_SC";
        String BULK_UPLOAD_SC_DESC = "批量上传回签合同";

        String DOWNLOAD_SC_OPERATOR = "Download_SC";
        String DOWNLOAD_SC_DESC = "下载回签合同";

        String BULK_DOWNLOAD_SC_OPERATOR = "Bulk_Download_SC";
        String BULK_DOWNLOAD_SC_DESC = "批量下载回签合同";

        String IMPORT_CHG_DEL_DATE_OPERATOR = "Import_Chg_Del_Date";
        String IMPORT_CHG_DEL_DATE_DESC = "导入变更交期";

        String IMPORT_REV_DEL_DATE = "Import_Rev_Del_Date";
        String IMPORT_REV_DEL_DATE_DESC = "导入复核交期";


        String CHG_DEL_DATE = "Chg_Del_Date";
        String CHG_DEL_DATE_DESC = "变更交期";

        String REV_DEL_DATE = "Rev_Del_Date";
        String REV_DEL_DATE_DESC = "复核交期";

        String EXPORT_SN_CODE = "Export_Sn_Code";
        String EXPORT_SN_CODE_DESC = "导出SN码";

    }

    public interface ShipmentPlanOperatorLogModule {

        String EXPORT_BARCODE = "Exp_Barcode";
        String EXPORT_BARCODE_DESC = "导出条码";

        String MARK_FILE_SYNCED_OPERATOR = "Mark_File_Synced";
        String MARK_FILE_SYNCED_DESC = "标记文件同步";

        String EXPORT_SHIPPING_MARK = "Exp_Carton_Marking";
        String EXPORT_SHIPPING_MARK_DESC = "导出箱唛";

    }
}
