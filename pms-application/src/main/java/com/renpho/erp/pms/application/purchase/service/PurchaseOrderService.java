package com.renpho.erp.pms.application.purchase.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.esotericsoftware.minlog.Log;
import com.renpho.erp.bpm.api.dto.KeyAndValueDto;
import com.renpho.erp.bpm.client.process.vo.ProcessInstanceUserVo;
import com.renpho.erp.data.permission.service.UserDataPermissionService;
import com.renpho.erp.ftm.client.response.FileDetailResponse;
import com.renpho.erp.log.repository.annotation.LogRecord;
import com.renpho.erp.log.repository.support.LogRecordContextHolder;
import com.renpho.erp.mdm.client.rate.vo.LatestFluctuateExchangeRateResponse;
import com.renpho.erp.pms.Infrastructure.PurchaseOrderConstants;
import com.renpho.erp.pms.Infrastructure.feign.RemoteFileFeign;
import com.renpho.erp.pms.Infrastructure.feign.product.ProductLookup;
import com.renpho.erp.pms.Infrastructure.feign.qms.QualityApplyOrderLookup;
import com.renpho.erp.pms.Infrastructure.feign.qms.RemoteQualityTaskFeign;
import com.renpho.erp.pms.Infrastructure.feign.rate.RateLookup;
import com.renpho.erp.pms.Infrastructure.feign.saleschannel.SalesChannelLookup;
import com.renpho.erp.pms.Infrastructure.feign.store.StoreLookup;
import com.renpho.erp.pms.Infrastructure.feign.supplier.PurchaseSupplierLookup;
import com.renpho.erp.pms.Infrastructure.feign.supplier.RemoteProductBusinessManagerFeign;
import com.renpho.erp.pms.Infrastructure.feign.tms.RemoteTransportRequestFeign;
import com.renpho.erp.pms.Infrastructure.feign.user.OperatorLookup;
import com.renpho.erp.pms.Infrastructure.feign.user.RemoteUserDetailsFeign;
import com.renpho.erp.pms.Infrastructure.feign.warehouse.WarehouseLookup;
import com.renpho.erp.pms.Infrastructure.form.PurchaseOrderForm;
import com.renpho.erp.pms.Infrastructure.persistence.purchase.po.converter.PurchaseOrderPriceConverter;
import com.renpho.erp.pms.Infrastructure.persistence.purchase.po.converter.PurchaseOrderProductConverter;
import com.renpho.erp.pms.Infrastructure.persistence.purchase.po.converter.PurchaseOrderSupplierConverter;
import com.renpho.erp.pms.Infrastructure.persistence.shipplanchangeorder.repo.ShipmentPlanChangeOrderRepo;
import com.renpho.erp.pms.Infrastructure.processinstance.ProcessInstanceService;
import com.renpho.erp.pms.Infrastructure.util.common.CustomBarcodePDFUtil;
import com.renpho.erp.pms.Infrastructure.stream.purchaseorder.PurchaseOrderProducer;
import com.renpho.erp.pms.Infrastructure.stream.purchaseorder.request.PurchaseOrderRequestConverter;
import com.renpho.erp.pms.Infrastructure.stream.shipmentplan.ShipmentPlanOrderProducer;
import com.renpho.erp.pms.Infrastructure.util.common.CustomBarcodePDFUtil;
import com.renpho.erp.pms.application.purchase.oplog.LogModule;
import com.renpho.erp.pms.application.synctask.core.SyncTaskManager;
import com.renpho.erp.pms.application.synctask.model.enums.SyncNodeEnum;
import com.renpho.erp.pms.domain.commom.Operator;
import com.renpho.erp.pms.domain.commom.OperatorId;
import com.renpho.erp.pms.domain.product.Product;
import com.renpho.erp.pms.domain.product.ProductId;
import com.renpho.erp.pms.domain.productprice.ProductPrice;
import com.renpho.erp.pms.domain.productprice.ProductPriceItem;
import com.renpho.erp.pms.domain.purchasechangeorder.PurchaseChangeOrderRepository;
import com.renpho.erp.pms.domain.purchaseorder.*;
import com.renpho.erp.pms.domain.purchaseorder.dto.PurchaseOrderExportDto;
import com.renpho.erp.pms.domain.purchaseorder.enums.PurchaseChangeTypeEnum;
import com.renpho.erp.pms.domain.purchaseorder.enums.PurchaseNoticeStatusEnum;
import com.renpho.erp.pms.domain.purchaseorder.enums.PurchaseOrderType;
import com.renpho.erp.pms.domain.purchaserequest.*;
import com.renpho.erp.pms.domain.saleschannel.SalesChannel;
import com.renpho.erp.pms.domain.saleschannel.SalesChannelId;
import com.renpho.erp.pms.domain.shipplan.*;
import com.renpho.erp.pms.domain.shipplanchangeorder.model.ShipmentPlanChangeOutDetail;
import com.renpho.erp.pms.domain.store.Store;
import com.renpho.erp.pms.domain.store.StoreId;
import com.renpho.erp.pms.domain.warehouse.Warehouse;
import com.renpho.erp.pms.domain.warehouse.WarehouseId;
import com.renpho.erp.pms.exception.BizErrorCode;
import com.renpho.erp.pms.exception.BusinessException;
import com.renpho.erp.pms.model.common.model.enums.PmsFileBusinessType;
import com.renpho.erp.pms.model.purchaseorder.enums.PurchaseOrderReviewStatusEnum;
import com.renpho.erp.pms.model.purchaseorder.enums.PurchaseOrderStatusEnum;
import com.renpho.erp.pms.model.purchaseorder.model.PurchaseOrderDTO;
import com.renpho.erp.pms.model.purchaseorder.model.PurchaseOrderSupplierInfoDTO;
import com.renpho.erp.pms.model.shipmentplan.enums.ShipmentPlanOrderStatusEnum;
import com.renpho.erp.pms.model.shipmentplan.enums.SupplierTradeTermsEnum;
import com.renpho.erp.pms.model.shipmentplan.model.ShipmentPlanOrderDTO;
import com.renpho.erp.pms.model.shipmentplan.model.ShipmentPlanOrderItemDTO;
import com.renpho.erp.security.util.SecurityUtils;
import com.renpho.erp.smc.client.dto.OumUserInfoRes;
import com.renpho.erp.srm.client.vo.ProductBusinessManagerResponse;
import com.renpho.erp.srm.client.vo.PurchaseSupplierDetailsResp;
import com.renpho.erp.tms.client.transportrequest.response.TransportRequestClientVo;
import com.renpho.karma.dto.PageQuery;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.exception.ApiException;
import com.renpho.karma.exception.ErrorCodeException;
import com.renpho.pms.client.purchase.response.PurchaseOrderVo;
import com.renpho.qms.client.apply.vo.ApplyOrderStatusCountVo;
import com.renpho.pms.client.purchase.response.PurchaseOrderVo;
import com.renpho.qms.client.apply.vo.ApplyOrderStatusCountVo;
import com.renpho.erp.pms.model.purchaseorder.enums.PurchaseOrderReviewStatusEnum;
import com.renpho.erp.pms.model.purchaseorder.enums.PurchaseOrderStatusEnum;
import com.renpho.erp.pms.model.shipmentplan.enums.ShipmentPlanOrderStatusEnum;
import com.renpho.erp.pms.model.shipmentplan.enums.SupplierTradeTermsEnum;
import com.renpho.qms.client.apply.vo.QualityNumCountVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 采购单服务接口
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PurchaseOrderService {

    private final PurchaseOrderLookup purchaseOrderLookup;
    private final PurchaseOrderRepository purchaseOrderRepository;
    private final PurchaseOrderItemLookup purchaseOrderItemLookup;
    private final PurchaseOrderItemRepository purchaseOrderItemRepository;
    private final PurchaseOrderProductInfoLookup purchaseOrderProductInfoLookup;
    private final PurchaseOrderProductInfoRepository purchaseOrderProductInfoRepository;
    private final PurchaseOrderSupplierInfoLookup purchaseOrderSupplierInfoLookup;
    private final PurchaseOrderSupplierInfoRepository purchaseOrderSupplierInfoRepository;
    private final PurchaseOrderPriceInfoLookup purchaseOrderPriceInfoLookup;
    private final PurchaseOrderPriceInfoRepository purchaseOrderPriceInfoRepository;
    private final PurchaseOrderDeliveryDateLogRepository purchaseOrderDeliveryDateLogRepository;
    private final PurchaseOrderDeliveryDateLogLookup purchaseOrderDeliveryDateLogLookup;
    private final ProductLookup productLookup;
    private final PurchaseSupplierLookup purchaseSupplierLookup;
    private final PurchaseRequestItemLookup purchaseRequestItemLookup;
    private final PurchaseRequestLookup purchaseRequestLookup;

    private final PurchaseOrderNoticeRepository purchaseOrderNoticeRepository;
    private final PurchaseOrderNoticeLookup purchaseOrderNoticeLookup;

    private final PurchaseOrderFileLookup purchaseOrderFileLookup;

    private final RemoteFileFeign remoteFileFeign;
    private final RemoteUserDetailsFeign remoteUserDetailsFeign;
    private final RateLookup rateLookup;

    private final PurchaseOrderSnService purchaseOrderSnService;
    private final ProcessInstanceService processInstanceService;
    private final UserDataPermissionService userDataPermissionService;
    private final RemoteProductBusinessManagerFeign remoteProductBusinessManagerFeign;
    private final RemoteQualityTaskFeign remoteQualityTaskFeign;
    private final OperatorLookup operatorLookup;
    private final SalesChannelLookup salesChannelLookup;
    private final StoreLookup storeLookup;

    private final PurchaseOrderSupplierConverter purchaseOrderSupplierConverter;
    private final PurchaseOrderProductConverter purchaseOrderProductConverter;
    private final PurchaseOrderPriceConverter purchaseOrderPriceConverter;


    private final PendShipmentPlanLookup pendShipmentPlanLookup;
    private final PendShipmentPlanRepository pendShipmentPlanRepository;

    private final ShipmentPlanOrderLookup shipmentPlanOrderLookup;
    private final ShipmentPlanOrderItemLookup shipmentPlanOrderItemLookup;
    private final WarehouseLookup warehouseLookup;
    private final ShipmentPlanOrderItemRepository shipmentPlanOrderItemRepository;

    private final QualityApplyOrderLookup qualityApplyOrderLookup;
    private final PurchaseOrderRequestConverter purchaseOrderRequestConverter;
    private final PurchaseOrderProducer purchaseOrderProducer;
    private final ShipmentPlanOrderProducer shipmentPlanOrderProducer;

    private final PurchaseChangeOrderRepository purchaseChangeOrderRepository;
    private final ShipmentPlanChangeOrderRepo shipmentPlanChangeOrderRepo;
    private final SyncTaskManager syncTaskManager;
    private final RemoteTransportRequestFeign remoteTransportRequestFeign;

    /**
     * 分页查询采购单，待后续修改
     *
     * @param condition 查询条件
     * @param pageQuery 分页参数
     */
    public Paging<PurchaseOrder> page(PurchaseOrderQuery condition, PageQuery pageQuery) {
        // 初始化当前用户ID
        Integer currentUserId = Optional.ofNullable(SecurityUtils.getUserId()).orElse(0);
        Map<String, ProcessInstanceUserVo> processInfoMap = new HashMap<>();
        // 处理当前审核标志
        if (handleCurrentReviewerFlag(processInfoMap, condition, currentUserId)) {
            return Paging.of();
        }
        Set<Integer> userIdSet = userDataPermissionService.getDataPermission();
        condition.setUserIdSet(userIdSet);
        // 查询分页数据
        Paging<PurchaseOrder> paging = purchaseOrderLookup.findPage(condition, pageQuery);

        if (CollectionUtil.isNotEmpty(paging.getRecords())) {
            // 提取采购单ID和流程实例ID
            List<PurchaseOrderId> poIds = paging.getRecords().stream()
                    .map(PurchaseOrder::getId)
                    .collect(Collectors.toList());
            List<String> processInstanceIds = paging.getRecords().stream()
                    .map(PurchaseOrder::getProcessInstanceId)
                    .collect(Collectors.toList());

            List<String> poNos = paging.getRecords().stream()
                    .map(PurchaseOrder::getPoNo)
                    .collect(Collectors.toList());

            // 查询采购单明细
            Map<PurchaseOrderId, List<PurchaseOrderItem>> itemMap = purchaseOrderItemLookup.findAllByPoIds(poIds);
            // 查询产品明细
            Map<PurchaseOrderId, PurchaseOrderProductInfo> productInfoMap = purchaseOrderProductInfoLookup.findAllByPoIds(poIds);
            //红点处理
            Map<PurchaseOrderId, List<PurchaseOrderNotice>> noticeMap = purchaseOrderNoticeLookup.findAllByPoIds(poIds);
            //文件处理
            Map<PurchaseOrderId, List<PurchaseOrderFile>> fileMap = purchaseOrderFileLookup.findAllByPoIds(poIds, PmsFileBusinessType.PO_ORDER.getCode());
            // 查询价目信息
            Map<PurchaseOrderId, PurchaseOrderPriceInfo> priceInfoMap = purchaseOrderPriceInfoLookup.findByPoIds(poIds);

            // 查询待发货计划
            Map<String, List<PendShipmentPlan>> pendShipmentPlanMap = pendShipmentPlanLookup.findByPoIdList(poIds);

            // 提取pendId
            List<Integer> pendIds = pendShipmentPlanMap.values().stream()
                    .flatMap(List::stream)
                    .map(PendShipmentPlan::getId)
                    .map(PendShipmentPlanId::id)
                    .collect(Collectors.toList());
            // 查询ShipmentPlanOrderItem
            Map<PendShipmentPlanId, List<ShipmentPlanOrderItem>> shipmentPlanItemMap = shipmentPlanOrderItemLookup.findByPendIdList(pendIds)
                    .stream().collect(Collectors.groupingBy(shipmentPlanOrderItem->new PendShipmentPlanId(shipmentPlanOrderItem.getPendId())));

            // 查询质量申请单
            Map<String, List<ApplyOrderStatusCountVo>> applyOrderStatusCountMap = qualityApplyOrderLookup.getStatusCountByPoList(poNos);

            // 查询QMS-QC质检结果
            Map<String, List<QualityNumCountVo>> taskNumMap = remoteQualityTaskFeign.getTaskNumMapByPoList(poNos);

            //查询是否存在PCO变更
            Map<PurchaseOrderId, Integer> existPcoMap = purchaseChangeOrderRepository.existPcoByPoIds(poIds);
            //查询是否存在PCD变更
            Map<PurchaseOrderId, Integer> existPcdMap = shipmentPlanChangeOrderRepo.existPcdByPoIds(poIds);

            // 提取用户ID列表
            Set<Integer> userIdList = extractUserIds(paging.getRecords());

            //设置文件扩展名
            if (CollectionUtils.isNotEmpty(fileMap.values())) {
                List<String> fileIdList = fileMap.values().stream().flatMap(Collection::stream).map(PurchaseOrderFile::getFileId).collect(Collectors.toList());
                Map<String, FileDetailResponse> fileInfoMap = remoteFileFeign.getFileResponseMap(fileIdList);
                fileMap.forEach((poId, fileList) -> fileList.forEach(file -> {
                    if (fileInfoMap.containsKey(file.getFileId())) {
                        file.setExt(fileInfoMap.get(file.getFileId()).getExt());
                    }
                }));
            }

            // 更新流程信息映射
            setProcessInfoMap(processInfoMap, userIdList, condition, processInstanceIds);
            // 查询用户信息和供应商信息
            Map<Integer, OumUserInfoRes> userMap = remoteUserDetailsFeign.getUserMapByIdList(new ArrayList<>(userIdList));
            Map<PurchaseOrderId, PurchaseOrderSupplierInfo> supplierInfoMap = purchaseOrderSupplierInfoLookup.findByPoIds(poIds);
            // 补充采购单信息
            enrichPurchaseOrders(paging.getRecords(), itemMap, supplierInfoMap, processInfoMap,
                    productInfoMap, noticeMap, userMap, fileMap, priceInfoMap, pendShipmentPlanMap, applyOrderStatusCountMap,
                    taskNumMap, shipmentPlanItemMap, existPcoMap, existPcdMap);
        }

        return paging;
    }

    /**
     * 查询采购单，待后续修改
     *
     * @param condition 查询条件
     * @param pageQuery 分页参数
     */
    public List<PurchaseOrderExportDto> exportPurchaseOrder(PurchaseOrderQuery condition, PageQuery pageQuery) {

        // 初始化当前用户ID
        Integer currentUserId = Optional.ofNullable(SecurityUtils.getUserId()).orElse(0);

        Map<String, ProcessInstanceUserVo> processInfoMap = new HashMap<>();
        // 处理当前审核标志
        if (handleCurrentReviewerFlag(processInfoMap, condition, currentUserId)) {
            return List.of();
        }
        Set<Integer> userIdSet = userDataPermissionService.getDataPermission();
        condition.setUserIdSet(userIdSet);

        List<PurchaseOrderExportDto> purchaseOrderExportDtoList = new ArrayList<>();
        // 查询分页数据
        Paging<PurchaseOrder> paging = purchaseOrderLookup.findPage(condition, pageQuery);

        while (CollectionUtil.isNotEmpty(paging.getRecords())) {
            // 提取采购单ID和流程实例ID
            List<PurchaseOrderId> poIds = paging.getRecords().stream()
                    .map(PurchaseOrder::getId)
                    .collect(Collectors.toList());
            Map<PurchaseOrderId, String> receiptDateMap = shipmentPlanOrderLookup.findReceiptDateByPoIds(poIds);
            // 查询采购单明细
            Map<PurchaseOrderId, List<PurchaseOrderItem>> itemMap = purchaseOrderItemLookup.findAllByPoIds(poIds);
            // 查询价目信息
            Map<PurchaseOrderId, PurchaseOrderPriceInfo> priceInfoMap = purchaseOrderPriceInfoLookup.findByPoIds(poIds);
            // 供应商名称
            Map<PurchaseOrderId, PurchaseOrderSupplierInfo> supplierInfoMap = purchaseOrderSupplierInfoLookup.findByPoIds(poIds);
            // 提取用户ID列表
            Set<Integer> userIdList = extractUserIds(paging.getRecords());
            supplierInfoMap.values().forEach(purchaseOrderSupplierInfo -> userIdList.add(purchaseOrderSupplierInfo.getSourcingPicUser().getId().id()));
            // 查询用户信息和供应商信息
            Map<Integer, OumUserInfoRes> userMap = remoteUserDetailsFeign.getUserMapByIdList(new ArrayList<>(userIdList));
            // 补充采购单信息
            buildPurchaseOrderExportDto(purchaseOrderExportDtoList, paging.getRecords(), itemMap, supplierInfoMap,
                    priceInfoMap, userMap, receiptDateMap);
            pageQuery.setPageIndex(pageQuery.getPageIndex() + 1);
            paging = purchaseOrderLookup.findPage(condition, pageQuery);
        }
        return purchaseOrderExportDtoList;
    }

    /**
     * 构建采购订单导出DTO列表
     * 本方法通过遍历采购订单记录，并为每个记录创建一个对应的PurchaseOrderExportDto实例，
     * 填充所需信息，最终将这些实例添加到导出DTO列表中
     *
     * @param purchaseOrderExportDtoList 用于存储构建好的采购订单导出DTO的列表
     * @param records                    采购订单记录列表，用于构建DTO
     * @param itemMap                    映射采购订单ID到其项列表的字典
     * @param supplierInfoMap            映射采购订单ID到供应商信息的字典
     * @param priceInfoMap               映射采购订单ID到价格信息的字典
     * @param userMap                    映射用户ID到用户信息的字典
     * @param receiptDateMap
     */
    private void buildPurchaseOrderExportDto(List<PurchaseOrderExportDto> purchaseOrderExportDtoList,
                                             List<PurchaseOrder> records,
                                             Map<PurchaseOrderId, List<PurchaseOrderItem>> itemMap,
                                             Map<PurchaseOrderId, PurchaseOrderSupplierInfo> supplierInfoMap,
                                             Map<PurchaseOrderId, PurchaseOrderPriceInfo> priceInfoMap,
                                             Map<Integer, OumUserInfoRes> userMap,
                                             Map<PurchaseOrderId, String> receiptDateMap) {

        // 遍历每个采购订单记录，构建对应的导出DTO
        records.forEach(purchaseOrder -> {
            try {
                // 创建一个新的采购订单导出DTO实例
                PurchaseOrderExportDto exportDto = new PurchaseOrderExportDto();
                // 填充基本字段
                populateBasicFields(exportDto, purchaseOrder);
                // 填充用户相关字段
                populateUserFields(exportDto, purchaseOrder, userMap);
                // 填充单据类型相关字段
                populateItemFields(exportDto, purchaseOrder.getId(), itemMap);
                // 填充供应商相关字段
                populateSupplierFields(exportDto, purchaseOrder.getId(), supplierInfoMap, userMap);
                // 填充价格相关字段
                populatePriceFields(exportDto, purchaseOrder.getId(), priceInfoMap, purchaseOrder);
                // 填充时间相关字段
                populateTimeFields(exportDto, purchaseOrder, receiptDateMap);
                // 将构建好的DTO添加到列表中
                purchaseOrderExportDtoList.add(exportDto);
            } catch (Exception e) {
                // 记录异常信息，避免程序崩溃
                Log.error("Error processing purchase order: " + e.getMessage());
            }
        });
    }


    /**
     * 填充导出DTO的基本字段
     * 此方法将从采购订单对象中复制相关信息到导出DTO对象中，以便于后续的数据导出操作
     * 它处理的字段包括采购订单号、SKU、序列号范围、采购数量、正常和备用数量、以及各种日期和备注信息
     *
     * @param exportDto     用于数据导出的DTO对象，其基本字段将被填充
     * @param purchaseOrder 采购订单对象，作为数据源，用于填充DTO的字段
     */
    private void populateBasicFields(PurchaseOrderExportDto exportDto, PurchaseOrder purchaseOrder) {
        // 设置采购订单号
        exportDto.setPoNo(purchaseOrder.getPoNo());
        // 设置采购SKU
        exportDto.setPsku(purchaseOrder.getPsku());
        // 设置序列号范围，合并开始和结束序列号
        exportDto.setSn(purchaseOrder.getSnStart() + "~" + purchaseOrder.getSnEnd());
        // 设置采购数量，转换为字符串
        exportDto.setPoQty(String.valueOf(purchaseOrder.getPoQty()));
        // 设置正常数量，转换为字符串
        exportDto.setNormalQty(String.valueOf(purchaseOrder.getNormalQty()));
        // 设置备用数量，转换为字符串
        exportDto.setSpareQty(String.valueOf(purchaseOrder.getSpareQty()));
        // 设置预期交货日期，如果日期不为空则转换为字符串，否则设置为空字符串
        exportDto.setExpectedDeliveryDate(purchaseOrder.getExpectedDeliveryDate() != null ? purchaseOrder.getExpectedDeliveryDate().toString() : "");
        // 设置计划备注
        exportDto.setPlanRemark(purchaseOrder.getPlanRemark());
        // 设置确认的交货日期，处理方式与预期交货日期相同
        exportDto.setConfirmedDeliveryDate(purchaseOrder.getConfirmedDeliveryDate() != null ? purchaseOrder.getConfirmedDeliveryDate().toString() : "");
        // 设置采购备注
        exportDto.setPurchaseRemark(purchaseOrder.getPurchaseRemark());
        // 设置更改后的交货日期，处理方式与预期交货日期相同
        exportDto.setChangedDeliveryDate(purchaseOrder.getChangedDeliveryDate() != null ? purchaseOrder.getChangedDeliveryDate().toString() : "");
        // 设置修改备注
        exportDto.setModifyRemark(purchaseOrder.getModifyRemark());
        // 设置检查后的交货日期，处理方式与预期交货日期相同
        exportDto.setCheckedDeliveryDate(purchaseOrder.getCheckedDeliveryDate() != null ? purchaseOrder.getCheckedDeliveryDate().toString() : "");
        // 设置审核备注
        exportDto.setReviewRemark(purchaseOrder.getReviewRemark());
    }


    /**
     * 填充用户相关字段
     */
    private void populateUserFields(PurchaseOrderExportDto exportDto, PurchaseOrder purchaseOrder, Map<Integer, OumUserInfoRes> userMap) {
        Integer planStaffId = getSafeUserId(purchaseOrder.getPlanStaff());
        if (planStaffId != null && userMap.containsKey(planStaffId)) {
            OumUserInfoRes userInfo = userMap.get(planStaffId);
            exportDto.setPlanStaff(userInfo.getName() + "(" + userInfo.getCode() + ")");
        }

        Integer buyerId = getSafeUserId(purchaseOrder.getBuyer());
        if (buyerId != null && userMap.containsKey(buyerId)) {
            OumUserInfoRes userInfo = userMap.get(buyerId);
            exportDto.setBuyer(userInfo.getName() + "(" + userInfo.getCode() + ")");
        }
    }

    /**
     * 填充项相关字段
     */
    private void populateItemFields(PurchaseOrderExportDto exportDto, PurchaseOrderId poId, Map<PurchaseOrderId, List<PurchaseOrderItem>> itemMap) {
        List<PurchaseOrderItem> items = itemMap.getOrDefault(poId, Collections.emptyList());
        if (!items.isEmpty()) {
            Set<String> poTypes = new HashSet<>();
            for (PurchaseOrderItem item : items) {
                if (Objects.nonNull(item.getPoType()) && Objects.nonNull(PurchaseOrderType.getEnums(item.getPoType()))) {
                    poTypes.add(PurchaseOrderType.getEnums(item.getPoType()).getDescCn());
                }
            }
            exportDto.setPoType(String.join(",", poTypes));
        }
    }

    /**
     * 填充供应商相关字段
     */
    private void populateSupplierFields(PurchaseOrderExportDto exportDto, PurchaseOrderId poId, Map<PurchaseOrderId, PurchaseOrderSupplierInfo> supplierInfoMap, Map<Integer, OumUserInfoRes> userMap) {
        PurchaseOrderSupplierInfo supplierInfo = supplierInfoMap.get(poId);
        if (supplierInfo != null) {
            exportDto.setSupplierCode(supplierInfo.getSupplierCode());
            exportDto.setSupplierFullName(supplierInfo.getSupplierFullName());

            Integer sourcingPicUserId = getSafeUserId(supplierInfo.getSourcingPicUser());
            if (sourcingPicUserId != null && userMap.containsKey(sourcingPicUserId)) {
                OumUserInfoRes userInfo = userMap.get(sourcingPicUserId);
                exportDto.setSourcingPicUser(userInfo.getName() + "(" + userInfo.getCode() + ")");
            }

            exportDto.setSupplierTradeTerms(SupplierTradeTermsEnum.enumOf(supplierInfo.getSupplierTradeTerms().getValue()).getName());
            exportDto.setSupplierDeliveryLocation(supplierInfo.getSupplierDeliveryLocation());
            exportDto.setSettlementDesc(supplierInfo.getSettlementDesc());
            exportDto.setAdvancePaymentRatio(String.valueOf(supplierInfo.getAdvancePaymentRatio()));
        }
    }

    /**
     * 处理价格信息
     */
    private void populatePriceFields(PurchaseOrderExportDto exportDto, PurchaseOrderId poId, Map<PurchaseOrderId, PurchaseOrderPriceInfo> priceInfoMap, PurchaseOrder purchaseOrder) {
        PurchaseOrderPriceInfo priceInfo = priceInfoMap.get(poId);
        if (priceInfo != null) {
            exportDto.setPriceNumber(priceInfo.getPriceNumber());
            exportDto.setCurrency(priceInfo.getCurrency());
            exportDto.setExchangeRate(String.valueOf(priceInfo.getExchangeRate()));

            BigDecimal inclusiveTaxPrice = priceInfo.getInclusiveTaxPrice();
            if (inclusiveTaxPrice != null && purchaseOrder.getNormalQty() != null) {
                exportDto.setInclusiveTaxPrice(String.valueOf(inclusiveTaxPrice));
                exportDto.setPurchaseAmount(String.valueOf(inclusiveTaxPrice.multiply(new BigDecimal(purchaseOrder.getNormalQty())).setScale(2, RoundingMode.HALF_UP)));
            }
        }
    }

    /**
     * 处理时间字段
     */
    private void populateTimeFields(PurchaseOrderExportDto exportDto, PurchaseOrder purchaseOrder, Map<PurchaseOrderId, String> receiptDateMap) {
        exportDto.setCreatedTime(formatDateTime(purchaseOrder.getCreated().getOperateTime()));
        exportDto.setSubmitTime(formatDateTime(purchaseOrder.getSubmitTime()));
        exportDto.setReviewTime(formatDateTime(purchaseOrder.getReviewTime()));
        exportDto.setReceivedTime(receiptDateMap.getOrDefault(purchaseOrder.getId(), ""));
    }


    private String formatDateTime(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "";
    }

    private Integer getSafeUserId(Operator userId) {
        return userId != null && userId.getId() != null ? userId.getId().id() : null;
    }

    /**
     * 处理当前审核人标识
     */
    private boolean handleCurrentReviewerFlag(Map<String, ProcessInstanceUserVo> processInfoMap, PurchaseOrderQuery condition, Integer currentUserId) {
        if (Objects.nonNull(condition.getCurrentReviewerFlag())) {
            List<String> processInstanceIds = purchaseOrderLookup.findAllProcessInstanceIds();
            if (CollectionUtils.isNotEmpty(processInstanceIds)) {
                condition.setProcessInstanceIds(processInstanceIds);
            }
            if (Objects.equals(condition.getCurrentReviewerFlag(), PurchaseOrderConstants.REVIEWER_IS_SELF)) {
                processInfoMap.putAll(filterProcessInstanceIdsByCurrentUser(condition, currentUserId));
            } else if (Objects.equals(condition.getCurrentReviewerFlag(), PurchaseOrderConstants.REVIEWER_IS_NOT_SELF)) {
                processInfoMap.putAll(filterProcessInstanceIdsByNonCurrentUser(condition, currentUserId));
            }
            if (CollectionUtils.isEmpty(condition.getProcessInstanceIds())) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 过滤当前用户的流程实例ID
     */
    private Map<String, ProcessInstanceUserVo> filterProcessInstanceIdsByCurrentUser(PurchaseOrderQuery condition, Integer currentUserId) {
        Map<String, ProcessInstanceUserVo> processInfoMap = processInstanceService.getOrderProcessInfo(condition.getProcessInstanceIds());
        condition.setProcessInstanceIds(processInfoMap.values().stream()
                .filter(vo -> vo.getCurrentApprovalUserIdSet().contains(currentUserId))
                .map(ProcessInstanceUserVo::getProcessInstanceId)
                .toList());
        return processInfoMap;
    }

    /**
     * 过滤非当前用户的流程实例ID
     */
    private Map<String, ProcessInstanceUserVo> filterProcessInstanceIdsByNonCurrentUser(PurchaseOrderQuery condition, Integer currentUserId) {
        Map<String, ProcessInstanceUserVo> processInfoMap = processInstanceService.getOrderProcessInfo(condition.getProcessInstanceIds());
        condition.setProcessInstanceIds(processInfoMap.values().stream()
                .filter(vo -> !vo.getCurrentApprovalUserIdSet().contains(currentUserId))
                .map(ProcessInstanceUserVo::getProcessInstanceId)
                .toList());
        return processInfoMap;
    }


    // 提取用户ID列表
    private Set<Integer> extractUserIds(List<PurchaseOrder> records) {
        Set<Integer> userIdList = new TreeSet<>();
        for (PurchaseOrder order : records) {
            userIdList.add(order.getBuyer().getId().id());
            userIdList.add(order.getPlanStaff().getId().id());
        }
        return userIdList;
    }


    /**
     * 设置流程信息
     */
    private void setProcessInfoMap(Map<String, ProcessInstanceUserVo> processInfoMap, Set<Integer> userIdList, PurchaseOrderQuery condition, List<String> processInstanceIds) {
//        if (CollectionUtils.isEmpty(condition.getProcessInstanceIds()) && CollectionUtils.isNotEmpty(processInstanceIds)) {
//            processInfoMap.putAll(processInstanceService.getOrderProcessInfo(processInstanceIds));
//        } else {
//            processInfoMap.keySet().retainAll(condition.getProcessInstanceIds());
//        }
//        if (CollectionUtils.isNotEmpty(processInfoMap.values())) {
//            processInfoMap.values().forEach(vo -> {
//                if (CollectionUtils.isNotEmpty(vo.getCurrentApprovalUserIdSet())) {
//                    userIdList.addAll(vo.getCurrentApprovalUserIdSet());
//                }
//            });
//        }
    }

    /**
     * 填充采购订单信息
     */
    private void enrichPurchaseOrders(List<PurchaseOrder> records, Map<PurchaseOrderId, List<PurchaseOrderItem>> itemMap,
                                      Map<PurchaseOrderId, PurchaseOrderSupplierInfo> supplierInfoMap,
                                      Map<String, ProcessInstanceUserVo> processInstanceUserVoMap,
                                      Map<PurchaseOrderId, PurchaseOrderProductInfo> productInfoMap,
                                      Map<PurchaseOrderId, List<PurchaseOrderNotice>> noticeMap,
                                      Map<Integer, OumUserInfoRes> userMap,
                                      Map<PurchaseOrderId, List<PurchaseOrderFile>> fileMap,
                                      Map<PurchaseOrderId, PurchaseOrderPriceInfo> priceInfoMap,
                                      Map<String, List<PendShipmentPlan>> pendShipmentPlanMap,
                                      Map<String, List<ApplyOrderStatusCountVo>> applyOrderStatusCountMap,
                                      Map<String, List<QualityNumCountVo>> taskNumMap,
                                      Map<PendShipmentPlanId, List<ShipmentPlanOrderItem>> shipmentPlanItemMap,
                                      Map<PurchaseOrderId, Integer> existPcoMap,
                                      Map<PurchaseOrderId, Integer> existPcdMap) {
        for (PurchaseOrder order : records) {
            PurchaseOrderId poId = order.getId();
            // 处理采购单明细
            List<PurchaseOrderItem> items = itemMap.getOrDefault(poId, Collections.emptyList());
            if (!items.isEmpty()) {
                order.setPoTypes(items.stream().map(PurchaseOrderItem::getPoType).distinct().collect(Collectors.toList()));
                items.forEach(item -> {
                    if (userMap != null && userMap.containsKey(item.getOperationStaffId().getId().id())) {
                        item.getOperationStaffId().setOperatorCode(userMap.get(item.getOperationStaffId().getId().id()).getCode());
                        item.getOperationStaffId().setOperatorName(userMap.get(item.getOperationStaffId().getId().id()).getName());
                    }
                });
            }

            // 处理供应商信息
            PurchaseOrderSupplierInfo supplierInfo = supplierInfoMap.get(poId);
            if (supplierInfo != null) {
                order.setContractTemplateId(supplierInfo.getContractTemplateId());
                order.setContractTemplateName(supplierInfo.getContractTemplateName());
                order.setSupplierCode(supplierInfo.getSupplierCode());
                order.setSupplierShortName(supplierInfo.getSupplierShortName());
            }

            // 处理产品信息
            PurchaseOrderProductInfo productInfo = productInfoMap.get(poId);
            if (productInfo != null) {
                order.setPicture(productInfo.getUrl());
                order.setBrandName(productInfo.getBrandName());
                order.setProductName(productInfo.getChineseName());
                order.setModelName(productInfo.getModelName());
            }

            // 处理红点信息
            List<PurchaseOrderNotice> notices = noticeMap.getOrDefault(poId, Collections.emptyList());
            setNoticeInfo(order, notices);

            Optional.ofNullable(pendShipmentPlanMap.get(order.getPoNo()))
                    .ifPresent(pendShipmentPlans -> {
                        // 计算已收货数量
                        int receivedSum = pendShipmentPlans.stream()
                                .map(PendShipmentPlan::getId)
                                .map(shipmentPlanItemMap::get)
                                .filter(Objects::nonNull)
                                .flatMap(Collection::stream)
                                .mapToInt(item->Optional.ofNullable(item.getReceiveQty()).orElse(0))
                                .sum();
                        order.setReceivedCount(receivedSum);

                        // 计算待收货数量
                        order.setUnReceivedCount(order.getPoQty() - receivedSum);

                        // 计算pendQty和pdQty
                        int pendQtySum = pendShipmentPlans.stream()
                                .mapToInt(PendShipmentPlan::getPendQty)
                                .sum();
                        order.setPendQty(pendQtySum);

                        order.setPdQty(pendShipmentPlans.stream()
                                .mapToInt(e -> e.getPurchaseRequestItem().getQuantity() - e.getPendQty())
                                .sum());
                    });

            if (applyOrderStatusCountMap.containsKey(order.getPoNo())) {
                order.setApplyCount(applyOrderStatusCountMap.get(order.getPoNo()).stream().mapToInt(ApplyOrderStatusCountVo::getApplyCount).sum());
                order.setUnApplyCount(applyOrderStatusCountMap.get(order.getPoNo()).stream().mapToInt(ApplyOrderStatusCountVo::getUnApplyCount).sum());
            }

            // QMS-QC质检结果
            Optional.ofNullable(taskNumMap)
                    .map(map -> map.get(order.getPoNo()))
                    .ifPresent(qualityCounts -> {
                        order.setQualifiedCount(qualityCounts.stream().mapToInt(QualityNumCountVo::getQualifiedQuantity).sum());
                        order.setUnQualifiedCount(qualityCounts.stream().mapToInt(QualityNumCountVo::getUnQualifiedQuantity).sum());
                        order.setPendingCount(qualityCounts.stream().mapToInt(QualityNumCountVo::getWaitingQuantity).sum());
                    });

            // 处理是否存在PCO变更单
            Optional.ofNullable(existPcoMap)
                    .map(map -> map.get(poId))
                    .ifPresent(existPco -> order.setExistPcoChange(existPco > 0));

            // 处理是否存在PCD变更单
            Optional.ofNullable(existPcdMap)
                    .map(map -> map.get(poId))
                    .ifPresent(existPcd -> order.setExistPcdChange(existPcd > 0));

            if (fileMap != null && fileMap.containsKey(poId)) {
                order.setFileList(fileMap.get(poId));
            }

            // 处理用户信息
            if (userMap != null && userMap.containsKey(order.getBuyer().getId().id())) {
                order.getBuyer().setOperatorCode(userMap.get(order.getBuyer().getId().id()).getCode());
                order.getBuyer().setOperatorName(userMap.get(order.getBuyer().getId().id()).getName());
            }
            if (userMap != null && userMap.containsKey(order.getPlanStaff().getId().id())) {
                order.getPlanStaff().setOperatorCode(userMap.get(order.getPlanStaff().getId().id()).getCode());
                order.getPlanStaff().setOperatorName(userMap.get(order.getPlanStaff().getId().id()).getName());
            }

            // 处理产品信息
            PurchaseOrderPriceInfo priceInfo = priceInfoMap.get(poId);
            if (Objects.nonNull(priceInfo)) {
                order.setPurchaseAmount(priceInfo.getInclusiveTaxPrice().multiply(new BigDecimal(order.getNormalQty())).setScale(2, RoundingMode.HALF_UP));
            }
            // 处理审核信息
            if (order.getReviewStatus().equals(PurchaseOrderReviewStatusEnum.WAIT_REVIEW) &&
                    processInstanceUserVoMap != null && processInstanceUserVoMap.containsKey(order.getProcessInstanceId())) {
                ProcessInstanceUserVo processInstanceUserVo = processInstanceUserVoMap.get(order.getProcessInstanceId());
                if (processInstanceUserVo != null && processInstanceUserVo.getCurrentApprovalUserIdSet() != null) {
                    processInstanceUserVo.getCurrentApprovalUserIdSet().forEach(userId -> {
                        if (userMap != null && userMap.containsKey(userId)) {
                            order.setReviewId(userId);
                            order.setReviewCode(userMap.get(userId).getCode());
                            order.setReviewName(userMap.get(userId).getName());
                        }
                    });
                }
            }
        }
    }


    /**
     * 处理红点信息
     */
    private void setNoticeInfo(PurchaseOrder order, List<PurchaseOrderNotice> notices) {
        // 确保 notices 不为 null
        if (CollectionUtils.isEmpty(notices)) {
            order.setChangedDeliveryDateStatus(PurchaseNoticeStatusEnum.DONE.getCode());
            order.setCheckedDeliveryDateStatus(PurchaseNoticeStatusEnum.DONE.getCode());
            return; // 如果没有相关通知，直接跳过处理
        }

        // 获取 noticeType == 1 的通知
        PurchaseOrderNotice purchaseOrderNotice = getNoticeByType(notices, PurchaseChangeTypeEnum.CHANGE_DELIVERY_DATE.getCode());
        order.setChangedDeliveryDateStatus(purchaseOrderNotice == null ? PurchaseNoticeStatusEnum.DONE.getCode() : purchaseOrderNotice.getNoticeStatus());

        // 获取 noticeType == 2 的通知
        PurchaseOrderNotice checkNotice = getNoticeByType(notices, PurchaseChangeTypeEnum.REVIEW_DELIVERY_DATE.getCode());
        order.setCheckedDeliveryDateStatus(checkNotice == null ? PurchaseNoticeStatusEnum.DONE.getCode() : checkNotice.getNoticeStatus());
    }

    private PurchaseOrderNotice getNoticeByType(List<PurchaseOrderNotice> notices, Integer noticeType) {
        if (CollectionUtils.isEmpty(notices)) {
            return null;
        }
        return notices.stream()
                .filter(notice -> notice.getNoticeType().equals(noticeType)).findFirst().orElse(null); // 避免不必要的对象创建
    }

    /**
     * 查询采购单详情
     *
     * @param purchaseOrderId 采购单id
     */
    public PurchaseOrder detail(PurchaseOrderId purchaseOrderId) {
        // 查找 PurchaseOrder 并处理未找到的情况
        PurchaseOrder purchaseOrder = purchaseOrderLookup.findById(purchaseOrderId)
                .orElseThrow(() -> new ApiException(BizErrorCode.DATA_DOES_NOT_EXIST));
        List<PurchaseOrderItem> items = purchaseOrderItemLookup.findByPoId(purchaseOrderId);
        PurchaseOrderProductInfo productInfo = purchaseOrderProductInfoLookup.findByPoId(purchaseOrderId);
        PurchaseOrderSupplierInfo supplierInfo = purchaseOrderSupplierInfoLookup.findByPoId(purchaseOrderId);
        PurchaseOrderPriceInfo priceInfo = purchaseOrderPriceInfoLookup.findByPoId(purchaseOrderId);

        //TODO 出库计划
        List<ShipmentPlanOrder> shipmentPlanOrders = shipmentPlanOrderLookup.findByPoNoAndStatus(purchaseOrder.getPoNo(), Arrays.asList(ShipmentPlanOrderStatusEnum.ON_REVIEW.getValue(), ShipmentPlanOrderStatusEnum.PEND_CONFIRM.getValue(), ShipmentPlanOrderStatusEnum.CONFIRMED.getValue()));
        Map<String, ShipmentPlanOrder> shipmentPlanMap = shipmentPlanOrders.stream().collect(Collectors.toMap(ShipmentPlanOrder::getPdNo, Function.identity()));
        List<ShipmentPlanOrderItem> shipmentPlanOrderItems = shipmentPlanOrderItemLookup.findByPdNoList(shipmentPlanOrders.stream().map(ShipmentPlanOrder::getPdNo).collect(Collectors.toSet()));
        // 处理TR状态
        Set<String> trNos = shipmentPlanOrderItems.stream().map(ShipmentPlanOrderItem::getTrNo).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(trNos)) {
            Map<String, TransportRequestClientVo> trMap = remoteTransportRequestFeign.findByTrNoList(trNos)
                    .stream()
                    .collect(Collectors.toMap(TransportRequestClientVo::getTrNo, Function.identity(), (v1, v2) -> v1));

            shipmentPlanOrderItems.forEach(item -> {
                if (item.getTrNo() != null && trMap.containsKey(item.getTrNo())) {
                    TransportRequestClientVo trVo = trMap.get(item.getTrNo());
                    if (trVo != null && trVo.getTrStatus() != null) {
                        item.setTrStatus(trVo.getTrStatus().getValue());
                    }
                }
            });
        }
        Map<String, List<ShipmentPlanOrderItem>> shipmentPlanItemMap = shipmentPlanOrderItems.stream().collect(Collectors.groupingBy(ShipmentPlanOrderItem::getPrNo));

        List<WarehouseId> warehouseIds = shipmentPlanOrderItems.stream().map(ShipmentPlanOrderItem::getDestWarehouseId).collect(Collectors.toList());
        Map<WarehouseId, Warehouse> warehouseMap = warehouseLookup.getWarehouseMapByIds(warehouseIds);

        List<StoreId> storeIds = items.stream().map(e -> e.getStore().getId()).distinct().collect(Collectors.toList());
        List<SalesChannelId> salesChannelIds = items.stream().map(e -> e.getSalesChannel().getId()).distinct().collect(Collectors.toList());

        Map<StoreId, Store> storeMap = storeLookup.queryStoreMapByIds(storeIds);
        Map<SalesChannelId, SalesChannel> salesChannelMap = salesChannelLookup.querySalesChannelMapByIds(salesChannelIds);


        purchaseOrder.setPoTypes(items.stream().map(PurchaseOrderItem::getPoType).distinct().collect(Collectors.toList()));

        if (Objects.nonNull(priceInfo.getInclusiveTaxPrice()) && Objects.nonNull(purchaseOrder.getNormalQty())) {
            purchaseOrder.setPurchaseAmount(priceInfo.getInclusiveTaxPrice().multiply(new BigDecimal(purchaseOrder.getNormalQty())).setScale(2, RoundingMode.HALF_UP));
        }
        Set<Operator> userIdList = new HashSet<>(Collections.unmodifiableSortedSet(new TreeSet<>()));
        userIdList.add(purchaseOrder.getBuyer());
        userIdList.add(purchaseOrder.getPlanStaff());
        userIdList.add(supplierInfo.getSourcingPicUser());

        List<PurchaseOrderItemForDetail> detailItems = new ArrayList<>();

        items.forEach(item -> {
            userIdList.add(item.getOperationStaffId());
            if (storeMap.containsKey(item.getStore().getId())) {
                item.setStore(storeMap.get(item.getStore().getId()));
            }
            if (salesChannelMap.containsKey(item.getSalesChannel().getId())) {
                item.setSalesChannel(salesChannelMap.get(item.getSalesChannel().getId()));
            }

            detailItems.addAll(PurchaseOrderItemForDetail.buildPurchaseOrderItemForDetail(item, shipmentPlanMap,
                    shipmentPlanItemMap.get(item.getPrNo()), warehouseMap));
        });


        purchaseOrder.setDetailItems(detailItems);
        operatorLookup.findAndSetByIds(userIdList);
        purchaseOrder.setProductInfo(productInfo);
        purchaseOrder.setSupplierInfo(supplierInfo);
        purchaseOrder.setPriceInfo(priceInfo);
        return purchaseOrder;
    }

    /**
     * 查询用于创建PCO的PO详情信息
     * @param purchaseOrderId
     * @return
     */
    public PurchaseOrder findPoDetailForCreatePco(PurchaseOrderId purchaseOrderId) {
        PurchaseOrder purchaseOrder = purchaseOrderLookup.findById(purchaseOrderId)
                .orElseThrow(() -> new ApiException(BizErrorCode.DATA_DOES_NOT_EXIST));
        List<PurchaseOrderItem> items = purchaseOrderItemLookup.findByPoId(purchaseOrderId);
        PurchaseOrderProductInfo productInfo = purchaseOrderProductInfoLookup.findByPoId(purchaseOrderId);
        PurchaseOrderSupplierInfo supplierInfo = purchaseOrderSupplierInfoLookup.findByPoId(purchaseOrderId);
        PurchaseOrderPriceInfo priceInfo = purchaseOrderPriceInfoLookup.findByPoId(purchaseOrderId);

        List<StoreId> storeIds = new ArrayList<>();
        List<SalesChannelId> salesChannelIds = new ArrayList<>();
        List<Operator> userIds = new ArrayList<>();

        for (var item : items) {
            storeIds.add(item.getStore().getId());
            salesChannelIds.add(item.getSalesChannel().getId());
            userIds.add(item.getOperationStaffId());
        }

        userIds.add(purchaseOrder.getBuyer());
        userIds.add(purchaseOrder.getPlanStaff());
        userIds.add(supplierInfo.getSourcingPicUser());
        operatorLookup.findAndSetByIds(userIds);

        Map<StoreId, Store> storeMap = storeLookup.queryStoreMapByIds(new ArrayList<>(storeIds));
        Map<SalesChannelId, SalesChannel> salesChannelMap = salesChannelLookup.querySalesChannelMapByIds(new ArrayList<>(salesChannelIds));

        items.forEach(item -> {
            if (storeMap.containsKey(item.getStore().getId())) {
                item.setStore(storeMap.get(item.getStore().getId()));
            }
            if (salesChannelMap.containsKey(item.getSalesChannel().getId())) {
                item.setSalesChannel(salesChannelMap.get(item.getSalesChannel().getId()));
            }
        });

        if (Objects.nonNull(priceInfo.getInclusiveTaxPrice()) && Objects.nonNull(purchaseOrder.getNormalQty())) {
            priceInfo.setPurchaseAmount(priceInfo.getInclusiveTaxPrice().multiply(new BigDecimal(purchaseOrder.getNormalQty())).setScale(2, RoundingMode.HALF_UP));
        }
        purchaseOrder.setPoTypes(items.stream().map(PurchaseOrderItem::getPoType).distinct().collect(Collectors.toList()));
        purchaseOrder.setItems(items);
        purchaseOrder.setProductInfo(productInfo);
        purchaseOrder.setSupplierInfo(supplierInfo);
        purchaseOrder.setPriceInfo(priceInfo);
        return purchaseOrder;
    }

    @LogRecord(module = LogModule.PURCHASE_ORDER_LOG_MODULE
            , type = LogModule.CommonDesc.INSERT_OPERATOR
            , desc = LogModule.CommonDesc.INSERT_DESC)
    @Transactional(rollbackFor = Exception.class)
    public List<PurchaseOrderItem> batchCreatePurchaseOrder(Collection<PurchaseOrder> poList, Integer auditUserId) {
        // 输入校验
        if (CollectionUtil.isEmpty(poList)) {
            return Collections.emptyList();
        }
        // 因为MQ回调后无法获取当前用户，所以需要获取系统用户传给PO单
        Operator operator = null;
        if (Objects.nonNull(auditUserId)) {
            operator = Optional.of(auditUserId).map(OperatorId::new).map(o -> {
                Operator op = new Operator();
                op.setOperatorId(o);
                return op;
            }).get();
        } else {
            operator = operatorLookup.findSystemUser();
        }

        // 根据供应商代码查询供应商信息
        List<String> supplierCodes = poList.stream().map(PurchaseOrder::getSupplierCode).toList();
        Map<String, PurchaseSupplierDetailsResp> supplierMap = purchaseSupplierLookup.findBySupplierCodes(supplierCodes);
        log.info("SRM获取到的供应商原始数据：【{}】", JSON.toJSONString(supplierMap));

        // 根据产品代码查询产品信息
        List<PurchaseOrderItem> purchaseOrderItems = new ArrayList<>();
        List<ProductId> productIds = poList.stream().map(PurchaseOrder::getProduct).map(Product::getId).toList();
        Map<ProductId, Product> productMap = productLookup.findByIds(productIds);
        //查询最新汇率
        Map<String, LatestFluctuateExchangeRateResponse> rateMap = rateLookup.findLatestExchangeRate("CNY");
        //根据产品代码查询产品商务信息
        Map<ProductId, ProductBusinessManagerResponse> productManagerMap = remoteProductBusinessManagerFeign.findByProductIds(productIds);

        try {
            for (PurchaseOrder purchaseOrder : poList) {
                ProductBusinessManagerResponse pbm = productManagerMap.get(purchaseOrder.getProduct().getId());
                //备品率
                purchaseOrder.setSpareRate(pbm.getSparePartsRate());
                purchaseOrder.init(operator);
                //设置SN信息
                Product product = productMap.get(purchaseOrder.getProduct().getId());
                //设置采购单sn码，并更新品牌最新sn码
                purchaseOrderSnService.setPurchaseSnInfo(purchaseOrder, product.getSn());
                //构建辅助信息（产品快照、供应商快照、产品价目快照）
                buildAuxiliary(purchaseOrder, supplierMap, product, rateMap, operator,null);
                Integer poId = purchaseOrderRepository.savePurchaseOrder(purchaseOrder);
                // 构建并累积 PurchaseOrderItemPO
                purchaseOrderItems.addAll(purchaseOrderItemRepository.batchSavePurchaseOrderItem(purchaseOrder.getItems(), operator, poId, purchaseOrder.getPoNo()));
                // 插入辅助信息
                insertAuxiliaryInfo(poId, purchaseOrder);
                LogRecordContextHolder.putRecordData(poId.toString(), null, purchaseOrder);
            }
        } catch (Exception e) {
            // 记录异常日志，确保事务回滚
            log.error("Error occurred during batch creation of purchase orders", e);
            throw e;
        }
        return purchaseOrderItems;
    }

    /**
     * 构建辅助信息（产品快照、供应商快照、产品价目快照）
     *
     * @param purchaseOrder 采购单
     * @param supplierMap   供应商信息
     * @param product       产品信息
     * @param rateMap       汇率信息
     * @param operator      操作人
     */
    private void buildAuxiliary(PurchaseOrder purchaseOrder,
                                Map<String, PurchaseSupplierDetailsResp> supplierMap,
                                Product product,
                                Map<String, LatestFluctuateExchangeRateResponse> rateMap,
                                Operator operator,
                                PurchaseOrderPriceInfo oldPrice) {
        PurchaseSupplierDetailsResp supplierInfo = supplierMap.get(purchaseOrder.getSupplierCode());
        log.info("供应商信息supplierInfo：{}", JSON.toJSONString(supplierInfo));
        PurchaseOrderSupplierInfo supplier = purchaseOrderSupplierConverter.toPurchaseOrderSupplierInfo(supplierInfo, operator);
        purchaseOrder.setSupplierInfo(supplier);
        purchaseOrder.setSupplierId(supplier.getSupplierId());

        purchaseOrder.setProductInfo(purchaseOrderProductConverter.toDomain(product, operator));
        purchaseOrder.setCategoryId(product.getCategoryId());

        if (Objects.isNull(oldPrice)){
            //已启用的产品价目
            ProductPrice productPrice = product.getEnabledProductPrice();

            String currencyType = productPrice.getCurrencyType();
            Optional.ofNullable(rateMap.get(currencyType))
                    .ifPresentOrElse(rate -> {
                        BigDecimal latestExchangeRate = rate.getExchangeRate();
                        ProductPriceItem finalPriceItem = productPrice.findFinalPriceItem(latestExchangeRate);
                        PurchaseOrderPriceInfo priceInfo = purchaseOrderPriceConverter.toDomain(rate, productPrice,
                                finalPriceItem, operator);
                        priceInfo.setPurchaseAmount(priceInfo.getInclusiveTaxPrice()
                                .multiply(new BigDecimal(purchaseOrder.getNormalQty()))
                                .setScale(2, RoundingMode.HALF_UP));

                        purchaseOrder.setPriceInfo(priceInfo);
                    }, () -> {
                        throw new BusinessException("pds.product.price.not.match");
                    });
        }else{
            PurchaseOrderPriceInfo copy = purchaseOrderPriceConverter.copy(oldPrice, operator);
            copy.setPurchaseAmount(copy.getInclusiveTaxPrice()
                    .multiply(new BigDecimal(purchaseOrder.getNormalQty()))
                    .setScale(2, RoundingMode.HALF_UP));
            purchaseOrder.setPriceInfo(copy);
        }



    }

    // 提取公共逻辑
    private void insertAuxiliaryInfo(Integer poId, PurchaseOrder purchaseOrder) {
        // 插入 ProductInfo
        purchaseOrderProductInfoRepository.savePurchaseOrderProductInfo(purchaseOrder.getProductInfo(), poId, purchaseOrder.getPoNo());
        // 插入 SupplierInfo
        purchaseOrderSupplierInfoRepository.savePurchaseOrderSupplierInfo(purchaseOrder.getSupplierInfo(), poId, purchaseOrder.getPoNo());
        // 插入 PriceInfo
        purchaseOrderPriceInfoRepository.savePurchaseOrderPriceInfo(purchaseOrder.getPriceInfo(), poId, purchaseOrder.getPoNo());
    }

    /**
     * 编辑采购单
     *
     * @param purchaseOrderEdit 采购单编辑参数
     */
    @LogRecord(module = LogModule.PURCHASE_ORDER_LOG_MODULE
            , type = LogModule.CommonDesc.EDIT_OPERATOR
            , desc = LogModule.CommonDesc.EDIT_DESC)
    @Transactional(rollbackFor = Exception.class)
    public void savePurchaseOrder(PurchaseOrderEdit purchaseOrderEdit) {
        // 参数校验
        PurchaseOrder purchaseOrder = validatePurchaseEditParam(purchaseOrderEdit);
        PurchaseOrder oldPurchaseOrder = SerializationUtils.clone(purchaseOrder);
        updatePurchaseOrderFields(purchaseOrder, purchaseOrderEdit);
        purchaseOrderRepository.updateById(purchaseOrder);
        LogRecordContextHolder.putRecordData(purchaseOrder.getId().id().toString(), oldPurchaseOrder, purchaseOrder);
    }

    /**
     * 审核采购单日志使用
     * 采购单撤回的日志已经另外处理
     */
    @LogRecord(module = LogModule.PURCHASE_ORDER_LOG_MODULE
            , type = LogModule.CommonDesc.REVIEW_OPERATOR
            , desc = LogModule.CommonDesc.REVIEW_DESC)
    public void saveReviewPurchaseOrderLog(PurchaseOrder purchaseOrder, PurchaseOrderStatusEnum status, PurchaseOrderReviewStatusEnum auditStatus, Integer auditUserId) {
        PurchaseOrder oldPurchaseOrder = SerializationUtils.clone(purchaseOrder);
        // 参数校验
        purchaseOrder.setStatus(status);
        purchaseOrder.setReviewStatus(auditStatus);
        purchaseOrder.setReviewTime(LocalDateTime.now());
        log.info("采购订单状态已更新，PO编号: {}, old状态: {}, 审核状态: {}", purchaseOrder.getPoNo(), oldPurchaseOrder.getStatus(), auditStatus);
        settingAuditUser(purchaseOrder, auditUserId);
        purchaseOrderRepository.updateById(purchaseOrder);
        log.info("采购订单状态已更新，PO编号: {}, 新状态: {}, 审核状态: {}", purchaseOrder.getPoNo(), status, auditStatus);
        LogRecordContextHolder.putRecordData(String.valueOf(purchaseOrder.getId().id()), oldPurchaseOrder, purchaseOrder);
    }

    /**
     * 审核采购单日志使用
     * 采购单撤回的日志已经另外处理
     */
    @LogRecord(module = LogModule.PURCHASE_ORDER_LOG_MODULE
            , type = LogModule.CommonDesc.WITHDRAW_OPERATOR
            , desc = LogModule.CommonDesc.WITHDRAW_DESC)
    public void saveWithDrawPurchaseOrderLog(PurchaseOrder purchaseOrder, PurchaseOrderStatusEnum status, PurchaseOrderReviewStatusEnum auditStatus, Integer auditUserId) {
        PurchaseOrder oldPurchaseOrder = SerializationUtils.clone(purchaseOrder);
        // 参数校验
        purchaseOrder.setStatus(status);
        purchaseOrder.setReviewStatus(auditStatus);
        purchaseOrder.setReviewTime(LocalDateTime.now());
        purchaseOrder.setProcessInstanceId("");
        purchaseOrder.setReviewTime(null);
        settingAuditUser(purchaseOrder, auditUserId);
        purchaseOrderRepository.updateById(purchaseOrder);
        log.info("订单状态已更新，PO编号: {}, 新状态: {}, 审核状态: {}", purchaseOrder.getPoNo(), status, auditStatus);
        LogRecordContextHolder.putRecordData(String.valueOf(purchaseOrder.getId().id()), oldPurchaseOrder, purchaseOrder);
    }

    /**
     * 设置审核人为更新人
     */
    private void settingAuditUser(PurchaseOrder purchaseOrder, Integer auditUserId) {
        // BPM 审核人
        if (Objects.nonNull(auditUserId)) {
            Operator updateOperator = new Operator();
            updateOperator.setOperatorId(new OperatorId(auditUserId));
            purchaseOrder.setUpdated(updateOperator);
        }
    }

    /**
     * 审核采购单，包含采购单的撤回回调
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateReviewPurchaseOrder(PurchaseOrder purchaseOrder, PurchaseOrderStatusEnum status, PurchaseOrderReviewStatusEnum auditStatus, Integer auditUserId) {
        if (status.equals(PurchaseOrderStatusEnum.PENDING)) {
            SpringUtil.getBean(PurchaseOrderService.class).saveWithDrawPurchaseOrderLog(purchaseOrder, status, auditStatus, auditUserId);
        } else {
            SpringUtil.getBean(PurchaseOrderService.class).saveReviewPurchaseOrderLog(purchaseOrder, status, auditStatus, auditUserId);
            if (PurchaseOrderReviewStatusEnum.PASS.equals(auditStatus)) {
                // 因为MQ回调后无法获取当前用户，所以需要获取系统用户传给PO单
                Operator operator = null;
                if (Objects.isNull(auditUserId)) {
                    operator = operatorLookup.findSystemUser();
                } else {
                    operator = Optional.of(auditUserId).map(OperatorId::new).map(o -> {
                        Operator op = new Operator();
                        op.setOperatorId(o);
                        return op;
                    }).get();
                }
                List<PurchaseOrderItem> byPoId = purchaseOrderItemLookup.findByPoId(purchaseOrder.getId());
                PurchaseOrderProductInfo productInfo = purchaseOrderProductInfoLookup.findByPoId(purchaseOrder.getId());
                Map<PurchaseRequestId, PurchaseRequestItem> byPrIds = purchaseRequestItemLookup.findByPrIds(byPoId.stream().map(data -> new PurchaseRequestId(data.getPrId())).toList());
                Operator finalOperator = operator;
                List<PendShipmentPlan> pendShipmentPlanList = byPoId.stream().map(data -> {
                    PendShipmentPlan pendShipmentPlan = new PendShipmentPlan(null);
                    pendShipmentPlan.setPurchaseOrderItem(data);
                    pendShipmentPlan.setProductInfo(productInfo);
                    pendShipmentPlan.setPurchaseOrder(purchaseOrder);
                    pendShipmentPlan.setPurchaseRequestItem(byPrIds.get(new PurchaseRequestId(data.getPrId())));
                    pendShipmentPlan.setPendQty(data.getApplicationQty());
                    pendShipmentPlan.setCreator(finalOperator);
                    pendShipmentPlan.setUpdater(finalOperator);
                    pendShipmentPlan.setLastDeliveryDate(purchaseOrder.getConfirmedDeliveryDate());
                    return pendShipmentPlan;
                }).toList();
                pendShipmentPlanRepository.batchSave(pendShipmentPlanList);

                // 记录同步任务推送IMS
                syncTaskManager.createTask(SyncNodeEnum.PO_AUDIT_SUCCESS.getNodeCode(), purchaseOrder.getPoNo());

                // 推送完整PO信息
                pushPoReviewPass(purchaseOrder);
            }
        }
    }

    /**
     * 推送审核通过的PO信息
     *
     * @param purchaseOrder PO
     */
    private void pushPoReviewPass(PurchaseOrder purchaseOrder) {
        // 推送完整PO
        PurchaseOrderId purchaseOrderId = purchaseOrder.getId();
        // 产品价目信息
        PurchaseOrderPriceInfo priceInfo = purchaseOrderPriceInfoLookup.findByPoId(purchaseOrderId);
        // 供应商信息
        PurchaseOrderSupplierInfo supplierInfo = purchaseOrderSupplierInfoLookup.findByPoId(purchaseOrderId);
        String supplierCode = supplierInfo.getSupplierCode();
        // 获取原始采购供应商信息
        Map<String, PurchaseSupplierDetailsResp> supplierMap = purchaseSupplierLookup.findBySupplierCodes(List.of(supplierCode));
        List<PurchaseOrderId> purchaseOrderIdList = List.of(purchaseOrderId);
        Map<PurchaseOrderId, PurchaseRequest> purchaseRequestMap = purchaseRequestLookup.findByPoIds(purchaseOrderIdList);
        // 获取产品信息
        Map<PurchaseOrderId, PurchaseOrderProductInfo> productInfoMap = purchaseOrderProductInfoLookup.findAllByPoIds(purchaseOrderIdList);
        PurchaseOrderDTO purchaseOrderDTO = generatePurchaseOrderRequest(purchaseOrder, Map.of(purchaseOrderId, priceInfo),
                Map.of(purchaseOrderId, supplierInfo), Map.of(purchaseOrderId, supplierMap.get(supplierCode)), purchaseRequestMap, productInfoMap);
        purchaseOrderProducer.poReviewPass(purchaseOrderDTO);
    }

    /**
     * 生成完整PO请求信息
     *
     * @param purchaseOrder   PO基础信息
     * @param priceInfoMap    PO关联产品价目信息
     * @param supplierInfoMap PO关联供应商信息
     * @param supplierMap     PO关联采购供应商原始信息
     * @param requestMap      PO关联的PR信息
     * @param productInfoMap  PO关联的产品信息
     * @return 完整PO
     */
    public PurchaseOrderDTO generatePurchaseOrderRequest(PurchaseOrder purchaseOrder, Map<PurchaseOrderId, PurchaseOrderPriceInfo> priceInfoMap,
                                                         Map<PurchaseOrderId, PurchaseOrderSupplierInfo> supplierInfoMap,
                                                         Map<PurchaseOrderId, PurchaseSupplierDetailsResp> supplierMap,
                                                         Map<PurchaseOrderId, PurchaseRequest> requestMap,
                                                         Map<PurchaseOrderId, PurchaseOrderProductInfo> productInfoMap) {
        PurchaseOrderId purchaseOrderId = purchaseOrder.getId();
        purchaseOrder.setRequest(requestMap.get(purchaseOrderId));
        purchaseOrder.setProductInfo(productInfoMap.get(purchaseOrderId));
        PurchaseOrderDTO purchaseOrderDTO = purchaseOrderRequestConverter.toPurchaseOrderRequest(purchaseOrder);
        // 产品价目信息
        PurchaseOrderPriceInfo priceInfo = priceInfoMap.get(purchaseOrderId);
        if (Objects.nonNull(priceInfo.getInclusiveTaxPrice()) && Objects.nonNull(purchaseOrder.getNormalQty())) {
            // 计算采购总额
            purchaseOrderDTO.setPurchaseAmount(priceInfo.getInclusiveTaxPrice().multiply(new BigDecimal(purchaseOrder.getNormalQty()))
                    .setScale(2, RoundingMode.HALF_UP));
        }
        purchaseOrderDTO.setPriceInfo(purchaseOrderRequestConverter.toPurchaseOrderPriceInfoRequest(priceInfo));
        // 获取关联采购供应商信息
        PurchaseSupplierDetailsResp purchaseSupplierDetailsResp = supplierMap.get(purchaseOrderId);
        PurchaseOrderSupplierInfoDTO purchaseOrderSupplierInfoDTO = purchaseOrderRequestConverter
                .toPurchaseOrderSupplierInfoRequestBasic(purchaseSupplierDetailsResp);
        PurchaseOrderSupplierInfo supplierInfo = supplierInfoMap.get(purchaseOrderId);
        purchaseOrderSupplierInfoDTO.setSupplerSettlementId(supplierInfo.getSupplerSettlementId());
        purchaseOrderSupplierInfoDTO.setSupplerFinancialId(supplierInfo.getSupplerFinancialId());
        purchaseOrderSupplierInfoDTO.setSupplierTradeTerms(supplierInfo.getSupplierTradeTerms());
        purchaseOrderSupplierInfoDTO.setSupplierDeliveryLocation(supplierInfo.getSupplierDeliveryLocation());
        // 获取原始采购供应商信息
        purchaseOrderRequestConverter.toPurchaseOrderSupplierInfoRequest(purchaseOrderSupplierInfoDTO, purchaseSupplierDetailsResp);
        purchaseOrderDTO.setSupplierInfo(purchaseOrderSupplierInfoDTO);
        return purchaseOrderDTO;
    }

    /**
     * 生成多个完整PO请求信息
     *
     * @param purchaseOrderIdList PO的id
     * @return PO的id - 完整PO请求信息
     */
    public Map<PurchaseOrderId, PurchaseOrderDTO> generatePurchaseOrderRequestList(List<PurchaseOrderId> purchaseOrderIdList) {
        // PO信息
        Map<PurchaseOrderId, PurchaseOrder> purchaseOrderMap = purchaseOrderLookup.findByPoIds(purchaseOrderIdList);
        // 产品价目信息
        Map<PurchaseOrderId, PurchaseOrderPriceInfo> priceInfoMap = purchaseOrderPriceInfoLookup.findByPoIds(purchaseOrderIdList);
        // 供应商信息
        Map<PurchaseOrderId, PurchaseOrderSupplierInfo> supplierInfoMap = purchaseOrderSupplierInfoLookup.findByPoIds(purchaseOrderIdList);
        // 获取原始采购供应商信息
        Map<String, PurchaseSupplierDetailsResp> supplierMap = purchaseSupplierLookup.findBySupplierCodes(
                supplierInfoMap.values().stream().map(PurchaseOrderSupplierInfo::getSupplierCode).distinct().toList()
        );
        // 获取PR信息
        Map<PurchaseOrderId, PurchaseRequest> purchaseRequestMap = purchaseRequestLookup.findByPoIds(purchaseOrderIdList);
        // 获取产品信息
        Map<PurchaseOrderId, PurchaseOrderProductInfo> productInfoMap = purchaseOrderProductInfoLookup.findAllByPoIds(purchaseOrderIdList);
        Map<PurchaseOrderId, PurchaseSupplierDetailsResp> supplierInfoOriginalMap = supplierInfoMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> supplierMap.get(entry.getValue().getSupplierCode())
                ));
        return purchaseOrderMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> generatePurchaseOrderRequest(entry.getValue(), priceInfoMap, supplierInfoMap, supplierInfoOriginalMap, purchaseRequestMap,
                                productInfoMap)
                ));
    }

    /**
     * 编辑并提交采购单
     *
     * @param purchaseOrderEdit 采购单编辑参数
     */
    @LogRecord(module = LogModule.PURCHASE_ORDER_LOG_MODULE
            , type = LogModule.CommonDesc.SUBMIT_OPERATOR
            , desc = LogModule.CommonDesc.SUBMIT_DESC)
    @Transactional(rollbackFor = Exception.class)
    public void submitPurchaseOrder(PurchaseOrderEdit purchaseOrderEdit) {
        // 参数校验
        PurchaseOrder purchaseOrder = validatePurchaseEditParam(purchaseOrderEdit);
        PurchaseOrder oldPurchaseOrder = SerializationUtils.clone(purchaseOrder);
        PurchaseOrderPriceInfo purchaseOrderPriceInfo = purchaseOrderPriceInfoLookup.findByPoId(purchaseOrder.getId());
        PurchaseOrderSupplierInfo purchaseOrderSupplierInfo = purchaseOrderSupplierInfoLookup.findByPoId(purchaseOrder.getId());
        // 更新 PurchaseOrder 属性
        updatePurchaseOrderFields(purchaseOrder, purchaseOrderEdit);
        // 获取提交用户 ID
        String userId = SecurityUtils.getUserId() != null ? SecurityUtils.getUserId().toString() : "0";
        purchaseOrderRepository.updateById(purchaseOrder);
        // 构建表单并生成远程命令
        PurchaseOrderForm purchaseOrderForm = PurchaseOrderForm.build(purchaseOrder, purchaseOrderSupplierInfo, purchaseOrderPriceInfo);
        purchaseOrderForm.setSubmitUser(List.of(KeyAndValueDto.builder().key(userId).value(userId).build()));
        // 调用远程服务启动流程实例
        String processInstanceId = processInstanceService.startProcessInstance(purchaseOrder.getId().id().toString(), purchaseOrderForm,null);
        // 更新 PurchaseOrder 状态及相关字段
        purchaseOrder.setStatus(PurchaseOrderStatusEnum.ON_REVIEW);
        purchaseOrder.setSubmitTime(LocalDateTime.now());
        purchaseOrder.setReviewStatus(PurchaseOrderReviewStatusEnum.WAIT_REVIEW);
        purchaseOrder.setProcessInstanceId(processInstanceId);
        // 更新数据库
        purchaseOrderRepository.batchUpdateStatus(List.of(purchaseOrder), PurchaseOrderStatusEnum.ON_REVIEW);
        LogRecordContextHolder.putRecordData(purchaseOrder.getId().id().toString(), oldPurchaseOrder, purchaseOrder);
    }

    @LogRecord(module = LogModule.PURCHASE_ORDER_LOG_MODULE
            , type = LogModule.CommonDesc.SUBMIT_OPERATOR
            , desc = LogModule.CommonDesc.SUBMIT_DESC)
    @Transactional(rollbackFor = Exception.class)
    public void batchSubmitPurchaseOrder(List<PurchaseOrderEdit> purchaseOrderEditList) {
        // 参数校验
        List<PurchaseOrderId> ids = purchaseOrderEditList.stream().map(PurchaseOrderEdit::getPoId).collect(Collectors.toList());

        purchaseOrderEditList.forEach(item -> {
            if (Objects.isNull(item.getConfirmedDeliveryDate()) || item.getConfirmedDeliveryDate().isBefore(LocalDate.now())) {
                throw new ApiException(BizErrorCode.CONFIRM_DATE_BEFORE_NOW);
            }
        });

        String userId = SecurityUtils.getUserId() != null ? SecurityUtils.getUserId().toString() : "0";
        Map<PurchaseOrderId, PurchaseOrderPriceInfo> purchaseOrderPriceInfoMap = purchaseOrderPriceInfoLookup.findByPoIds(ids);
        Map<PurchaseOrderId, PurchaseOrder> purchaseOrderMap = purchaseOrderLookup.findByPoIds(ids);
        Map<PurchaseOrderId, PurchaseOrderSupplierInfo> purchaseOrderSupplierInfoMap = purchaseOrderSupplierInfoLookup.findByPoIds(ids);
        List<PurchaseOrder> purchaseOrders = new ArrayList<>();
        Map<PurchaseOrder, PurchaseOrder> logMap = new HashMap<>();
        for (PurchaseOrderEdit purchaseOrderEdit : purchaseOrderEditList) {
            PurchaseOrderId id = purchaseOrderEdit.getPoId();
            PurchaseOrder purchaseOrder = purchaseOrderMap.get(id);
            PurchaseOrder oldPurchaseOrder = SerializationUtils.clone(purchaseOrder);
            if (Objects.isNull(purchaseOrder)) {
                log.info("采购单不存在：{}", id);
                continue;
            }
            if (!PurchaseOrderStatusEnum.PENDING.equals(purchaseOrder.getStatus())) {
                log.info("采购单:{} 的状态不是待提交", purchaseOrder.getPoNo());
                continue;
            }
            PurchaseOrderPriceInfo purchaseOrderPriceInfo = purchaseOrderPriceInfoMap.get(id);
            PurchaseOrderSupplierInfo purchaseOrderSupplierInfo = purchaseOrderSupplierInfoMap.get(id);
            // 更新 PurchaseOrder 属性
            updatePurchaseOrderFields(purchaseOrder, purchaseOrderEdit);
            purchaseOrderRepository.updateById(purchaseOrder);
            // 构建表单并生成远程命令
            PurchaseOrderForm purchaseOrderForm = PurchaseOrderForm.build(purchaseOrder, purchaseOrderSupplierInfo, purchaseOrderPriceInfo);
            purchaseOrderForm.setSubmitUser(List.of(KeyAndValueDto.builder().key(userId).value(userId).build()));
            // 调用远程服务启动流程实例
            String processInstanceId = processInstanceService.startProcessInstance(purchaseOrder.getId().id().toString(), purchaseOrderForm,null);
            // 更新 PurchaseOrder 状态及相关字段
            purchaseOrder.setStatus(PurchaseOrderStatusEnum.ON_REVIEW);
            purchaseOrder.setSubmitTime(LocalDateTime.now());
            purchaseOrder.setReviewStatus(PurchaseOrderReviewStatusEnum.WAIT_REVIEW);
            purchaseOrder.setProcessInstanceId(processInstanceId);
            purchaseOrders.add(purchaseOrder);
            logMap.put(oldPurchaseOrder, purchaseOrder);
        }
        purchaseOrderRepository.batchUpdateStatus(purchaseOrders, PurchaseOrderStatusEnum.ON_REVIEW);
        // 旧日志
        if (logMap.isEmpty()) {
            return;
        }
        logMap.forEach((k, v) -> LogRecordContextHolder.putRecordData(k.getId().id().toString(), k, v));
    }

    public void prepareBatchReviewPurchaseOrder(List<PurchaseOrderId> ids) {
        // 参数校验
        Map<PurchaseOrderId, PurchaseOrder> purchaseOrderMap = purchaseOrderLookup.findByPoIds(ids);
        ids.forEach(id -> {
            PurchaseOrder purchaseOrder = purchaseOrderMap.get(id);
            if (Objects.isNull(purchaseOrder)) {
                throw new ApiException(BizErrorCode.DATA_DOES_NOT_EXIST);
            }
            if (!PurchaseOrderStatusEnum.ON_REVIEW.equals(purchaseOrder.getStatus())) {
                throw new ErrorCodeException(BizErrorCode.Purchase_Order_Status_IS_NOT_ON_REVIEW, purchaseOrder.getPoNo());
            }
        });
        List<String> instanceIds = purchaseOrderMap.values().stream().map(PurchaseOrder::getProcessInstanceId).filter(StringUtils::isNotBlank).toList();
        List<String> poNos = processInstanceService.getNotCurrentUserReviewOrders(instanceIds);
        if (CollectionUtils.isNotEmpty(poNos)) {
            List<String> poNoList = new ArrayList<>();
            poNos.forEach(poId -> {
                PurchaseOrder purchaseOrder = purchaseOrderMap.get(new PurchaseOrderId(Integer.parseInt(poId)));
                if (Objects.nonNull(purchaseOrder)) {
                    poNoList.add(purchaseOrder.getPoNo());
                }
            });
            throw new ErrorCodeException(BizErrorCode.REVIEW_USER_IS_NOT_CURRENT_USER, System.lineSeparator() + String.join(System.lineSeparator(), poNoList));
        }
    }


    // 参数校验方法
    private PurchaseOrder validatePurchaseEditParam(PurchaseOrderEdit purchaseOrderEdit) {

        if (purchaseOrderEdit.getConfirmedDeliveryDate() == null) {
            log.warn("确认日期为空，订单ID：{}", purchaseOrderEdit.getPoId());
        }
        if (purchaseOrderEdit.getPurchaseRemark() == null) {
            log.warn("备注信息为空，订单ID：{}", purchaseOrderEdit.getPoId());
        }

        if (Objects.isNull(purchaseOrderEdit.getConfirmedDeliveryDate()) || purchaseOrderEdit.getConfirmedDeliveryDate().isBefore(LocalDate.now())) {
            throw new ApiException(BizErrorCode.CONFIRM_DATE_BEFORE_NOW);
        }
        return validatePurchaseOrder(purchaseOrderEdit.getPoId().id(), PurchaseOrderStatusEnum.PENDING);
    }

    private PurchaseOrder validatePurchaseOrder(Integer poId, PurchaseOrderStatusEnum status) {
        // 查找 PurchaseOrder 并处理未找到的情况
        PurchaseOrder purchaseOrder = purchaseOrderLookup.findById(new PurchaseOrderId(poId))
                .orElseThrow(() -> new ApiException(BizErrorCode.DATA_DOES_NOT_EXIST));

        // 校验状态是否为 PENDING
        if (!status.getValue().equals(purchaseOrder.getStatus().getValue())) {
            throw new ErrorCodeException(BizErrorCode.Purchase_Order_Status_IS_NOT_PENDING, purchaseOrder.getPoNo());
        }
        return purchaseOrder;
    }

    // 更新 PurchaseOrder 字段方法
    private void updatePurchaseOrderFields(PurchaseOrder purchaseOrder, PurchaseOrderEdit purchaseOrderEdit) {
        purchaseOrder.setConfirmedDeliveryDate(purchaseOrderEdit.getConfirmedDeliveryDate());
        purchaseOrder.setPurchaseRemark(purchaseOrderEdit.getPurchaseRemark());
    }


    /**
     * 获取变更日期日志记录
     *
     * @param poId      PurchaseOrderId 采购单ID
     * @param isConfirm boolean 是否是计划角色查看【变更/复核交期记录】
     * @return List<PurchaseOrderDeliveryDateLog>
     */
    public List<PurchaseOrderDeliveryDateLog> listChangeDeliveryDateLog(PurchaseOrderId poId, Boolean isConfirm) {
        PurchaseOrder purchaseOrder = purchaseOrderLookup.findById(poId)
                .orElseThrow(() -> new ApiException(BizErrorCode.DATA_DOES_NOT_EXIST));
        // 计划角色查看【变更/复核交期记录】后，则复核日期红点提醒不显示
        if (isConfirm) {
            purchaseOrderNoticeRepository.updateStatusByPoIdAndType(poId.id(), PurchaseChangeTypeEnum.REVIEW_DELIVERY_DATE.getCode(),
                    PurchaseNoticeStatusEnum.DONE.getCode());
        }

        List<PurchaseOrderDeliveryDateLog> items = purchaseOrderDeliveryDateLogLookup.listChangeDeliveryDateLog(purchaseOrder.getPoNo());
        items.add(items.size(), PurchaseOrderDeliveryDateLog.buildPurchaseOrderDeliveryDateLog(purchaseOrder));
        Set<Operator> userIdList = items.stream()
                .map(PurchaseOrderDeliveryDateLog::getCreated)
                .filter(Objects::nonNull).collect(Collectors.toSet()); // 使用 Set 去重
        operatorLookup.findAndSetByIds(userIdList);
        return items;
    }

    /**
     * 返回该采购单的红点标识信息
     *
     * @param purchaseOrderId 采购单ID
     * @return List<Map < Integer, Integer>> 采购单红点表示
     */
    public Map<Integer, Integer> getRedDotInfo(PurchaseOrderId purchaseOrderId) {
        purchaseOrderLookup.findById(purchaseOrderId)
                .orElseThrow(() -> new ApiException(BizErrorCode.DATA_DOES_NOT_EXIST));
        //红点处理
        List<PurchaseOrderNotice> notices = purchaseOrderNoticeLookup.findByPoId(purchaseOrderId);
        Map<Integer, Integer> orderNoticeMap = new HashMap<>();
        // 确保 notices 不为 null
        if (CollectionUtils.isEmpty(notices)) {
            orderNoticeMap.put(PurchaseChangeTypeEnum.CHANGE_DELIVERY_DATE.getCode(), PurchaseNoticeStatusEnum.DONE.getCode());
            orderNoticeMap.put(PurchaseChangeTypeEnum.REVIEW_DELIVERY_DATE.getCode(), PurchaseNoticeStatusEnum.DONE.getCode());
        }
        // 获取 noticeType == 1 的通知
        PurchaseOrderNotice purchaseOrderNotice = getNoticeByType(notices, PurchaseChangeTypeEnum.CHANGE_DELIVERY_DATE.getCode());
        orderNoticeMap.put(PurchaseChangeTypeEnum.CHANGE_DELIVERY_DATE.getCode(), Objects.isNull(purchaseOrderNotice) ?
                PurchaseNoticeStatusEnum.DONE.getCode() : purchaseOrderNotice.getNoticeStatus());
        // 获取 noticeType == 2 的通知
        PurchaseOrderNotice checkNotice = getNoticeByType(notices, PurchaseChangeTypeEnum.REVIEW_DELIVERY_DATE.getCode());
        orderNoticeMap.put(PurchaseChangeTypeEnum.REVIEW_DELIVERY_DATE.getCode(), Objects.isNull(checkNotice) ?
                PurchaseNoticeStatusEnum.DONE.getCode() : checkNotice.getNoticeStatus());
        return orderNoticeMap;
    }

    /**
     * 变更交期
     */
    @LogRecord(module = LogModule.PURCHASE_ORDER_LOG_MODULE
            , type = LogModule.PurchaseOrderOperatorLogModule.CHG_DEL_DATE
            , desc = LogModule.PurchaseOrderOperatorLogModule.CHG_DEL_DATE_DESC)
    @Transactional(rollbackFor = Exception.class)
    public void changeDeliveryDate(PurchaseOrderDeliveryDateLog changeDeliveryDate) {
        PurchaseOrderId poId = new PurchaseOrderId(changeDeliveryDate.getPoId());
        PurchaseOrder purchaseOrder = purchaseOrderLookup.findById(poId)
                .orElseThrow(() -> new ApiException(BizErrorCode.DATA_DOES_NOT_EXIST));

        // 校验变更日期是否在当前日期之前和采购单状态
        validateBeforeUpdate(changeDeliveryDate, BizErrorCode.CHANGE_DATE_BEFORE_NOW, purchaseOrder);

        PurchaseOrder oldPurchaseOrder = SerializationUtils.clone(purchaseOrder);
        List<PurchaseOrderNotice> purchaseOrderNotices = purchaseOrderNoticeLookup.findByPoId(poId);
        purchaseOrderDeliveryDateLogRepository.savePurchaseOrderDeliveryDateLog(changeDeliveryDate);
        updateNotice(purchaseOrder, purchaseOrderNotices, PurchaseChangeTypeEnum.CHANGE_DELIVERY_DATE, PurchaseNoticeStatusEnum.UNDO);
        updateNotice(purchaseOrder, purchaseOrderNotices, PurchaseChangeTypeEnum.REVIEW_DELIVERY_DATE, PurchaseNoticeStatusEnum.DONE);
        purchaseOrder.setChangedDeliveryDate(changeDeliveryDate.getDeliveryDate());
        purchaseOrder.setModifyRemark(changeDeliveryDate.getRemark());
        purchaseOrderRepository.updateById(purchaseOrder);
        updateLastDeliveryDate(changeDeliveryDate.getPoId(), changeDeliveryDate, purchaseOrder.getPoNo());

        LogRecordContextHolder.putRecordData(purchaseOrder.getId().id().toString(), oldPurchaseOrder, purchaseOrder);
    }

    /**
     * 复核交期
     */
    @LogRecord(module = LogModule.PURCHASE_ORDER_LOG_MODULE
            , type = LogModule.PurchaseOrderOperatorLogModule.REV_DEL_DATE
            , desc = LogModule.PurchaseOrderOperatorLogModule.REV_DEL_DATE_DESC)
    @Transactional(rollbackFor = Exception.class)
    public void confirmDeliveryDate(PurchaseOrderDeliveryDateLog confirmDeliveryDate) {
        PurchaseOrderId poId = new PurchaseOrderId(confirmDeliveryDate.getPoId());
        PurchaseOrder purchaseOrder = purchaseOrderLookup.findById(poId)
                .orElseThrow(() -> new ApiException(BizErrorCode.DATA_DOES_NOT_EXIST));
        // 校验变更日期是否在当前日期之前和采购单状态
        validateBeforeUpdate(confirmDeliveryDate, BizErrorCode.REVIEW_DATE_BEFORE_NOW, purchaseOrder);
        PurchaseOrder oldPurchaseOrder = SerializationUtils.clone(purchaseOrder);
        List<PurchaseOrderNotice> purchaseOrderNotices = purchaseOrderNoticeLookup.findByPoId(poId);
        updateNotice(purchaseOrder, purchaseOrderNotices, PurchaseChangeTypeEnum.CHANGE_DELIVERY_DATE, PurchaseNoticeStatusEnum.DONE);
        updateNotice(purchaseOrder, purchaseOrderNotices, PurchaseChangeTypeEnum.REVIEW_DELIVERY_DATE, PurchaseNoticeStatusEnum.UNDO);
        purchaseOrderDeliveryDateLogRepository.savePurchaseOrderDeliveryDateLog(confirmDeliveryDate);
        purchaseOrder.setCheckedDeliveryDate(confirmDeliveryDate.getDeliveryDate());
        purchaseOrder.setReviewRemark(confirmDeliveryDate.getRemark());
        purchaseOrderRepository.updateById(purchaseOrder);
        updateLastDeliveryDate(confirmDeliveryDate.getPoId(), confirmDeliveryDate, purchaseOrder.getPoNo());
        LogRecordContextHolder.putRecordData(purchaseOrder.getId().id().toString(), oldPurchaseOrder, purchaseOrder);
    }


    /**
     * 更新最后交期
     *
     * @param poId               采购单ID
     * @param changeDeliveryDate 最新交期对象
     * @param poNo               采购单号
     */
    private void updateLastDeliveryDate(Integer poId, PurchaseOrderDeliveryDateLog changeDeliveryDate, String poNo) {
        List<PendShipmentPlan> pendShipmentPlans = pendShipmentPlanLookup.findByPoList(Collections.singletonList(poId));
        LocalDate lastDeliveryDate = changeDeliveryDate.getDeliveryDate();
        if (CollectionUtils.isNotEmpty(pendShipmentPlans)) {
            List<Integer> pendIds = pendShipmentPlans.stream().map(PendShipmentPlan::getId).map(PendShipmentPlanId::id).collect(Collectors.toList());
            pendShipmentPlanRepository.updatePendShipmentLastDeliveryDate(pendIds, lastDeliveryDate);
            shipmentPlanOrderItemRepository.updateShipmentLastDeliveryDate(pendIds, lastDeliveryDate);
            List<ShipmentPlanOrderItem> shipmentPlanOrderItemList = shipmentPlanOrderItemLookup.findByPendIdList(pendIds);
            // PD 变更最新交期日期推送
            if (CollUtil.isNotEmpty(shipmentPlanOrderItemList)) {
                for (ShipmentPlanOrderItem shipmentPlanOrderItem : shipmentPlanOrderItemList) {
                    ShipmentPlanOrderDTO shipmentPlanOrderDTO = new ShipmentPlanOrderDTO();
                    shipmentPlanOrderDTO.setId(shipmentPlanOrderItem.getId().id());
                    shipmentPlanOrderDTO.setPdNo(shipmentPlanOrderItem.getPdNo());
                    ShipmentPlanOrderItemDTO shipmentPlanOrderItemDTO = new ShipmentPlanOrderItemDTO();
                    shipmentPlanOrderItemDTO.setLastDeliveryDate(lastDeliveryDate);
                    shipmentPlanOrderDTO.setItem(shipmentPlanOrderItemDTO);
                    shipmentPlanOrderProducer.pdUpdate(shipmentPlanOrderDTO);
                }
            }
        }
        qualityApplyOrderLookup.updateDeliveryInfo(poNo, lastDeliveryDate, changeDeliveryDate.getRemark());
    }

    /**
     * 校验日期是否正确
     *
     * @param confirmDeliveryDate 日期
     * @param reviewDateBeforeNow 错误码
     * @param purchaseOrder       采购单
     */
    private void validateBeforeUpdate(PurchaseOrderDeliveryDateLog confirmDeliveryDate, BizErrorCode reviewDateBeforeNow, PurchaseOrder purchaseOrder) {
        if (Objects.isNull(confirmDeliveryDate.getDeliveryDate()) || confirmDeliveryDate.getDeliveryDate().isBefore(LocalDate.now())) {
            throw new ApiException(reviewDateBeforeNow);
        }
        if (PurchaseOrderStatusEnum.PENDING.equals(purchaseOrder.getStatus())
                || PurchaseOrderStatusEnum.REJECTED.equals(purchaseOrder.getStatus())
                || PurchaseOrderStatusEnum.VOID.equals(purchaseOrder.getStatus())) {
            throw new ApiException(BizErrorCode.PO_STATUS_IS_INCORRECT);
        }
    }

    /**
     * 更新通知状态的通用方法
     */
    private void updateNotice(PurchaseOrder purchaseOrder, List<PurchaseOrderNotice> purchaseOrderNotices,
                              PurchaseChangeTypeEnum noticeType, PurchaseNoticeStatusEnum status) {
        Optional<PurchaseOrderNotice> optionalNotice = purchaseOrderNotices.stream()
                .filter(notice -> notice.getNoticeType().equals(noticeType.getCode()))
                .findFirst();

        PurchaseOrderNotice orderNotice = optionalNotice.orElseGet(() ->
                PurchaseOrderNotice.bulidPurchaseOrderNotice(noticeType, purchaseOrder));

        if (orderNotice.getId() == null) {
            orderNotice.setNoticeStatus(status.getCode());
            purchaseOrderNoticeRepository.savePurchaseOrderNotice(orderNotice);
        } else {
            orderNotice.setNoticeStatus(status.getCode());
            purchaseOrderNoticeRepository.updateByIdAndVersion(orderNotice);
        }
    }

    /**
     * 统计采购单状态数量
     */

    public Map<Integer, Object> countByStatus() {
        Set<Integer> userIdSet = userDataPermissionService.getDataPermission();
        Map<Integer, Object> map = purchaseOrderLookup.countByStatus(userIdSet);
        Arrays.stream(PurchaseOrderStatusEnum.values()).forEach(status -> {
            if (!map.containsKey(status.getValue())) {
                map.put(status.getValue(), 0);
            }
        });
        // 为所有采购单数量之和
        map.put(PurchaseOrderConstants.ALL_STATUS_COUNT, map.values().stream().map(e -> Integer.parseInt(e.toString())).reduce(0, Integer::sum));
        return map;
    }

    /**
     * 作废采购单
     *
     * @param purchaseOrderId 采购单ID
     */
    @LogRecord(module = LogModule.PURCHASE_ORDER_LOG_MODULE
            , type = LogModule.CommonDesc.VOID_OPERATOR
            , desc = LogModule.CommonDesc.VOID_DESC)
    @Transactional(rollbackFor = Exception.class)
    public void voidPurchaseOrder(Integer purchaseOrderId) {
        PurchaseOrder purchaseOrder = purchaseOrderLookup.findById(new PurchaseOrderId(purchaseOrderId))
                .orElseThrow(() -> new ApiException(BizErrorCode.DATA_DOES_NOT_EXIST));
        // 校验状态是否为 PENDING
        if (!PurchaseOrderStatusEnum.PENDING.getValue().equals(purchaseOrder.getStatus().getValue())) {
            throw new ErrorCodeException(BizErrorCode.Purchase_Order_Status_IS_NOT_PENDING, purchaseOrder.getPoNo());
        }
        PurchaseOrder oldPurchaseOrder = SerializationUtils.clone(purchaseOrder);
        purchaseOrder.setStatus(PurchaseOrderStatusEnum.VOID);
        purchaseOrderRepository.updateById(purchaseOrder);
        LogRecordContextHolder.putRecordData(purchaseOrder.getId().id().toString(), oldPurchaseOrder, purchaseOrder);
    }


    public PurchaseOrder findById(PurchaseOrder domain) {
        return purchaseOrderLookup.findById(domain.getId())
                .orElseThrow(() -> new BusinessException("DATA_DOES_NOT_EXIST"));
    }

    @LogRecord(module = LogModule.PURCHASE_ORDER_LOG_MODULE
            , type = LogModule.PurchaseOrderOperatorLogModule.EXPORT_SN_CODE
            , desc = LogModule.PurchaseOrderOperatorLogModule.EXPORT_SN_CODE_DESC)
    public void saveExportLog(Integer poId) {
        LogRecordContextHolder.putRecordData(poId.toString(), new Object(), new Object());
    }

    public PurchaseOrderVo getPurchaseOrderVo(String poNo) throws IOException {
        PurchaseOrderVo purchaseOrderVo = new PurchaseOrderVo();
        PurchaseOrder purchaseOrder = purchaseOrderLookup.findByPoNo(poNo).orElseThrow(() -> new ApiException(BizErrorCode.DATA_DOES_NOT_EXIST));
        purchaseOrderVo.setSnEnd(purchaseOrder.getSnEnd());
        purchaseOrderVo.setSnStart(purchaseOrder.getSnStart());
        ByteArrayOutputStream baos = CustomBarcodePDFUtil.generateBarcodePdfStream(List.of(purchaseOrderVo.getSnEnd()), "30MM");
        purchaseOrderVo.setSnImage(CustomBarcodePDFUtil.pdfOSToBase64Image(baos));
        return purchaseOrderVo;

    }

    public PurchaseOrderProductInfo getPurchaseProductVo(String poNo) {
        return purchaseOrderProductInfoLookup.findByPoNo(poNo);
    }

    public List<PurchaseOrder> getDetail(Collection<Integer> idList, Collection<String> poNoList) {
        Map<PurchaseOrderId, PurchaseOrder> purchaseOrderMap;
        if (CollUtil.isNotEmpty(idList)) {
            purchaseOrderMap = purchaseOrderLookup.findByPoIds(idList.stream().distinct().map(PurchaseOrderId::new).toList());
        } else if (CollUtil.isNotEmpty(poNoList)) {
            purchaseOrderMap = purchaseOrderLookup.findByPoNoList(poNoList.stream().distinct().toList())
                    .stream().collect(Collectors.toMap(PurchaseOrder::getId, Function.identity()));
        } else {
            throw new BusinessException("params.is.not.null");
        }
        if (CollUtil.isNotEmpty(purchaseOrderMap)) {
            // 组装数据
            List<PurchaseOrderId> ids = purchaseOrderMap.keySet().stream().toList();
            Map<PurchaseOrderId, PurchaseOrderPriceInfo> priceInfoMap = purchaseOrderPriceInfoLookup.findByPoIds(ids);
            Map<PurchaseOrderId, PurchaseOrderSupplierInfo> supplierInfoMap = purchaseOrderSupplierInfoLookup.findByPoIds(ids);
            for (Map.Entry<PurchaseOrderId, PurchaseOrder> entry : purchaseOrderMap.entrySet()) {
                PurchaseOrderId key = entry.getKey();
                PurchaseOrder value = entry.getValue();
                PurchaseOrderPriceInfo priceInfo = priceInfoMap.get(key);
                // 采购总价
                value.setPurchaseAmount(priceInfo.getInclusiveTaxPrice().multiply(new BigDecimal(value.getNormalQty())).setScale(2, RoundingMode.HALF_UP));
                value.setSupplierInfo(supplierInfoMap.get(key));
            }
            return purchaseOrderMap.values().stream().toList();
        }
        return Collections.emptyList();
    }

    public List<PurchaseOrder> listInner(PurchaseOrderQuery query) {
        Collection<Integer> idList = query.getPoIdList();
        Collection<String> poNoList = query.getPoNoList();
        if (CollUtil.isNotEmpty(idList)) {
            return purchaseOrderLookup.findByPoIds(idList.stream().distinct().map(PurchaseOrderId::new).toList()).values().stream().toList();
        } else if (CollUtil.isNotEmpty(poNoList)) {
            return purchaseOrderLookup.findByPoNoList(poNoList.stream().distinct().toList());
        } else {
            return purchaseOrderLookup.findList(query);
        }
    }

    /**
     * PCD 变更更新挪出PO
     */
    @Transactional(rollbackFor = Exception.class)
    public void updatePoByPcd(PurchaseOrder purchaseOrder, Map<Integer, ShipmentPlanChangeOutDetail> outDetailMap,boolean shareQtyUpdated) {

        // 按PR ID 拆分 OUT 明细
        log.info("updatePoByPcd:{}", purchaseOrder);

        // 关联的全部PR 列表
        List<String> prNos = purchaseOrder.getItems().stream().map(PurchaseOrderItem::getPrNo).toList();
        // 获取 待出货计划 列表
        List<PendShipmentPlan> pengShipmentPlanList = pendShipmentPlanLookup.findByPrNoList(prNos);

        // Map<Pr，plan> PO 关联的全量数据
        Map<String, PendShipmentPlan> prPlanMap = pengShipmentPlanList.stream().collect(Collectors.toMap(e -> e.getPurchaseOrderItem().getPrNo(), Function.identity()));

        // 补全 PO 明细 待收货数量，挪出数量
        List<PurchaseOrderItem> items = purchaseOrder.getItems();


        items.forEach(item -> {
            PendShipmentPlan pendShipmentPlan = prPlanMap.get(item.getPrNo());
            if (pendShipmentPlan != null) {
                item.setUnReceiveQty(pendShipmentPlan.getPendQty());
            }

            // 获取 移出数量，需要更新的 PO 明细 设置 OUT 数量 ，否则设置为 0
            ShipmentPlanChangeOutDetail outDetail = outDetailMap.get(item.getPrId());
            if (outDetail != null) {
                item.setOutQty(outDetail.getMoveOutQty());
            } else {
                item.setOutQty(0);
            }
        });

        // 判断 PO 明细的 剩余数量 是否为 0
        // 剩余数 = 未收货数量 - 挪出数量
        boolean b = purchaseOrder.getItems().stream().allMatch(PurchaseOrderItem::allMoveOut);
        if (b) {
            // PO 明细 PSKU数量全部移除，原单状态变更为【已作废】
            purchaseOrder.setStatus(PurchaseOrderStatusEnum.VOID);
        } else {
            // PO 明细 PSKU数量部分移除， PO单合同上传状态变更为【待上传】
            purchaseOrder.setUploadFlag(0);
        }

        // 待出货计划 数量更新
        prPlanMap.forEach((k, v) -> {
            ShipmentPlanChangeOutDetail shipmentPlanChangeOutDetail = outDetailMap.get(v.getPurchaseOrderItem().getPrId());
            // 为空说明待出货计划无需更新
            if (shipmentPlanChangeOutDetail == null) {
                return;
            }
            v.setPendQty(shipmentPlanChangeOutDetail.getMoveOutQty());
            pendShipmentPlanRepository.updatePendingQtyAndPrQty(v);
        });

        // 更新PO明细
        items.forEach(item -> {
            item.setApplicationQty(item.getApplicationQty() - item.getOutQty());
            item.setPurchaseQty(item.getPurchaseQty() - item.getOutQty());
        });
        int sum = items.stream().mapToInt(PurchaseOrderItem::getApplicationQty).sum();
        purchaseOrder.setPoQty(sum);
        // 要么 0 / 全部挪出
        purchaseOrder.setSpareQty( shareQtyUpdated ? 0 : purchaseOrder.getSpareQty());
        purchaseOrder.setNormalQty( purchaseOrder.getPoQty() - purchaseOrder.getSpareQty());

        // PO 明细更新 、PO 更新
        purchaseOrderRepository.updateById(purchaseOrder);
        purchaseOrderItemRepository.updateBatchById(items);
    }




    @LogRecord(module = LogModule.PURCHASE_ORDER_LOG_MODULE
            , type = LogModule.CommonDesc.INSERT_OPERATOR
            , desc = LogModule.CommonDesc.INSERT_DESC)
    @Transactional(rollbackFor = Exception.class)
    public List<PurchaseOrderItem> createPurchaseOrderByPcd(PurchaseOrder purchaseOrder, Integer shareQty, Product product, Integer auditUserId, PurchaseOrderPriceInfo price) {
        // 因为MQ回调后无法获取当前用户，所以需要获取系统用户传给PO单
        Operator operator = null;
        if (Objects.nonNull(auditUserId)) {
            operator = Optional.of(auditUserId).map(OperatorId::new).map(o -> {
                Operator op = new Operator();
                op.setOperatorId(o);
                return op;
            }).get();
        } else {
            operator = operatorLookup.findSystemUser();
        }

        // 根据供应商代码查询供应商信息
        List<String> supplierCodes = List.of(purchaseOrder.getSupplierCode());
        Map<String, PurchaseSupplierDetailsResp> supplierMap = purchaseSupplierLookup.findBySupplierCodes(supplierCodes);
        log.info("SRM获取到的供应商原始数据：【{}】", JSON.toJSONString(supplierMap));

        List<PurchaseOrderItem> purchaseOrderItems = new ArrayList<>();
        //查询最新汇率
//        Map<String, LatestFluctuateExchangeRateResponse> rateMap = rateLookup.findLatestExchangeRate("CNY");

        try {
            purchaseOrder.init(operator);

            // 重计算备品率
            purchaseOrder.resetSpareQty(shareQty);

            //设置SN信息 设置采购单sn码，并更新品牌最新sn码
            purchaseOrderSnService.setPurchaseSnInfo(purchaseOrder, product.getSn());

            //构建辅助信息（产品快照、供应商快照、产品价目快照）
            buildAuxiliary(purchaseOrder, supplierMap, product, null, operator, price);

            Integer poId = purchaseOrderRepository.savePurchaseOrder(purchaseOrder);

            // 回填poId
            purchaseOrder.setPoId(poId);
            // 构建并累积 PurchaseOrderItemPO
            purchaseOrderItems.addAll(purchaseOrderItemRepository.batchSavePurchaseOrderItem(purchaseOrder.getItems(), operator, poId, purchaseOrder.getPoNo()));
            // 插入辅助信息
            insertAuxiliaryInfo(poId, purchaseOrder);
            LogRecordContextHolder.putRecordData(poId.toString(), null, purchaseOrder);

        } catch (Exception e) {
            // 记录异常日志，确保事务回滚
            log.error("Error occurred during batch creation of purchase orders", e);
            throw e;
        }
        return purchaseOrderItems;
    }
}
