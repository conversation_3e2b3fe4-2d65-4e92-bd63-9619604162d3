package com.renpho.erp.pms.application.purchaserequest;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.renpho.erp.bpm.client.process.vo.ProcessInstanceRecordVO;
import com.renpho.erp.log.repository.annotation.LogRecord;
import com.renpho.erp.log.repository.support.LogRecordContextHolder;
import com.renpho.erp.pms.Infrastructure.feign.product.ProductPriceLookup;
import com.renpho.erp.pms.Infrastructure.feign.supplier.RemoteProductBusinessManagerFeign;
import com.renpho.erp.pms.Infrastructure.form.PurchaseRequisitionOrderForm;
import com.renpho.erp.pms.Infrastructure.persistence.bpm.po.converter.BpmBatchConverter;
import com.renpho.erp.pms.Infrastructure.persistence.purchaserequest.po.PurchaseRequestPO;
import com.renpho.erp.pms.Infrastructure.persistence.purchaserequest.po.converter.PurchaseRequestConverter;
import com.renpho.erp.pms.Infrastructure.processinstance.ProcessInstanceService;
import com.renpho.erp.pms.application.bpm.BpmBatchService;
import com.renpho.erp.pms.application.purchase.oplog.LogModule;
import com.renpho.erp.pms.application.purchase.service.PurchaseOrderService;
import com.renpho.erp.pms.domain.bpm.BpmBatch;
import com.renpho.erp.pms.domain.commom.ReceiptGenerationTypeEnum;
import com.renpho.erp.pms.domain.processinstance.ApproveResultEnum;
import com.renpho.erp.pms.domain.processinstance.ProcessInstanceId;
import com.renpho.erp.pms.domain.product.Product;
import com.renpho.erp.pms.domain.product.ProductId;
import com.renpho.erp.pms.domain.productprice.ProductPrice;
import com.renpho.erp.pms.domain.purchaseorder.PurchaseOrder;
import com.renpho.erp.pms.domain.purchaseorder.PurchaseOrderItem;
import com.renpho.erp.pms.domain.purchaseorder.PurchaseOrderPriceInfo;
import com.renpho.erp.pms.domain.purchaserequest.*;
import com.renpho.erp.pms.domain.saleschannel.SalesChannel;
import com.renpho.erp.pms.domain.store.Store;
import com.renpho.erp.pms.exception.BusinessException;
import com.renpho.erp.pms.model.purchaseorder.enums.PurchaseRequestType;
import com.renpho.erp.srm.client.vo.ProductBusinessManagerResponse;
import com.renpho.karma.exception.ApiException;
import com.renpho.karma.exception.error.PlatformErrorCode;
import com.renpho.karma.i18n.I18nMessageKit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 请购单查询 service
 *
 * <AUTHOR>
 * @since 2025/3/17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PurchaseRequestService {
    //正常补货
    private final String NORMAL_REPLENISHMENT = "0";


    private final PurchaseRequestLookup purchaseRequestLookup;
    private final PurchaseRequestItemLookup purchaseRequestItemLookup;

    private final ProcessInstanceService processInstanceService;
    private final PurchaseOrderService purchaseOrderService;
    private final BpmBatchService bpmBatchService;

    private final PurchaseRequestRepository purchaseRequestRepository;
    private final PurchaseRequestItemRepository purchaseRequestItemRepository;

    private final RemoteProductBusinessManagerFeign remoteProductBusinessManagerFeign;
    private final ProductPriceLookup productPriceLookup;

    private final BpmBatchConverter bpmBatchConverter;
    private final PurchaseRequestConverter purchaseRequestConverter;

    /**
     * PCD 生成PR 单
     */
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(module = LogModule.PURCHASE_REQUEST_ORDER_LOG_MODULE, type = LogModule.CommonDesc.INSERT_OPERATOR, desc = LogModule.CommonDesc.INSERT_DESC)
    public PurchaseRequest addByPcdWithoutBpm(PurchaseRequest domain, SalesChannel salesChannel, Store store, Product product) {
        LocalDateTime now = LocalDateTime.now();
        domain.setStatus(PurchaseRequestStatus.COMPLETED);
        domain.setSubmitTime(now);
        domain.setReviewTime(now);

        // 填充数据
        domain.fillExtData(salesChannel, store, product);

        PurchaseRequest add = purchaseRequestRepository.add(domain);

        // 反向填充
        add.fillExtData(salesChannel, store, product);

        PurchaseRequestPO po = purchaseRequestConverter.toPo(add);

        LogRecordContextHolder.putRecordData(String.valueOf( add.getId().id()), null,po );

        return add;
    }

    @Transactional(rollbackFor = Exception.class)
    public PurchaseRequest add(PurchaseRequest domain) {
        domain.setStatus(PurchaseRequestStatus.PENDING);
        purchaseRequestLookup.findSalesChannelAssociations(List.of(domain));
        purchaseRequestLookup.findStoreAssociations(List.of(domain));
        purchaseRequestItemLookup.findProductAssociation(domain.getItem());
        return purchaseRequestRepository.add(domain);
    }

    @Transactional(rollbackFor = Exception.class)
    public List<PurchaseRequest> batchAdd(Collection<PurchaseRequest> domains) {
        domains.forEach(d -> d.setStatus(PurchaseRequestStatus.PENDING));
        purchaseRequestLookup.findSalesChannelAssociations(domains);
        purchaseRequestLookup.findStoreAssociations(domains);
        purchaseRequestLookup.findProductAssociations(domains);
        return purchaseRequestRepository.batchAdd(domains);
    }

    @Transactional(rollbackFor = Exception.class)
    public PurchaseRequest update(PurchaseRequest command) {
        PurchaseRequest domain = purchaseRequestLookup.findById(command.getId()).orElseThrow(() -> new ApiException(PlatformErrorCode.SYSTEM_EXCEPTION));
        if (domain.getStatus() != PurchaseRequestStatus.PENDING) {
            throw new ApiException(PlatformErrorCode.INVALID_ARGUMENT);
        }
        command.setStatus(PurchaseRequestStatus.PENDING);
        purchaseRequestLookup.findSalesChannelAssociations(List.of(command));
        purchaseRequestItemLookup.findStoreAssociation(command.getItem());
        purchaseRequestItemLookup.findProductAssociation(command.getItem());
        return purchaseRequestRepository.update(command);
    }

    @Transactional(rollbackFor = Exception.class)
    public void submit(List<PurchaseRequestId> ids) {
        List<PurchaseRequest> domains = purchaseRequestLookup.findByIds(ids);
        if (domains.stream().anyMatch(p -> p.getStatus() != PurchaseRequestStatus.PENDING)) {
            throw new ApiException(PlatformErrorCode.INVALID_ARGUMENT);
        }
        //提交 BPM
        for (PurchaseRequest p : domains) {
            String instanceId = processInstanceService.startProcessInstance(p.getId().id().toString(),
                    new PurchaseRequisitionOrderForm(),null);
            p.setProcessInstanceId(new ProcessInstanceId(instanceId));
            p.setReviewStatus(ApproveResultEnum.ON_REVIEW);
            p.setSubmitTime(LocalDateTime.now());
        }
        purchaseRequestRepository.updateStatus(domains, PurchaseRequestStatus.ON_REVIEW);
    }


    @Transactional(rollbackFor = Exception.class)
    public void cancel(List<PurchaseRequestId> ids) {
        List<PurchaseRequest> domains = purchaseRequestLookup.findByIds(ids);
        if (domains.stream().anyMatch(p -> p.getStatus() != PurchaseRequestStatus.PENDING)) {
            throw new ApiException(PlatformErrorCode.INVALID_ARGUMENT);
        }
        purchaseRequestRepository.updateStatus(domains, PurchaseRequestStatus.CANCEL);
    }

    /**
     * 批量审核前置校验（包含批量审核）
     *
     * @param ids                      pr单ID
     * @param expectedDeliveryTimePass 期望交期是否通过二次提醒，true-通过，false-不通过（首次调用传null）
     * @param purchaseQtyPass          采购数量是否通过二次提醒，true-通过，false-不通过（首次调用传null）
     */
    public Map<String, Object> checkBeforeReview(List<PurchaseRequestId> ids, Boolean expectedDeliveryTimePass,
                                                 Boolean purchaseQtyPass) {
        //是否批量审核
        boolean isBatchReview = ids.size() > 1;
        PurchaseRequestQuery query = new PurchaseRequestQuery();
        query.setIds(ids);
        //请购单列表
        List<PurchaseRequest> requestList = purchaseRequestLookup.findByIds(ids);
        Map<PurchaseRequestId, String> prMap = requestList.stream().collect(Collectors.toMap(PurchaseRequest::getId,
                PurchaseRequest::getPrNo));

        Map<ProductId, List<PurchaseRequestItem>> itemsMap = purchaseRequestItemLookup.findMapByIds(query);
        Set<ProductId> productIds = itemsMap.keySet();
        //先校验pr单的审批人是否是当前登录用户
        List<String> instanceIds = requestList.stream()
                .map(p -> p.getProcessInstanceId().id())
                .filter(StringUtils::isNotBlank).toList();
        //审批人不是当前登录用户的审批单
        List<String> prIds = processInstanceService.getNotCurrentUserReviewOrders(instanceIds);
        if (CollectionUtils.isNotEmpty(prIds)) {
            List<String> poNoList = new ArrayList<>();
            prIds.forEach(prId -> {
                String prNo = prMap.get(new PurchaseRequestId(Integer.parseInt(prId)));
                if (Objects.nonNull(prNo)) {
                    poNoList.add(prNo);
                }
            });
            throw new BusinessException("pr.not.current.user.approval", poNoList);
        }

        //产品商务信息
        Map<ProductId, ProductBusinessManagerResponse> productManagerMap = remoteProductBusinessManagerFeign.findByProductIds(productIds);
        //产品价目信息
        Map<ProductId, List<ProductPrice>> productPriceListMap = productPriceLookup.getProductPriceMap(productIds);

        Map<String, Object> resMap = new HashMap<>();
        resMap.put("expectedDeliveryTimePass", expectedDeliveryTimePass);
        resMap.put("purchaseQtyPass", purchaseQtyPass);

        //前置校验
        for (ProductId productId : itemsMap.keySet()) {
            List<PurchaseRequestItem> items = itemsMap.get(productId);
            PurchaseRequestItem item = items.get(0);

//            if (isBatchReview) {
//                if (expectedDeliveryTimePass == null) {
//                    //1、校验相同PSKU的PR单中的期望交期是否在7天范围内
//                    boolean within7Days = item.isRangeWithin7Days(items);
//                    if (!within7Days) {
//                        throw new ErrorCodeException(BizErrorCode.EXPECTED_DELIVERY_RANGE_EXCEEDS_7_DAYS);
//                    }
//                } else if (!expectedDeliveryTimePass) {
//                    resMap.put("pass", false);
//                    return resMap;
//                }
//            }

            //1、PSKU产品价目校验
            isValidProductPrice(productId, productPriceListMap);
            //2、PSKU补货状态校验
            isValidReplenishment(productManagerMap, productId);

            if (purchaseQtyPass == null) {
                //3、汇总采购数量是否满足该SKU的MOQ
                Integer moq = Optional.ofNullable(productManagerMap.get(productId))
                        .map(ProductBusinessManagerResponse::getMoq)
                        .orElse(0);
                boolean metMoq = item.isMetMoq(moq, items);
                if (!metMoq) {
                    resMap.put("pass", false);
                    resMap.put("purchaseQtyPass", false);
                    resMap.put("purchaseQtyErrMsg", I18nMessageKit.getMessage("pr.purchase.qty.not.met.moq"));
                    return resMap;
                }
            } else if (!purchaseQtyPass) {
                resMap.put("pass", false);
                return resMap;
            }
        }


        //保存PR单与批次的关联关系
        List<BpmBatch> bpmBatches = bpmBatchConverter.purchaseRequestToBpmBatchList(requestList);
        bpmBatchService.batchInsert(bpmBatches);

        resMap.put("pass", true);
        return resMap;
    }

    /**
     * 校验产品价目
     *
     * @param productId           产品ID
     * @param productPriceListMap 产品价目列表
     */
    private void isValidProductPrice(ProductId productId, Map<ProductId, List<ProductPrice>> productPriceListMap) {
        // 判断PSKU是有【已启用】的产品价目，若无已启用价目，则不允许审核通过
        Optional.ofNullable(productPriceListMap.get(productId))
                .filter(list -> list.stream().anyMatch(p -> Objects.equals(PriceStatus.ENABLED, p.getStatus())))
                .orElseThrow(() -> new BusinessException("pr.psku.price.no.enabled"));
    }

    /**
     * 校验补货状态
     *
     * @param productManagerMap 产品商务信息
     * @param productId         产品ID
     */
    private void isValidReplenishment(Map<ProductId, ProductBusinessManagerResponse> productManagerMap, ProductId productId) {
        //4、判断PSKU补货状态是否为【正常补货】，若补货状态不是正常补货，则不允许审核通过
        Optional.ofNullable(productManagerMap.get(productId))
                .map(ProductBusinessManagerResponse::getReplenishmentStatus)
                .filter(s -> !NORMAL_REPLENISHMENT.equals(s))
                .ifPresent(s -> {
                    throw new BusinessException("pr.psku.replenishment.not.normal");
                });
    }

    /**
     * pr单合并成po单
     *
     * @param purchaseRequests pr单
     * @return po单
     */
    private List<PurchaseOrder> mergeToPO(List<PurchaseRequest> purchaseRequests) {
        // 相同的PSKU，相同的供应商，相同的采购人员及计划人员，请购类型非【返修】类型，需合并同一个PO
        Map<String, List<PurchaseRequest>> map = purchaseRequests.stream()
                .collect(Collectors.groupingBy(p -> {

                    //请购类型
                    String type = PurchaseRequestType.REPAIR.equals(p.getType()) ? "true" : "false";

                    return String.join(",",
                            p.getItem().getProduct().getPsku(),
                            p.getItem().getProduct().getSupplierCode(),
                            p.getItem().getProduct().getProcurementStaffId().toString(),
                            p.getItem().getProduct().getPlanningStaffId().toString(), type);
                }));

        return map.values()
                .stream()
                .map(purchaseRequestConverter::toPurchaseOrder)
                .toList();
    }


    /**
     * 更新请购单状态并保存PR单审核日志
     *
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusAndSaveLog(PurchaseRequest command, ApproveResultEnum reviewStatus,Integer auditUserId) {
        Optional<PurchaseRequest> oldData = purchaseRequestLookup.findById(command.getId());
        // 更新请购单状态
        log.info("更新请购单状态---开始");
        purchaseRequestRepository.updateStatusAndUser(List.of(command), reviewStatus.getPurchaseRequestStatus(),auditUserId);
        log.info("更新请购单状态---结束；{},purchaseRequestStatus：{}", JSON.toJSONString(command),
                reviewStatus.getPurchaseRequestStatus());
        Optional<PurchaseRequest> newData = purchaseRequestLookup.findById(command.getId());
        //记录日志
        if (reviewStatus.equals(ApproveResultEnum.CANCEL)){
            SpringUtil.getBean(PurchaseRequestService.class).saveWithDrawLog(String.valueOf(command.getId().id()), oldData.get(), newData.get());
        }else {
            SpringUtil.getBean(PurchaseRequestService.class).saveReviewLog(String.valueOf(command.getId().id()), oldData.get(), newData.get());
        }
    }

    /**
     * 保存撤回日志
     * @param prId prId
     * @param oldData 旧值
     * @param newData 新值
     */
    @LogRecord(module = "PURCHASE_REQUEST", type = LogModule.CommonDesc.WITHDRAW_OPERATOR,
            desc = LogModule.CommonDesc.WITHDRAW_DESC)
    @Transactional(rollbackFor = Exception.class)
    public void saveWithDrawLog(String prId, PurchaseRequest oldData, PurchaseRequest newData){
        LogRecordContextHolder.putRecordData(prId, oldData, newData);
    }


    /**
     * 保存审核日志
     * @param prId prId
     * @param oldData 旧值
     * @param newData 新值
     */
    @LogRecord(module = "PURCHASE_REQUEST", type = LogModule.CommonDesc.REVIEW_OPERATOR,
            desc = LogModule.CommonDesc.REVIEW_DESC)
    @Transactional(rollbackFor = Exception.class)
    public void saveReviewLog(String prId, PurchaseRequest oldData, PurchaseRequest newData){
        LogRecordContextHolder.putRecordData(prId, oldData, newData);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateStatusByReview(PurchaseRequest command, ApproveResultEnum reviewStatus,Integer auditUserId) {
        if (command.getItem().hasPO()) {
            return;
        }

        //更新该请购单对应批次
        BpmBatch bpmBatch = new BpmBatch(command.getPrNo(), ReceiptGenerationTypeEnum.PR, command.getProcessInstanceId());
        List<BpmBatch> batchList = bpmBatchService.updateStatusAndReturnBatchList(bpmBatch, reviewStatus);

        boolean allFinished, isBulkAudit;
        //batchList == null 代表是单个审批；单个审批通过，生成PO单
        if (CollectionUtils.isEmpty(batchList)) {
            log.info("单个审批通过：PrNo:【{}】", command.getPrNo());
            allFinished = ApproveResultEnum.PASS.equals(reviewStatus);
            isBulkAudit = false;
        } else {
            allFinished = batchList.stream().map(BpmBatch::getReviewStatus).allMatch(ApproveResultEnum.PASS::equals);
            isBulkAudit = true;
            log.info("批量审批通过：PrNo:【{}】", command.getPrNo());
        }

        //完全完结，生成PO单
        if (allFinished) {

            List<String> prNos = isBulkAudit ? batchList.stream().map(BpmBatch::getOrderNo).toList() : List.of(command.getPrNo());
            log.info("生成PO单...PrNOs:[{}]",prNos);

            List<PurchaseRequest> purchaseRequests = purchaseRequestLookup.findByPrNos(prNos);
            purchaseRequestLookup.findItemAssociations(purchaseRequests);
            purchaseRequestLookup.findProductAssociations(purchaseRequests);
            purchaseRequestLookup.findSalesChannelAssociations(purchaseRequests);
            purchaseRequestLookup.findStoreAssociations(purchaseRequests);
            purchaseRequestLookup.findOperatorAssociations(purchaseRequests);

            //PR单合并PO后，生成PO单
            List<PurchaseOrder> poList = mergeToPO(purchaseRequests);

            List<PurchaseOrderItem> poItems = purchaseOrderService.batchCreatePurchaseOrder(poList,auditUserId);

            Map<Integer, PurchaseOrderItem> poItemMap = poItems.stream().collect(Collectors.toMap(PurchaseOrderItem::getPrId, Function.identity()));

            //记录pr单的po单号
            List<PurchaseRequestItem> purchaseRequestItems = purchaseRequests.stream()
                    .map(PurchaseRequest::getItem)
                    .peek(i -> i.setPoInfo(poItemMap))
                    .toList();

            purchaseRequestItemRepository.batchSetPoInfo(purchaseRequestItems);
        }
        SpringUtil.getBean(PurchaseRequestService.class).updateStatusAndSaveLog(command, reviewStatus,auditUserId);
    }

    /**
     * 同步PR单审核结果
     *
     * @param instIds 审批实例IDs
     * @param batchNo 批次号
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncPrApprovalResult(List<String> instIds, String batchNo) {
        List<String> instanceIds = new ArrayList<>();

        if (CollectionUtils.isEmpty(instIds)) {
            //待同步的pr单列表
            List<BpmBatch> prList = bpmBatchService.findNoApprovalResultList(batchNo, ReceiptGenerationTypeEnum.PR);
            instanceIds = CollectionUtils.emptyIfNull(prList).stream().map(BpmBatch::getProcessInstanceId)
                    .map(ProcessInstanceId::id).toList();
        }

        Stream.of(instanceIds, CollectionUtils.emptyIfNull(instIds))
                .flatMap(Collection::stream)
                .filter(StringUtils::isNotBlank).distinct()
                .map(ProcessInstanceId::new)
                .forEach(instId -> processInstanceService.findById(instId).map(ProcessInstanceRecordVO::getResult)
                        .ifPresent(result -> {
                            ApproveResultEnum reviewStatus = ApproveResultEnum.get(result);

                            //审批状态变化，则更新PR状态
                            if (!reviewStatus.equals(ApproveResultEnum.ON_REVIEW)) {
                                purchaseRequestLookup.findByInstanceId(instId)
//                                        .filter(pr -> pr.getReviewStatus().equals(ApproveResultEnum.ON_REVIEW))
                                        .ifPresent(pr -> {
                                            pr.setReviewStatus(reviewStatus);
                                            SpringUtil.getBean(PurchaseRequestService.class).updateStatusByReview(pr, reviewStatus,null);
                                        });
                            }
                        }));
    }

    /**
     * PCD 下推PR 单
     */
    public PurchaseOrder autoCreatePoByPcd(PurchaseRequest purchaseRequest,Integer shareQty,Product product, Integer userId, PurchaseOrderPriceInfo priceInfo){
//       人员逻辑 先注释
//        purchaseRequestLookup.findOperatorAssociations(purchaseRequests);

        //PR单 ==》 PO 单
        PurchaseOrder po = purchaseRequestConverter.toPurchaseOrder(List.of(purchaseRequest));

        List<PurchaseOrderItem> poItems = purchaseOrderService.createPurchaseOrderByPcd(po,shareQty,product,userId,priceInfo);

        Map<Integer, PurchaseOrderItem> poItemMap = poItems.stream().collect(Collectors.toMap(PurchaseOrderItem::getPrId, Function.identity()));

        //记录pr单的po单号
        purchaseRequest.getItem().setPoInfo(poItemMap);

        purchaseRequestItemRepository.batchSetPoInfo(List.of(purchaseRequest.getItem()));
        return po;
    }
}
