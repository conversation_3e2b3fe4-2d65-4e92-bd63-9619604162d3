#amazoncorretto:17-alpine3.20-full 是一个基于 Amazon Corretto 17 和 Alpine Linux 3.18 的
#轻量级 Docker 镜像，适用于资源受限和注重安全性的环境
#生产环境可以选择切换为这个镜像amazoncorretto:17.0.12-al2023，它是一个基于 Amazon Linux 2 的版本，适合 AWS 环境中的优化需求
FROM renpho.harbor.com/new-erp-common/amazoncorretto:17.0.12-al2023
# 维护者信息
LABEL maintainer="Hollis Huo"

# 构建镜像时传参数据
ARG SERVICE_NAME
ARG SERVICE_PORT
ARG JAVA_OPTS

# 设置环境变量-运行时也可传参进来耍哈
ENV SERVICE_NAME=${SERVICE_NAME}
ENV SERVICE_JAR=${SERVICE_NAME}.jar
ENV SERVICE_PORT=${SERVICE_PORT}
ENV JAVA_OPTS=${JAVA_OPTS}

# 设置工作目录
WORKDIR /

# 复制 JAR 文件到工作目录
COPY pms-start/${SERVICE_NAME}/target/${SERVICE_JAR} ./${SERVICE_JAR}

# 使用 yum 包管理器安装 fontconfig 和字体
#RUN yum install -y fontconfig dejavu-sans-fonts && yum clean all

# 刷新字体缓存
#RUN fc-cache -vf

# 对外暴露的端口号
EXPOSE ${SERVICE_PORT}

# 运行 Java 应用程序
CMD nohup java ${JAVA_OPTS} -jar ./${SERVICE_JAR}  2>&1

