package com.renpho.erp.tms.application.inbound;

import com.renpho.erp.tms.TransportationManagementSystemServeApplication;
import com.renpho.erp.tms.domain.inbound.InboundRecord;
import com.renpho.erp.tms.domain.inbound.InboundRecordId;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2025/7/31
 */
@SpringBootTest(classes = TransportationManagementSystemServeApplication.class, properties = {"spring.cloud.nacos.discovery.register-enabled=false"})
class InboundAllocationServiceTest {

    @Autowired
    private InboundAllocationService inboundAllocationService;
    @Autowired
    private InboundRecordQueryService inboundRecordQueryService;

    @Test
    void allocateFreightAndTax() {
        Optional<InboundRecord> inbound = inboundRecordQueryService.findById(new InboundRecordId(14));
        inboundAllocationService.allocateFreightAndTax(inbound.orElseThrow());
    }
}