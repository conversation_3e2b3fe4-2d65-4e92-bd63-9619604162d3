package com.renpho.erp.tms.application.transportrequest;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.renpho.erp.pms.model.shipmentplan.model.ShipmentPlanOrderDTO;
import com.renpho.erp.tms.TransportationManagementSystemServeApplication;
import com.renpho.erp.tms.adapter.stream.transportrequest.cmd.AddTransportRequestCmd;
import com.renpho.erp.tms.adapter.stream.transportrequest.cmd.converter.TransportRequestCmdStreamConverter;
import com.renpho.erp.tms.domain.transportrequest.PushTask;
import com.renpho.erp.tms.domain.transportrequest.PushTaskStatus;
import com.renpho.erp.tms.domain.transportrequest.TransportRequest;
import com.renpho.erp.tms.domain.transportrequest.TransportRequestId;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;

/**
 * <AUTHOR>
 * @since 2025/7/22
 */
@SpringBootTest(classes = TransportationManagementSystemServeApplication.class, properties = {"spring.cloud.nacos.discovery.register-enabled=false"})
class PushTaskServiceTest {

    @Autowired
    private PushTaskService pushTaskService;
    @Autowired
    private TransportRequestQueryService transportRequestQueryService;
    @Autowired
    private TransportRequestCmdStreamConverter transportRequestCmdStreamConverter;
    @Autowired
    private ObjectMapper mapper;

    @Test
    @Rollback
    void init() {
        TransportRequest tr = transportRequestQueryService.findDetailById(new TransportRequestId(1));
        pushTaskService.init(tr);
    }

    @Test
    @Rollback
    void createTrFailed() throws JsonProcessingException {
        ShipmentPlanOrderDTO vo = mapper.readValue("{\"confirmTime\":\"2025-07-30 04:21:41\",\"expectedPdDate\":\"2025-08-08\",\"id\":203,\"item\":{\"boxPcs\":10,\"boxQty\":10.00,\"boxType\":true,\"destWarehouseCode\":\"2020\",\"destWarehouseId\":46,\"destWarehouseType\":3,\"expectedPdDate\":\"2025-08-08\",\"firstLegMethod\":\"Sea-Exp\",\"firstLegType\":\"Direct\",\"fnSku\":\"X000000002\",\"lastDeliveryDate\":\"2025-09-01\",\"logisticsType\":\"Other\",\"pdQty\":100,\"shipWarehouseMethod\":\"NOT-APPLICABLE\"},\"pdNo\":\"PD2507300003\",\"pdQty\":100,\"poNo\":\"PO2507300014\",\"psku\":\"PUS-ES-CS20M-RD-A2-WMT\",\"purchaseOrder\":{\"buyerId\":4211,\"companyId\":68,\"confirmedDeliveryDate\":\"2025-09-01\",\"createTime\":\"2025-07-30 03:30:46\",\"id\":192,\"normalQty\":264,\"poNo\":\"PO2507300014\",\"poQty\":300,\"priceInfo\":{\"currency\":\"USD\",\"exchangeRate\":7.5500,\"id\":151,\"inclusiveTaxPrice\":1314.0000,\"poId\":192,\"poNo\":\"PO2507300014\",\"priceNumber\":\"JM2507300001\",\"rateId\":226638,\"taxRate\":0},\"productId\":263,\"productInfo\":{\"boxHeightMetric\":300.00,\"boxLengthMetric\":500.00,\"boxWidthMetric\":400.00,\"brandName\":\"大疆\",\"grossWeightMetric\":0.800,\"grossWeightPerBoxMetric\":20.000,\"hazardousProList\":[],\"modelName\":\"006-AAB11\",\"numberOfUnitsPerBox\":10,\"packagingHeightMetric\":20.00,\"packagingLengthMetric\":20.00,\"packagingWidthMetric\":20.00,\"productDescription\":\"[\\\"1\\\"]\",\"productHeightMetric\":10.00,\"productId\":263,\"productLengthMetric\":10.00,\"productQty\":1,\"productUnit\":\"1\",\"productWeightMetric\":0.600,\"productWidthMetric\":10.00,\"psku\":\"PUS-ES-CS20M-RD-A2-WMT\"},\"psku\":\"PUS-ES-CS20M-RD-A2-WMT\",\"purchaseAmount\":346896.00,\"request\":{\"boxQty\":10.00,\"boxType\":true,\"planningsStaffId\":4211,\"prNo\":\"PR2507300005\",\"salesChannelId\":2,\"salesStaffId\":4204,\"type\":\"SC_RETURN\"},\"spareQty\":36,\"spareRate\":120,\"supplierId\":82,\"supplierInfo\":{\"advancePaymentRatio\":22.00,\"baseTime\":1,\"daysOfAccountingPeriod\":2,\"description\":\"22\",\"enName\":\"Tess\",\"id\":82,\"isPrimary\":true,\"name\":\"测试工业\",\"relativeToBaseTime\":2,\"settlementType\":1,\"shortName\":\"Tess\",\"supplerFinancialId\":36,\"supplerSettlementId\":73,\"supplierCode\":\"99877\",\"supplierDeliveryLocation\":\"东莞\",\"supplierTradeTerms\":\"CFR\",\"weekOrDay\":1}},\"referenceID\":\"\",\"shipmentID\":\"\",\"siteCode\":\"US\",\"storeId\":16}", new TypeReference<>() {});
        AddTransportRequestCmd cmd = transportRequestCmdStreamConverter.toAddCmd(vo);
        pushTaskService.createTrFailed(cmd, "test");
    }

    @Test
    @Rollback
    void createInbound() {
        TransportRequest tr = transportRequestQueryService.findDetailById(new TransportRequestId(1));
        pushTaskService.createInbound(tr, PushTaskStatus.FAILURE, 0, "test");
    }

    @Test
    @Rollback
    void inbound1() {
        TransportRequest tr = transportRequestQueryService.findDetailById(new TransportRequestId(1));
        pushTaskService.inbound(tr, PushTaskStatus.FAILURE, 0, "test");
    }

    @Test
    @Rollback
    void inbound2() {
    }

    @Test
    @Rollback
    void inbound3() {
    }

    @Test
    @Rollback
    void putaway() {
    }

    @Test
    @Rollback
    void testPutaway() {
    }

    @Test
    @Rollback
    void trNo() {
    }

    @Test
    @Rollback
    void cartonFileGeneration() {
    }

    @Test
    @Rollback
    void cartonFile() {
    }

    @Test
    @Rollback
    void deliveryTime() {
    }

    @Test
    @Rollback
    void handover() {
    }

    @Test
    @Rollback
    void departure() {
    }

    @Test
    @Rollback
    void allocation1() {
        TransportRequest tr = transportRequestQueryService.findDetailById(new TransportRequestId(1));
        PushTask task = pushTaskService.allocation(tr, PushTaskStatus.PENDING, 0, "test");
        pushTaskService.allocation(tr, PushTaskStatus.FAILURE, 0, "test");
    }

    @Test
    @Rollback
    void allocation2() {
    }

    @Test
    @Rollback
    void inventory() {
    }
}
