<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>

    <!-- 配合apollo app.id, 因为log日志启动先于 apollo的启动，所以用这样的方式进行命名区别 -->
    <springProperty name="springAppName" scope="context" source="spring.application.name"/>
    <springProperty name="environment" scope="context" source="spring.profiles.active"/>
    <!-- 日志存放路径 -->
    <property name="logPath" value="/home/<USER>/logs/${springAppName}"/>
    <!-- 日志输出格式 -->
    <property name="LogPatternConsole"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} %clr(${springAppName}) ${environment} %clr(${PID:- }){magenta} %clr(-%5p [%X{tlogTraceId:-},%X{X-B3-SpanId:-}]) %clr(-){faint} %clr([%t]){faint} %clr(%logger){cyan} %clr(:){faint} %msg%n"/>
    <property name="LogPattern"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} ${springAppName} ${environment} [%X{tlogTraceId:-}] %level{5} [%thread] [%X{X-B3-SpanId:-}] %logger{20} - %msg%n"/>

    <property name="logfile" value="${LOG_FILE:-${logPath}/info.log}"/>
    <property name="debugLogfile" value="${LOG_FILE:-${logPath}/debug.log}"/>
    <property name="warnLogfile" value="${LOG_FILE:-${logPath}/warn.log}"/>
    <property name="errorLogfile" value="${LOG_FILE:-${logPath}/error.log}"/>


    <appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${LogPatternConsole}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <appender name="InfoLogFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>${LogPattern}</pattern>
            <charset>utf8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>INFO</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
        <file>${logfile}</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${logPath}/info.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <!-- 日志最大的历史 30天 -->
            <maxHistory>30</maxHistory>
            <!-- maxFileSize:这是当前活动日志文件的大小，自动分割日志，默认值是10MB，测试时可改成1KB看效果 -->
            <maxFileSize>50MB</maxFileSize>
            <!--表示所有的日志文件加起来不能超过 5GB。当达到这个上限时，Logback 会删除最旧的日志文件，以保持总大小在限制之内。-->
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    <!-- debug的日志 -->
    <appender name="DebugLogfile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>${LogPattern}</pattern>
            <charset>utf8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>DEBUG</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
        <file>${debugLogfile}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${logPath}/debug.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <!-- 日志最大的历史 30天 -->
            <maxHistory>30</maxHistory>
            <!-- maxFileSize:这是当前活动日志文件的大小，自动分割日志，默认值是10MB，测试时可改成1KB看效果 -->
            <maxFileSize>50MB</maxFileSize>
            <!--表示所有的日志文件加起来不能超过 5GB。当达到这个上限时，Logback 会删除最旧的日志文件，以保持总大小在限制之内。-->
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    <!-- warn的日志 -->
    <appender name="WarnLogfile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>${LogPattern}</pattern>
            <charset>utf8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>WARN</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
        <file>${warnLogfile}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${logPath}/warn.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <!-- 日志最大的历史 30天 -->
            <maxHistory>30</maxHistory>
            <!-- maxFileSize:这是当前活动日志文件的大小，自动分割日志，默认值是10MB，测试时可改成1KB看效果 -->
            <maxFileSize>50MB</maxFileSize>
            <!--表示所有的日志文件加起来不能超过 5GB。当达到这个上限时，Logback 会删除最旧的日志文件，以保持总大小在限制之内。-->
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    <!-- error的日志 -->
    <appender name="ErrorLogFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>${LogPattern}</pattern>
            <charset>utf8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>ERROR</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
        <file>${errorLogfile}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${logPath}/error.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <!-- 日志最大的历史 30天 -->
            <maxHistory>30</maxHistory>
            <!-- maxFileSize:这是当前活动日志文件的大小，自动分割日志，默认值是10MB，测试时可改成1KB看效果 -->
            <maxFileSize>50MB</maxFileSize>
            <!--表示所有的日志文件加起来不能超过 5GB。当达到这个上限时，Logback 会删除最旧的日志文件，以保持总大小在限制之内。-->
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="AsyncRollingLogFile" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 异步日志 https://blog.csdn.net/u011943534/article/details/119427147 -->
        <discardingThreshold>0</discardingThreshold>
        <queueSize>12345</queueSize>
        <appender-ref ref="InfoLogFile"/>
    </appender>
    <appender name="AsyncWarnLogFile" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>12345</queueSize>
        <appender-ref ref="WarnLogfile"/>
    </appender>
    <appender name="AsyncDebugLogFile" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>12345</queueSize>
        <appender-ref ref="DebugLogfile"/>
    </appender>
    <appender name="AsyncErrorLogFile" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 本文设置为0说明永远不会丢弃日志level -->
        <discardingThreshold>0</discardingThreshold>
        <queueSize>12345</queueSize>
        <appender-ref ref="ErrorLogFile"/>
    </appender>

    <root level="INFO">
        <appender-ref ref="Console"/>
        <appender-ref ref="AsyncRollingLogFile"/>
        <appender-ref ref="AsyncDebugLogFile"/>
        <appender-ref ref="AsyncWarnLogFile"/>
        <appender-ref ref="AsyncErrorLogFile"/>
    </root>

    <jmxConfigurator/>
</configuration>