CREATE TABLE `pms_purchase_change_order` (
  `id` int NOT NULL AUTO_INCREMENT,
  `pco_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '变更类型（多选逗号分割） 数量变更:QUANTITY_CHANGE 供应商信息变更:SUPPLIER_CHANGE 价格变更:PRICE_CHANGE',
  `pco_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '采购变更单号',
  `pco_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '变更原因',
  `po_id` int NOT NULL COMMENT '采购ID',
  `po_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '采购单号',
  `product_id` int NOT NULL COMMENT '产品ID',
  `psku` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'PSKU',
  `product_version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '产品版本',
  `supplier_id` int NOT NULL COMMENT '供应商ID',
  `company_id` int NOT NULL DEFAULT '0' COMMENT '甲方公司ID',
  `brand_id` int DEFAULT NULL COMMENT '品牌Id',
  `model_id` int DEFAULT NULL COMMENT '型号id',
  `category_id` int DEFAULT NULL COMMENT '类目ID',
  `po_qty_old` int NOT NULL DEFAULT '0' COMMENT '采购数量',
  `normal_qty_old` int NOT NULL DEFAULT '0' COMMENT '正品数量',
  `spare_qty_old` int NOT NULL DEFAULT '0' COMMENT '备品数量',
  `spare_rate_old` int NOT NULL DEFAULT '0' COMMENT '备品率(‰)',
  `po_qty_new` int NOT NULL DEFAULT '0' COMMENT '采购数量变更',
  `normal_qty_new` int NOT NULL DEFAULT '0' COMMENT '正品数量变更',
  `spare_qty_new` int NOT NULL DEFAULT '0' COMMENT '备品数量变更',
  `spare_rate_new` int NOT NULL DEFAULT '0' COMMENT '备品率(‰)变更',
  `sn_start` varchar(9) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'SN开始码',
  `sn_end` varchar(9) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'SN结束码',
  `plan_staff_id` int NOT NULL COMMENT '计划人员',
  `buyer_id` int NOT NULL COMMENT '采购人员',
  `change_initiator_id` int NOT NULL COMMENT '变更发起人',
  `status` int NOT NULL DEFAULT '0' COMMENT '采购变更单状态 0:待提交,1:审核中,2:已驳回,3:已作废,4: 已完成',
  `review_status` int NOT NULL DEFAULT '0' COMMENT '审核状态:0=初始状态,1=待审核;2=审核通过;3=审核不通过',
  `po_process_instance_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'PO单流程实例id',
  `pco_process_instance_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'PCO单流程实例id',
  `generation_time` datetime DEFAULT NULL COMMENT '生成时间',
  `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
  `review_time` datetime DEFAULT NULL COMMENT '审核完成时间',
  `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
  `expected_delivery_date` date NOT NULL COMMENT '期望交期',
  `confirmed_delivery_date` date DEFAULT NULL COMMENT '确认交期',
  `changed_delivery_date` date DEFAULT NULL COMMENT '变更交期',
  `checked_delivery_date` date DEFAULT NULL COMMENT '复核交期',
  `plan_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '计划备注',
  `purchase_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '采购备注',
  `modify_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '计划变更备注',
  `review_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '复核备注',
  `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除,0=未删除,1=删除',
  `create_by` int NOT NULL COMMENT '创建的用户',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` int NOT NULL COMMENT '更新的用户',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_psku` (`psku`) USING BTREE,
  KEY `idx_po_no` (`po_no`) USING BTREE,
  KEY `idx_supplier_id` (`supplier_id`) USING BTREE,
  KEY `idx_plan_staff_id` (`plan_staff_id`) USING BTREE,
  KEY `idx_pco_no` (`pco_no`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=164 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='采购变更单表';

CREATE TABLE `pms_purchase_change_order_item`
(
    `id`                  int                                                          NOT NULL AUTO_INCREMENT,
    `po_item_id`      int                                                          NOT NULL COMMENT '原采购单明细ID',
    `pco_id`              int                                                          NOT NULL COMMENT '采购变更id',
    `pco_no`              varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '采购变更单号',
    `pr_id`               int                                                          NOT NULL COMMENT '请购id',
    `pr_no`               varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '请购单号',
    `po_id`               int                                                          NOT NULL COMMENT '采购id',
    `po_no`               varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '采购单号',
    `po_type`             varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'PO类型：\r\nSC_RETURN：SC返单\r\nSC_FIRST：SC首单\r\nREPAIR：返修\r\nVC：VC\r\nB2B：B2B',
    `application_qty`     int                                                          NOT NULL COMMENT '申请数量',
    `purchase_qty_old`    int                                                          NOT NULL COMMENT '采购数量',
    `purchase_qty_new`    int                                                          NOT NULL COMMENT '采购数量变更',
    `sales_channel_id`    int                                                          NOT NULL COMMENT '销售渠道ID',
    `store_id`            int                                                          NOT NULL COMMENT '店铺ID',
    `site_code`           varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT '' COMMENT '目的港,对应店铺绑定的站点字段',
    `operation_staff_id`  int                                                          NOT NULL COMMENT '运营人员',
    `is_deleted`          int                                                          NOT NULL DEFAULT '0' COMMENT '是否删除,0=未删除,1=删除',
    `create_by`           int                                                          NOT NULL COMMENT '创建的用户',
    `create_time`         datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`           int                                                          NOT NULL COMMENT '更新的用户',
    `update_time`         datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_pr_no` (`pr_no`) USING BTREE,
    KEY `idx_po_no` (`po_no`) USING BTREE,
    KEY `idx_channel_id` (`sales_channel_id`) USING BTREE,
    KEY `idx_operation_staff_id` (`operation_staff_id`) USING BTREE,
    KEY `idx_site_code` (`site_code`) USING BTREE,
    KEY `idx_store_id` (`store_id`) USING BTREE,
    KEY `idx_pco_no` (`pco_no`) USING BTREE,
    KEY `idx_pco_Id` (`pco_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 168
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='采购变更单明细表';


CREATE TABLE `pms_purchase_change_order_price_info`
(
    `id`                         int                                                          NOT NULL AUTO_INCREMENT,
    `po_price_id`            int                                                          NOT NULL COMMENT '原采购单价目ID',
    `pco_id`                     int                                                          NOT NULL COMMENT '采购变更id',
    `pco_no`                     varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '采购变更单号',
    `po_id`                      int                                                          NOT NULL COMMENT '采购id',
    `po_no`                      varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '采购单号',
    `rate_id_old`                    int                                                          NOT NULL COMMENT '产品价目管理价格信息ID  参考 mdm_fluctuate_exchange_rate表id',
    `rate_id_new`             int                                                          NOT NULL COMMENT '产品价目管理价格信息ID变更  参考 mdm_fluctuate_exchange_rate表id',
    `price_number_old`               varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '价目编号',
    `price_number_new`        varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '价目编号变更',
    `currency_old`                   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '币别',
    `currency_new`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '币别变更',
    `inclusive_tax_price_old`        decimal(10, 4)                                                        DEFAULT '0.0000' COMMENT '含税单价',
    `inclusive_tax_price_new` decimal(10, 4)                                                        DEFAULT '0.0000' COMMENT '含税单价变更',
    `tax_rate_old`                   int                                                          NOT NULL DEFAULT '0' COMMENT '税率,之后用来计算未税价',
    `tax_rate_new`            int                                                          NOT NULL DEFAULT '0' COMMENT '税率,之后用来计算未税价变更',
    `purchase_amount_old`            decimal(12, 4)                                               NOT NULL DEFAULT '0.0000' COMMENT '采购总价',
    `purchase_amount_new`     decimal(12, 4)                                               NOT NULL DEFAULT '0.0000' COMMENT '采购总价变更',
    `exchange_rate_old`              decimal(10, 4)                                               NOT NULL DEFAULT '1.0000' COMMENT '汇率',
    `exchange_rate_new`       decimal(10, 4)                                               NOT NULL DEFAULT '1.0000' COMMENT '汇率变更',
    `is_deleted`                 int                                                          NOT NULL DEFAULT '0' COMMENT '是否删除,0=未删除,1=删除',
    `create_by`                  int                                                          NOT NULL COMMENT '创建的用户',
    `create_time`                datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`                  int                                                          NOT NULL COMMENT '更新的用户',
    `update_time`                datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_po_no` (`po_no`) USING BTREE,
    KEY `idx_poId` (`po_id`) USING BTREE,
    KEY `idx_pco_no` (`pco_no`) USING BTREE,
    KEY `idx_pco_Id` (`pco_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 79
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='采购变更单产品价目表';

CREATE TABLE `pms_purchase_change_order_supplier_info`
(
    `id`                                int                                                           NOT NULL AUTO_INCREMENT,
    `po_supplier_id`                int                                                           NOT NULL COMMENT '原采购单供应商ID',
    `pco_id`                            int                                                           NOT NULL COMMENT '采购变更id',
    `pco_no`                            varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '采购变更单号',
    `po_id`                             int                                                           NOT NULL COMMENT '采购id',
    `po_no`                             varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '采购单号',
    `supplier_id_old`                       int                                                           NOT NULL COMMENT '供应商ID',
    `supplier_id_new`                int                                                           NOT NULL COMMENT '供应商ID变更',
    `supplier_code_old`                     varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '供应商编码',
    `supplier_code_new`              varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '供应商编码变更',
    `supplier_short_name_old`               varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '简称',
    `supplier_short_name_new`        varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '简称变更',
    `supplier_full_name_old`                varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
    `supplier_full_name_new`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称变更',
    `supplier_full_name_en_old`             varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '英文名称',
    `supplier_full_name_en_new`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '英文名称变更',
    `supplier_contact_id_old`         int NOT NULL COMMENT '联系人ID',
    `supplier_contact_id_new`          int NOT NULL COMMENT '联系人ID变更',
    `supplier_contact_name_old`             varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '联系人名称',
    `supplier_contact_name_new`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '联系人名称变更',
    `supplier_contact_tel_old`              varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '联系人电话',
    `supplier_contact_tel_new`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '联系人电话变更',
    `supplier_trade_terms_old`              int                                                                    DEFAULT NULL COMMENT '贸易条款：\r\n1=FOB\r\n2=EXW\r\n3=CIF\r\n4=CFR',
    `supplier_trade_terms_new`       int                                                                    DEFAULT NULL COMMENT '贸易条款变更：\r\n1=FOB\r\n2=EXW\r\n3=CIF\r\n4=CFR',
    `supplier_delivery_location_old`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '交货地点',
    `supplier_delivery_location_new` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '交货地点变更',
    `contract_template_id_old`              int                                                           NOT NULL DEFAULT '0' COMMENT '合同模板ID',
    `contract_template_id_new`       int                                                           NOT NULL DEFAULT '0' COMMENT '合同模板ID变更',
    `contract_template_name_old`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '合同模板名字',
    `contract_template_name_new`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '合同模板名字变更',
    `suppler_settlement_id_old`             int                                                           NOT NULL COMMENT '供应商结算方式ID',
    `suppler_settlement_id_new`      int                                                           NOT NULL COMMENT '供应商结算方式ID变更',
    `settlement_desc_old`                   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '账期描述',
    `settlement_desc_new`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '账期描述变更',
    `advance_payment_ratio_old`             decimal(8, 2)                                                          DEFAULT NULL COMMENT '预付款比例',
    `advance_payment_ratio_new`      decimal(8, 2)                                                          DEFAULT NULL COMMENT '预付款比例变更',
    `supplier_address_id_old`         int NOT NULL COMMENT '详细地址ID',
    `supplier_address_id_new`          int NOT NULL COMMENT '详细地址ID变更',
    `address_old`                           varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '详细地址',
    `address_new`                    varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '详细地址变更',
    `swift_code_old`                        varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT 'swift code',
    `swift_code_new`                 varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT 'swift code变更',
    `legal_name_old`                        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '资质名称',
    `legal_name_new`                 varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '资质名称变更',
    `sourcing_pic_userid_old`               int                                                                    DEFAULT NULL COMMENT '商务负责人的userid',
    `sourcing_pic_userid_new`        int                                                                    DEFAULT NULL COMMENT '商务负责人的userid变更',
    `suppler_financial_id_old`              int                                                           NOT NULL DEFAULT '0' COMMENT '供应商财务信息ID',
    `suppler_financial_id_new`       int                                                           NOT NULL DEFAULT '0' COMMENT '供应商财务信息ID变更',
    `account_id_old`                        varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '银行账户',
    `account_id_new`                 varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '银行账户变更',
    `bank_name_old`                         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '银行名称',
    `bank_name_new`                  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '银行名称变更',
    `is_deleted`                        int                                                           NOT NULL DEFAULT '0' COMMENT '是否删除,0=未删除,1=删除',
    `create_by`                         int                                                           NOT NULL COMMENT '创建的用户',
    `create_time`                       datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`                         int                                                           NOT NULL COMMENT '更新的用户',
    `update_time`                       datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_po_no` (`po_no`) USING BTREE,
    KEY `idx_poId` (`po_id`) USING BTREE,
    KEY `idx_pco_no` (`pco_no`) USING BTREE,
    KEY `idx_pco_Id` (`pco_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 78
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='采购变更单供应商表';

/* 菜单-PCO */
INSERT INTO `sys_menu_info` ( `parent_id`, `system_module`, `multi_language_names`, `menu_icon`, `menu_type`, `component_path`, `route_address`, `route_params`, `perms`, `sort`, `status`, `is_deleted`, `is_cached`, `update_by`, `create_by`, `update_time`, `create_time`)
SELECT id, 'PMS', '[{\"name\": \"PCO\", \"language\": \"en-US\"}, {\"name\": \"PCO变更单\", \"language\": \"zh-CN\"}]', '', 'Menu', 'pms/purchaseChageOrder/index', 'purchaseChageOrder', '', '', 5, 1, 0, 0, 1, 1, NOW(), NOW() FROM sys_menu_info WHERE system_module='PMS' AND parent_id=0;

INSERT INTO `sys_menu_info` ( `parent_id`, `system_module`, `multi_language_names`, `menu_icon`, `menu_type`, `component_path`, `route_address`, `route_params`, `perms`, `sort`, `status`, `is_deleted`, `is_cached`, `update_by`, `create_by`, `update_time`, `create_time`)
SELECT id, 'PMS', '[{\"name\": \"List\", \"language\": \"en-US\"}, {\"name\": \"列表\", \"language\": \"zh-CN\"}]', '', 'Button', '', '', '', 'pms:purchaseChangeOrder:page', 0, 1, 0, 0, 1, 1, NOW(), NOW() FROM sys_menu_info WHERE system_module='PMS' AND route_address='purchaseChageOrder'
UNION ALL SELECT id, 'PMS', '[{\"name\": \"Edit\", \"language\": \"en-US\"}, {\"name\": \"编辑\", \"language\": \"zh-CN\"}]', '', 'Button', '', '', '', 'pms:purchaseChangeOrder:edit', 1, 1, 0, 0, 1, 1, NOW(), NOW() FROM sys_menu_info WHERE system_module='PMS' AND route_address='purchaseChageOrder'
UNION ALL SELECT id, 'PMS', '[{\"name\": \"Detail\", \"language\": \"en-US\"}, {\"name\": \"详情\", \"language\": \"zh-CN\"}]', '', 'Button', '', '', '', 'pms:purchaseChangeOrder:detail', 2, 1, 0, 0, 1, 1, NOW(), NOW() FROM sys_menu_info WHERE system_module='PMS' AND route_address='purchaseChageOrder'
UNION ALL SELECT id, 'PMS', '[{\"name\": \"Export\", \"language\": \"en-US\"}, {\"name\": \"导出\", \"language\": \"zh-CN\"}]', '', 'Button', '', '', '', 'pms:purchaseChangeOrder:export', 3, 1, 0, 0, 1, 1, NOW(), NOW() FROM sys_menu_info WHERE system_module='PMS' AND route_address='purchaseChageOrder'
UNION ALL SELECT id, 'PMS', '[{\"name\": \"Invalid\", \"language\": \"en-US\"}, {\"name\": \"作废\", \"language\": \"zh-CN\"}]', '', 'Button', '', '', '', 'pms:purchaseChangeOrder:invalid', 4, 1, 0, 0, 1, 1, NOW(), NOW() FROM sys_menu_info WHERE system_module='PMS' AND route_address='purchaseChageOrder'
UNION ALL SELECT id, 'PMS', '[{\"name\": \"Review\", \"language\": \"en-US\"}, {\"name\": \"审批\", \"language\": \"zh-CN\"}]', '', 'Button', '', '', '', 'pms:purchaseChangeOrder:review', 5, 1, 0, 0, 1, 1, NOW(), NOW() FROM sys_menu_info WHERE system_module='PMS' AND route_address='purchaseChageOrder'
UNION ALL SELECT id, 'PMS', '[{\"name\": \"Log\", \"language\": \"en-US\"}, {\"name\": \"日志\", \"language\": \"zh-CN\"}]', '', 'Button', '', '', '', 'pms:purchaseChangeOrder:log', 6, 1, 0, 0, 1, 1, NOW(), NOW() FROM sys_menu_info WHERE system_module='PMS' AND route_address='purchaseChageOrder'
UNION ALL SELECT id, 'PMS', '[{\"name\": \"ADD PCO\", \"language\": \"en-US\"}, {\"name\": \"新增PCO\", \"language\": \"zh-CN\"}]', '', 'Button', '', '', '', 'pms:purchaseChangeOrder:add', 22, 1, 0, 0, 1, 1, NOW(), NOW() FROM sys_menu_info WHERE system_module='PMS' AND route_address='purchaseOrder'
UNION ALL SELECT id, 'PMS', '[{\"name\": \"withdraw\", \"language\": \"en-US\"}, {\"name\": \"撤回\", \"language\": \"zh-CN\"}]', '', 'Button', '', '', '', 'pms:purchaseChangeOrder:withdraw', 7, 1, 0, 0, 1, 1, NOW(), NOW() FROM sys_menu_info WHERE system_module='PMS' AND route_address='purchaseChageOrder';

INSERT INTO `sys_menu_language` (`menu_id`, `language`, `name`, `create_by`, `create_time`, `update_by`, `update_time`, `is_deleted`)
SELECT id, 'en-US', 'PCO', 1, NOW(), 1, NOW(), 0 FROM sys_menu_info WHERE system_module='PMS' AND route_address='purchaseChageOrder'
UNION ALL SELECT id, 'zh-CN', 'PCO变更单', 1, NOW(), 1, NOW(), 0 FROM sys_menu_info WHERE system_module='PMS' AND route_address='purchaseChageOrder'
UNION ALL SELECT id, 'en-US', 'List', 1, NOW(), 1, NOW(), 0 FROM sys_menu_info WHERE system_module='PMS' AND perms='pms:purchaseChangeOrder:page'
UNION ALL SELECT id, 'zh-CN', '列表', 1, NOW(), 1, NOW(), 0 FROM sys_menu_info WHERE system_module='PMS' AND perms='pms:purchaseChangeOrder:page'
UNION ALL SELECT id, 'en-US', 'Edit', 1, NOW(), 1, NOW(), 0 FROM sys_menu_info WHERE system_module='PMS' AND perms='pms:purchaseChangeOrder:edit'
UNION ALL SELECT id, 'zh-CN', '编辑', 1, NOW(), 1, NOW(), 0 FROM sys_menu_info WHERE system_module='PMS' AND perms='pms:purchaseChangeOrder:edit'
UNION ALL SELECT id, 'en-US', 'Detail', 1, NOW(), 1, NOW(), 0 FROM sys_menu_info WHERE system_module='PMS' AND perms='pms:purchaseChangeOrder:detail'
UNION ALL SELECT id, 'zh-CN', '详情', 1, NOW(), 1, NOW(), 0 FROM sys_menu_info WHERE system_module='PMS' AND perms='pms:purchaseChangeOrder:detail'
UNION ALL SELECT id, 'en-US', 'Export', 1, NOW(), 1, NOW(), 0 FROM sys_menu_info WHERE system_module='PMS' AND perms='pms:purchaseChangeOrder:export'
UNION ALL SELECT id, 'zh-CN', '导出', 1, NOW(), 1, NOW(), 0 FROM sys_menu_info WHERE system_module='PMS' AND perms='pms:purchaseChangeOrder:export'
UNION ALL SELECT id, 'en-US', 'Invalid', 1, NOW(), 1, NOW(), 0 FROM sys_menu_info WHERE system_module='PMS' AND perms='pms:purchaseChangeOrder:invalid'
UNION ALL SELECT id, 'zh-CN', '作废', 1, NOW(), 1, NOW(), 0 FROM sys_menu_info WHERE system_module='PMS' AND perms='pms:purchaseChangeOrder:invalid'
UNION ALL SELECT id, 'en-US', 'Review', 1, NOW(), 1, NOW(), 0 FROM sys_menu_info WHERE system_module='PMS' AND perms='pms:purchaseChangeOrder:review'
UNION ALL SELECT id, 'zh-CN', '审批', 1, NOW(), 1, NOW(), 0 FROM sys_menu_info WHERE system_module='PMS' AND perms='pms:purchaseChangeOrder:review'
UNION ALL SELECT id, 'en-US', 'Log', 1, NOW(), 1, NOW(), 0 FROM sys_menu_info WHERE system_module='PMS' AND perms='pms:purchaseChangeOrder:log'
UNION ALL SELECT id, 'zh-CN', '日志', 1, NOW(), 1, NOW(), 0 FROM sys_menu_info WHERE system_module='PMS' AND perms='pms:purchaseChangeOrder:log'
UNION ALL SELECT id, 'en-US', 'ADD PCO', 1, NOW(), 1, NOW(), 0 FROM sys_menu_info WHERE system_module='PMS' AND perms='pms:purchaseChangeOrder:add'
UNION ALL SELECT id, 'zh-CN', '新增PCO', 1, NOW(), 1, NOW(), 0 FROM sys_menu_info WHERE system_module='PMS' AND perms='pms:purchaseChangeOrder:add'
UNION ALL SELECT id, 'en-US', 'withdraw', 1, NOW(), 1, NOW(), 0 FROM sys_menu_info WHERE system_module='PMS' AND perms='pms:purchaseChangeOrder:withdraw'
UNION ALL SELECT id, 'zh-CN', '撤回', 1, NOW(), 1, NOW(), 0 FROM sys_menu_info WHERE system_module='PMS' AND perms='pms:purchaseChangeOrder:withdraw';


/* 字典-PCO变更类型 */
INSERT INTO `sys_dict_type` (`dict_type`, `dict_name`, `status`, `is_deleted`, `remark`, `create_by`, `update_by`, `update_time`, `create_time`)
VALUES ('PMS_PCO_TYPE', '[{\"name\": \"PMS TO PCO Type\", \"language\": \"en-US\"}, {\"name\": \"PMS_PCO类型\", \"language\": \"zh-CN\"}]', 1, 0, NULL, 1, 1, NOW(), NOW());

INSERT INTO `sys_dict_type_language` (`dict_type_id`, `language`, `name`, `create_by`, `create_time`, `update_by`, `update_time`, `is_deleted`)
SELECT id, 'en-US', 'PMS TO PCO Type', 1, NOW(), 1, NOW(), 0 FROM sys_dict_type WHERE dict_type ='PMS_PCO_TYPE'
UNION ALL SELECT id, 'zh-CN', 'PMS_PCO类型', 1, NOW(), 1, NOW(), 0 FROM sys_dict_type WHERE dict_type ='PMS_PCO_TYPE';


INSERT INTO `sys_dict_item` (`dict_id`, `dict_type`, `multi_language_names`, `dict_key`, `sort`, `status`, `remark`, `is_deleted`, `update_by`, `create_by`, `update_time`, `create_time`)
SELECT id, 'PMS_PCO_TYPE', NULL, 'QUANTITY_CHANGE', 0, 1, NULL, 0, 1, 1, NOW(), NOW() FROM sys_dict_type WHERE dict_type ='PMS_PCO_TYPE'
UNION ALL SELECT id, 'PMS_PCO_TYPE', NULL, 'SUPPLIER_CHANGE', 1, 1, NULL, 0, 1, 1, NOW(), NOW() FROM sys_dict_type WHERE dict_type ='PMS_PCO_TYPE'
UNION ALL SELECT id, 'PMS_PCO_TYPE', NULL, 'PRICE_CHANGE', 2, 1, NULL, 0, 1, 1, NOW(), NOW() FROM sys_dict_type WHERE dict_type ='PMS_PCO_TYPE';

INSERT INTO `sys_dict_item_language` (`dict_item_id`, `language`, `name`, `create_by`, `create_time`, `update_by`, `update_time`, `is_deleted`)
SELECT id, 'en-US', 'Quantity Change', 1,  NOW(), 1,  NOW(), 0 FROM sys_dict_item WHERE dict_key='QUANTITY_CHANGE'
UNION ALL SELECT id, 'zh-CN', '数量变更', 1,  NOW(), 1,  NOW(), 0 FROM sys_dict_item WHERE dict_key='QUANTITY_CHANGE'
UNION ALL SELECT id, 'en-US', 'Supplier Change', 1,  NOW(), 1,  NOW(), 0 FROM sys_dict_item WHERE dict_key='SUPPLIER_CHANGE'
UNION ALL SELECT id, 'zh-CN', '供应商信息变更', 1,  NOW(), 1, NOW(), 0 FROM sys_dict_item WHERE dict_key='SUPPLIER_CHANGE'
UNION ALL SELECT id, 'en-US', 'Price Change', 1,  NOW(), 1,  NOW(), 0 FROM sys_dict_item WHERE dict_key='PRICE_CHANGE'
UNION ALL SELECT id, 'zh-CN', '价格变更', 1, NOW(), 1,  NOW(), 0 FROM sys_dict_item WHERE dict_key='PRICE_CHANGE';