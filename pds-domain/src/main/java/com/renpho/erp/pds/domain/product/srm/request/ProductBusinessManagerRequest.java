package com.renpho.erp.pds.domain.product.srm.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 推送采购供应商信息到PDS的载体.
 *
 * <AUTHOR>
 * @since 2024.12.10
 */
@Data
public class ProductBusinessManagerRequest {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 产品管理主键ID
     */
    private Integer productManagerId;

    /**
     * 采购供应商ID
     */
    private Integer supplierId;

    /**
     * 采购供应商等级
     */
    private String supplierLevel;

    /**
     * 供应商型号
     */
    private String supplierType;

    /**
     * 备品率(‰)
     */
    private Integer sparePartsRate;

    /**
     * MOQ(pcs)
     */
    private Integer moq;

    /**
     * LT(天)
     */
    private Integer ltDays;

    /**
     * 补货状态
     */
    private String replenishmentStatus;

    /**
     * 审核状态:0,草稿; 1,待审核;2,审核通过;3,审核不通过
     */
    private Integer reviewStatus;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTime;

    /**
     * 审核完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reviewTime;

    /**
     * 合同模板有效期开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime validityDateStart;

    /**
     * 合同模板有效期结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime validityDateEnd;

    /**
     * 合同模版ID
     */
    private String contractTemplateId;

    /**
     * 备注
     */
    private String remark;

}
