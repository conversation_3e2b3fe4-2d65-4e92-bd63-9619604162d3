package com.renpho.erp.pds.domain.common;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/9/29
 */
@Data
public class FtmFileDetailResponse {

	/**
	 * 文件id
	 */
	private String id;

	/**
	 * 文件访问地址
	 */
	private String url;

	/**
	 * 文件大小，单位字节
	 */
	private Long size;

	/**
	 * 文件名称
	 */
	private String filename;

	/**
	 * 原始文件名
	 */
	private String originalFilename;

	/**
	 * 基础存储路径
	 */
	private String basePath;

	/**
	 * 存储路径
	 */
	private String path;

	/**
	 * 文件扩展名
	 */
	private String ext;

	/**
	 * MIME 类型
	 */
	private String contentType;

	/**
	 * 存储平台
	 */
	private String platform;

	/**
	 * 缩略图访问路径
	 */
	private String thUrl;

	/**
	 * 缩略图名称
	 */
	private String thFilename;

	/**
	 * 缩略图大小，单位字节
	 */
	private Long thSize;

	/**
	 * 缩略图 MIME 类型
	 */
	private String thContentType;

	/**
	 * 预签名URL
	 */
	private String presignedUrl;

}
