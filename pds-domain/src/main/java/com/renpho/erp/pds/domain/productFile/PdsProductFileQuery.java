package com.renpho.erp.pds.domain.productFile;


import com.renpho.karma.dto.PageQuery;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.util.List;

/**
 * @Author: Wyatt
 * @Date: 2024-12-03 15:19:06
 * @Description: TODO
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PdsProductFileQuery extends PageQuery {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 产品唯一id
     */
    private Integer productManagerId;

    /**
     * 采购SKU
     */
    private String purchaseSku;

    /**
     * 文件名称
     */
    private String originalFilename;

    /**
     * 标签id
     */
    private Integer labelId;


    /**
     * 文件类型
     */
    private Integer fileType;

    /**
     * 上传人id
     */
    private List<Integer> uploadUser;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

}
