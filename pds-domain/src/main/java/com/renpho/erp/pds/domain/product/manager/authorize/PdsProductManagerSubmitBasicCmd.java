package com.renpho.erp.pds.domain.product.manager.authorize;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.renpho.erp.pds.domain.common.LanguageEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 产品管理-基础信息
 *
 * <AUTHOR>
 * @since 2024/11/04
 */
@Data
public class PdsProductManagerSubmitBasicCmd implements VO {

    /**
     * 产品管理-基础信息-主键ID
     */
    private Integer id;

    /**
     * 产品封面图片
     */
    private String productCoverImageId;

    /**
     * 国家/地区ID
     */
    private Integer countryRegionId;

    /**
     * 产品型号ID
     */
    private Integer productModelId;

    /**
     * 品牌ID
     */
    private Integer brandId;

    /**
     * 品类ID
     */
    private Integer categoryId;

    /**
     * 颜色ID
     */
    private Integer colorId;


    // /**
    //  * 销售渠道ID
    //  */
    // private Integer salesChannelId;
    /**
     * 渠道类型
     */
    private Integer channelType;

    /**
     * 版本编号,允许数字与符号组合、字符限制32字符,最多支持4字符
     */
    private String version;

    /**
     * PSKU
     */
    private String purchaseSku;

    /**
     * 属性编码，允许数字与符号组合、最多支持4字符
     */
    private String attributeEncoding;

    /**
     * 中文名称，仅支持中文输入，不支持特殊字符、字符限制40字符
     */
    private String chineseName;

    /**
     * 英文名称，仅支持英文大小写输入，不支持特殊字符，文本间支持空格、字符限制100字符
     */
    private String englishName;

    /**
     * 市场名称，支持文本输入，不支持特殊字符，文本间支持空格、字符限制100字符
     */
    private String marketName;

    /**
     * 产品描述,每行输入框限制200字符、添加最多20行
     */
    private List<String> productDescriptionList;

    /**
     * 产品卖点,每行输入框限制200字符、添加最多20行
     */
    private List<String> productSellingPointsList;

    /**
     * 附件
     */
    private List<AttachmentInfoDto> attachmentList;

    /**
     * 备注,输入框限制2000字符
     */
    private String remark;

    /**
     * 产品负责人ID
     */
    private Integer productManagerId;

    /**
     * 商务负责人ID
     */
    private Integer businessManagerId;

    /**
     * 采购负责人ID
     */
    private Integer procurementManagerId;

    /**
     * 计划负责人ID
     */
    private Integer planningManagerId;

    /**
     * 合规负责人ID
     */
    private Integer complianceManagerId;

    /**
     * 包材负责人ID
     */
    private Integer packingMaterialManagerId;

    /**
     * 配件信息: 是否含适配器 (0: 否, 1: 是)
     */
    private Integer adapterIncluded;

    /**
     * 审核状态:0,草稿; 1,待审核;2,审核通过;3,审核不通过
     */
    private Integer reviewStatus;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * product_type 产品类型字典
     */
    @Trans(type = TransType.DICTIONARY, key = "product_type", ref = "productTypeName")
    private Integer productType;

    /**
     * 产品类型对应的名称， 来源于 product_type
     */
    private String productTypeName;

    /**
     * product_status 产品状态字典
     */
    @Trans(type = TransType.DICTIONARY, key = "product_status", ref = "productStatusName")
    private Integer productStatus = 0;

    /**
     * 产品状态对应的名称， 来源于 product_status
     */
    private String productStatusName;


    /* 缓存字段信息，仅仅导入使用 */

    /**
     * 产品封面图片链接
     */
    private String productCoverImageIdBasic;

    /**
     * 国家Code
     */
    private String countryRegionCodeBasic;

    /**
     * 产品型号
     */
    private String productModelNoBasic;

    /**
     * 颜色Code
     */
    private String colorCodeBasic;

    /**
     * 渠道类型名字
     */
    private String channelTypeBasic;

    /**
     * 产品负责人工号
     */
    private String productManagerCodeBasic;

    /**
     * 商务负责人工号
     */
    private String businessManagerCodeBasic;

    /**
     * 采购负责人工号
     */
    private String procurementManagerCodeBasic;

    /**
     * 计划负责人工号
     */
    private String planningManagerCodeBasic;

    /**
     * 合规负责人工号
     */
    private String complianceManagerCodeBasic;

    /**
     * 包材负责人工号
     */
    private String packingMaterialManagerCodeBasic;

    /**
     * 产品类型, 见字典 product_type
     */
    private String productTypeBasic;


    /**
     * 校验字典是否合法
     */
    public void checkDict(LanguageEnum language){
        if(StringUtils.isBlank(productTypeName)){
            throwValidationException(language,
                    "产品类型参数不合法",
                    "Product type parameter is illegal"
            );
        }
        if(StringUtils.isBlank(productStatusName)){
            throwValidationException(language,
                    "产品状态参数不合法",
                    "Product status parameter is illegal"
            );
        }

    }

    /**
     * 校验字段内容是否合法
     *
     * @param language 语言类型（中文/英文）
     * @param isStrict 是否启用严格模式（true: 按注解规则校验；false: 非空时校验格式）
     */
    public void validateFields(LanguageEnum language, boolean isStrict) {
        validateField(isStrict, language, countryRegionId != null, "国家/地区信息不能为空", "Country/Region information cannot be null");
        validateField(isStrict, language, productModelId != null, "产品型号信息不能为空", "Product model information cannot be null");
        validateField(isStrict, language, brandId != null, "品牌信息不能为空", "Brand information cannot be null");
        validateField(isStrict, language, categoryId != null, "品类信息不能为空", "Category information cannot be null");
        validateField(isStrict, language, colorId != null, "颜色信息不能为空", "Color information cannot be null");
        // validateField(isStrict, language, salesChannelId != null, "销售渠道信息不能为空", "Sales channel information cannot be null");
        validateField(isStrict, language, channelType != null, "渠道类型信息不能为空", "channel type information cannot be null");
        validateField(isStrict, language, !StringUtils.isBlank(purchaseSku), "PSKU信息不能为空", "PSKU information cannot be null");
        validatePattern(isStrict, language, purchaseSku, "^[A-Z0-9-]{1,32}$",
                "PSKU仅支持英文大写字母、数字、字符'-'组合输入，字符限制32字符",
                "PSKU must only contain uppercase letters, numbers, and '-' with a maximum of 32 characters");
        // 校验属性编码
        validatePattern(false, language, attributeEncoding, "^[0-9\\p{Punct}]{1,4}$",
                null, null,
                "属性编码仅允许数字与符号组合，最多支持4字符",
                "Attribute encoding allows numbers and symbols only, up to 4 characters");
        // 校验版本编号
        validatePattern(false, language, version, "^[0-9\\p{Punct}]{1,4}$",
                null, null,
                "版本编号仅允许数字与符号组合，最多支持4字符",
                "Version must allow only numbers and symbols, up to 4 characters");
        validateLength(isStrict, language, chineseName, 40,
                "中文名称字符限制40字符",
                "Chinese name limit to 40 characters");
        validatePattern(isStrict, language, englishName, "^[A-Za-z ]{1,100}$",
                "英文名称仅支持英文大小写输入，不支持特殊字符，字符限制100字符",
                "English name allows only letters, no special characters, limit to 100 characters");
        validatePattern(isStrict, language, marketName, "^[\\p{L}0-9_ ]{1,100}$",
                "市场名称支持文本输入，不支持特殊字符，字符限制100字符",
                "Market name supports text input, no special characters, limit to 100 characters");
        validateListField(isStrict, language, productDescriptionList, 1, 50,
                "产品描述不能为空", "Product description cannot be null",
                "产品描述最小包含1行", "Product description must have at least 1 row",
                "产品描述最多包含50行", "Product description can have at most 50 rows",
                200, "每行的产品描述不能超过200个字符", "Each product description line cannot exceed 200 characters");
        validateListField(isStrict, language, productSellingPointsList, 1, 50,
                "产品卖点不能为空", "Product selling points cannot be null",
                "产品卖点最小包含1行", "Product selling points must have at least 1 row",
                "产品卖点最多包含50行", "Product selling points can have at most 50 rows",
                200, "每行的产品卖点不能超过200个字符", "Each product selling point line cannot exceed 200 characters");
        validateField(isStrict, language, productType != null, "产品类型不能为空", "Product type cannot be null");
        validateField(isStrict, language, productManagerId != null, "产品负责人不能为空", "Product manager cannot be null");
        validateField(isStrict, language, businessManagerId != null, "商务负责人不能为空", "Business manager cannot be null");
        validateField(isStrict, language, procurementManagerId != null, "采购负责人不能为空", "Procurement manager cannot be null");
        validateField(isStrict, language, planningManagerId != null, "计划负责人不能为空", "Planning manager cannot be null");
        validateField(isStrict, language, complianceManagerId != null, "合规负责人不能为空", "Compliance manager cannot be null");
        validateField(isStrict, language, packingMaterialManagerId != null, "包装设计师不能为空", "Packaging designers cannot be null");
        validateLength(isStrict, language, remark, 2000,
                "备注不能超过2000个字符", "Remark cannot exceed 2000 characters");
    }

    /**
     * 校验正则表达式字段
     *
     * @param isStrict        是否严格校验
     * @param language        语言类型
     * @param field           字段值
     * @param regex           正则表达式
     * @param nullMessageZh   中文-字段为空提示信息
     * @param nullMessageEn   英文-字段为空提示信息
     * @param patternMessageZh 中文-字段格式错误提示信息
     * @param patternMessageEn 英文-字段格式错误提示信息
     */
    private void validatePattern(boolean isStrict, LanguageEnum language, String field, String regex,
                                 String nullMessageZh, String nullMessageEn,
                                 String patternMessageZh, String patternMessageEn) {
        if (StringUtils.isBlank(field)) {
            if (isStrict && nullMessageZh != null && nullMessageEn != null) {
                throwValidationException(language, nullMessageZh, nullMessageEn);
            }
        } else if (!field.matches(regex)) {
            throwValidationException(language, patternMessageZh, patternMessageEn);
        }
    }

    private void validateField(boolean isStrict, LanguageEnum language, boolean condition, String chineseMessage, String englishMessage) {
        if (isStrict || condition) {
            if (!condition) {
                throwValidationException(language, chineseMessage, englishMessage);
            }
        }
    }

    private void validatePattern(boolean isStrict, LanguageEnum language, String field, String pattern, String chineseMessage, String englishMessage) {
        if (!StringUtils.isBlank(field) || isStrict) {
            if (StringUtils.isBlank(field)) {
                throwValidationException(language, chineseMessage, englishMessage);
            }
            if (!field.matches(pattern)) {
                throwValidationException(language, chineseMessage, englishMessage);
            }
        }
    }

    private void validateListField(boolean isStrict, LanguageEnum language, List<String> list, int min, int max,
                                   String nullMessageZh, String nullMessageEn, String minMessageZh, String minMessageEn,
                                   String maxMessageZh, String maxMessageEn, int maxLength, String lineMessageZh, String lineMessageEn) {
        if (isStrict || (list != null && !list.isEmpty())) {
            if (list == null || list.isEmpty()) {
                throwValidationException(language, nullMessageZh, nullMessageEn);
            }
            if (list.size() < min) {
                throwValidationException(language, minMessageZh, minMessageEn);
            }
            if (list.size() > max) {
                throwValidationException(language, maxMessageZh, maxMessageEn);
            }
            for (String line : list) {
                if (line != null && line.length() > maxLength) {
                    throwValidationException(language, lineMessageZh, lineMessageEn);
                }
            }
        }
    }

    private void validateLength(boolean isStrict, LanguageEnum language, String field, int maxLength, String chineseMessage, String englishMessage) {
        if (!StringUtils.isBlank(field) || isStrict) {
            if (field != null && field.length() > maxLength) {
                throwValidationException(language, chineseMessage, englishMessage);
            }
        }
    }

    private void throwValidationException(LanguageEnum language, String chineseMessage, String englishMessage) {
        if (language == LanguageEnum.China) {
            throw new IllegalArgumentException(chineseMessage);
        } else {
            throw new IllegalArgumentException(englishMessage);
        }
    }
}
