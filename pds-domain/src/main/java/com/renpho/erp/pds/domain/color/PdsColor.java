package com.renpho.erp.pds.domain.color;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.jmolecules.ddd.types.AggregateRoot;
import org.jmolecules.ddd.types.Identifiable;
import org.jmolecules.ddd.types.Identifier;

import java.util.List;

/**
 * 颜色表.
 * <AUTHOR>
 * @since 2024.9.23
 */
@Data
public class PdsColor implements AggregateRoot<PdsColor, PdsColor.PdsColorID> {

    private PdsColor.PdsColorID id;

    /**
     * 颜色代码
     */
    private String code;

    /**
     * 备注
     */
    private String remark;

    /**
     * 参数状态 1：Active，0：Disabled 单选框，必选，默认Active
     */
    private Integer status;

    /**
     * 多语言
     */
    private List<LanguageColor> names;

    @RequiredArgsConstructor(staticName = "of")
    public static class PdsColorID implements Identifiable<Integer>, Identifier {

        private final Integer id;

        @Override
        public Integer getId() {
            return id;
        }

    }

}