package com.renpho.erp.pds.domain.common;

import lombok.Getter;

/**
 * 用户状态枚举
 *
 * <AUTHOR>
 * @since 2024.10.23
 */
@Getter
public enum UserStatusEnum {

    /**
     * 在职
     */
    ON("在职", 1),

    /**
     * 离职
     */
    RESIGN("离职", 0);

    /**
     * 名称
     */
    private final String name;

    /**
     * 值
     */
    private final Integer value;

    UserStatusEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    /**
     * 状态寻找
     * @param value 下标
     * @return 有效状态
     */
    public static UserStatusEnum getUserStatus(Integer value) {
        if (value == null) {
            return null;
        }
        for (UserStatusEnum statusEnum : UserStatusEnum.values()) {
            if (statusEnum.getValue().equals(value)) {
                return statusEnum;
            }
        }
        return null;
    }

}
