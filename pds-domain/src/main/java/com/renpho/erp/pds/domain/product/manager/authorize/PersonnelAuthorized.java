package com.renpho.erp.pds.domain.product.manager.authorize;

import lombok.Data;

import java.util.Date;

/**
 * 产品管理视图对象(授权详情)-运营人员.
 *
 * <AUTHOR>
 * @since 2024.10.29
 */
@Data
public class PersonnelAuthorized {

    /**
     * 运营人员用户id
     */
    private Integer authorizedUserId;

    /**
     * 运营人员名称
     */
    private String authorizedUserName;

    /**
     * 授权时间
     */
    private Date createTime;


}
