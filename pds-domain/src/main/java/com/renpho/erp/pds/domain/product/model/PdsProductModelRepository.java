package com.renpho.erp.pds.domain.product.model;

import java.util.Collection;
import java.util.List;

/**
 * 产品型号-操作接口
 */
public interface PdsProductModelRepository {

    Integer add(PdsProductModel pdsBrand, Integer currentUserId);

    Integer edit(PdsProductModel pdsBrand, Integer currentUserId);

    Integer getCurrentSerial(String brandCode, String cateCode);

    List<PdsProductModel> findProductModelListByIds(Collection<PdsProductModel.PdsProductModelID> productModelIds);
}
