package com.renpho.erp.pds.domain.common;

/**
 * 公共常量
 *
 * <AUTHOR>
 * @since 2024.11.5
 */
public interface CommonConstants {

    /**
     * 有效的数据
     */
    Integer ACTIVE = 1;
    /**
     * 无效的数据
     */
    Integer IN_ACTIVE = 0;
    /**
     * 自动生成
     */
    Integer AUTO_GENERATED = 1;
    /**
     * 非自动生成
     */
    Integer UN_AUTO_GENERATED = 0;

    /**
     * 成功
     */
    Integer SUCCESS = 1;

    /**
     * 失败
     */
    Integer ERROR = 1;

    /**
     * 删除标识
     */
    Integer DELETE = 1;

    /**
     * 未删除标识
     */
    Integer NOT_DELETE = 0;

    /**
     * 新增-草稿类型
     */
    Integer ACTION_TYPE_DRAFT = 0;

    /**
     * 新增-提交类型
     */
    Integer ACTION_TYPE_SUBMIT = 1;

    /**
     * 新增-更新类型
     */
    Integer ACTION_TYPE_UPDATE = 2;

    /**
     * 自动识别json对象白名单配置（仅允许解析的包名，范围越小越安全）
     */
    String[] JSON_WHITELIST_STR = { "org.springframework", "com.renpho.erp.pds" };

    /**
     * 标签: 运营管理  6（字典）
     */
    String LABEL_PM = "6";

    /**
     * 标签: 运营人员  7（字典）
     */
    String LABEL_PERSONNEL = "7";
}
