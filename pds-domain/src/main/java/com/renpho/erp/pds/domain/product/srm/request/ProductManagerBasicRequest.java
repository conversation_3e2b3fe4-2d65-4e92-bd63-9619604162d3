package com.renpho.erp.pds.domain.product.srm.request;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * PDS产品管理基础信息mq同步载体
 *
 * <AUTHOR>
 * @since 2024.12.10
 */
@Data
public class ProductManagerBasicRequest {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 国家/地区ID
     */
    private Integer countryRegionId;

    /**
     * 产品封面图片
     */
    private String productCoverImageId;

    /**
     * 产品型号ID
     */
    private Integer productModelId;

    /**
     * 品牌ID(老系统不一定有，所以可以为空)
     */
    private Integer brandId;

    /**
     * 品类ID(老系统不一定有，所以可以为空)
     */
    private Integer categoryId;

    /**
     * 颜色ID(老系统不一定有，所以可以为空)
     */
    private Integer colorId;

    /**
     * 销售渠道ID(老系统不一定有，所以可以为空)
     */
    private Integer salesChannelId;

    /**
     * 属性编码
     */
    private String attributeEncoding;

    /**
     * 版本编号
     */
    private String version;

    /**
     * 采购SKU
     */
    private String purchaseSku;

    /**
     * 产品类型,详情见字典
     */
    private Integer productType;

    /**
     * 市场名称
     */
    private String marketName;

    /**
     * 产品描述，text格式
     */
    private String productDescription;

    /**
     * 产品卖点，text格式
     */
    private String productSellingPoints;

    /**
     * 产品负责人ID
     */
    private Integer productManagerId;

    /**
     * 商务负责人ID
     */
    private Integer businessManagerId;

    /**
     * 采购负责人ID
     */
    private Integer procurementManagerId;

    /**
     * 计划负责人ID
     */
    private Integer planningManagerId;

    /**
     * 合规负责人ID
     */
    private Integer complianceManagerId;

    /**
     * 包材负责人ID
     */
    private Integer packingMaterialManagerId;

    /**
     * 产品状态:0,正常; 1,正在补货;2,淘汰
     */
    private Integer productStatus;

    /**
     * 审核状态:0,草稿; 1,待审核;2,审核通过;3,审核不通过
     */
    private Integer reviewStatus;

    /**
     * 授权状态: 0待授权、1待确认、2已授权
     */
    private Integer authorizeStatus;

    /**
     * 配件信息: 是否含适配器 (0: 否, 1: 是)
     */
    private Integer adapterIncluded;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 审核时间
     */
    private LocalDateTime reviewTime;

    /**
     * 中文名称
     */
    private String chineseName;

    /**
     * 英文名称
     */
    private String englishName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态，0=InActive，1=Active
     */
    private Integer status;

    /**
     * 是否删除，0=未删除，1=删除
     */
    private Integer isDeleted;

    /**
     * 创建的用户ID
     */
    private Integer createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新的用户ID
     */
    private Integer updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
