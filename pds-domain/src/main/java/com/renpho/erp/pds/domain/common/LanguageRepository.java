package com.renpho.erp.pds.domain.common;

import java.util.List;

/**
 * 多语言功能操作接口.
 *
 * <AUTHOR>
 * @since 2024.9.26
 */
public interface LanguageRepository<T extends LanguageParent> {

	/**
	 * 新增多语言
	 * @param language 语言信息
	 * @param tableName 表名
	 */
	void insertLanguage(T language, String tableName);

	/**
	 * 更新多语言
	 * @param language 语言信息
	 * @param tableName 表名
	 */
	void updateLanguage(T language, String tableName);

	/**
	 * 查找多语言信息
	 * @param parentId 主表主键ID
	 * @param tableName 表名
	 * @param clazz 多语言主体class
	 * @return 多语言信息
	 */
	List<T> selectByParentIdAndLanguage(Integer parentId, String tableName, Class<T> clazz);

}
