package com.renpho.erp.pds.domain.productFile;

import com.renpho.karma.dto.Paging;
import org.jmolecules.ddd.types.Repository;

import java.util.List;

public interface PdsProductFileRepository extends Repository<PdsProductFile, PdsProductFile.PdsProductFileID> {

    List<PdsProductFile> addProductFile(AddProductFileReq addProductFileReq);


    Paging<PdsProductFile> findPage(PdsProductFileQuery pdsProductFileQuery);

    /**
     * 更新文件名
     * @param pdsProductFile
     */
    void updateFileName(PdsProductFile pdsProductFile);

    /**
     * 删除文件
     * @param pdsProductFile
     */
    List<Integer> deleteFile(PdsProductFile pdsProductFile);

    /**
     *批量删除文件
     * @param idList
     */
    List<Integer> batchDeleteFile(List<Integer> idList);

}
