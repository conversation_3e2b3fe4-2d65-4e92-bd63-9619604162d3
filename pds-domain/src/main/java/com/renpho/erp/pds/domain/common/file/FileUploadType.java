package com.renpho.erp.pds.domain.common.file;

/**
 * 上传文件类型
 *
 * <AUTHOR>
 * @since 2024.11.11
 */
public enum FileUploadType {

    /**
     * 公开文件上传
     */
    PUBLIC(1, "公开文件上传"),
    /**
     * 私有文件上传
     */
    PRIVATE(2, "私有文件上传"),
    /**
     * 临时文件上传
     */
    TEMPORARY(3, "临时文件上传");

    private final int code;
    private final String description;

    FileUploadType(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 根据 code 获取对应的枚举值
    public static FileUploadType fromCode(int code) {
        for (FileUploadType type : FileUploadType.values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的文件上传类型代码: " + code);
    }

    @Override
    public String toString() {
        return this.description;
    }
}

