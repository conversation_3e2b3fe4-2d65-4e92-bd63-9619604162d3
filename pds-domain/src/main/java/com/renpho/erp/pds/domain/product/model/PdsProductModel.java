package com.renpho.erp.pds.domain.product.model;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.jmolecules.ddd.types.AggregateRoot;
import org.jmolecules.ddd.types.Identifiable;
import org.jmolecules.ddd.types.Identifier;

import java.io.Serializable;

/**
 * 产品型号-集合根.
 * <AUTHOR>
 */
@Data
public class PdsProductModel implements AggregateRoot<PdsProductModel, PdsProductModel.PdsProductModelID> {

	private PdsProductModelID id;

	/**
	 * 是否自动生成产品型号（0：否，1：是）
	 */
	private Integer isAutoGenerated;

	/**
	 * 产品型号
	 */
	private String modelNo;

	/**
	 * 品牌主键ID
	 */
	private Integer brandId;

	/**
	 * 一级品类主键ID
	 */
	private Integer cateFirst;

	/**
	 * 二级品类主键ID
	 */
	private Integer cateSecond;

	/**
	 * 三级品类主键ID
	 */
	private Integer cateThird;

	/**
	 * 产品经理userid
	 */
	private Integer pmUserid;


	@Data
	public static class PdsProductModelID implements Identifiable<Integer>, Identifier {

		private final Integer id;

	}
}
