package com.renpho.erp.pds.domain.common;


import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.jmolecules.ddd.types.AggregateRoot;
import org.jmolecules.ddd.types.Identifiable;
import org.jmolecules.ddd.types.Identifier;

/**
 * @Author: Wyatt
 * @Date: 2024-12-06 15:08:32
 * @Description: 产品系统型号类型
 **/
@Data
public class PdsSysTypes implements AggregateRoot<PdsSysTypes, PdsSysTypes.PdsSysTypesID> {


    /**
     * 字典id
     */
    private PdsSysTypesID id;

    /**
     * 所属模块名称。
     */
    private String moduleName;

    /**
     * 类别名称。
     */
    private String typeName;

    /**
     * 类别的值。
     */
    private String typeValue;

    /**
     * 备注或其他说明。
     */
    private String remark;

    @RequiredArgsConstructor(staticName = "of")
    public static class PdsSysTypesID implements Identifiable<Integer>, Identifier {
        private final Integer id;

        @Override
        public Integer getId() {
            return id;
        }
    }
}
