package com.renpho.erp.pds.domain.productFile;

import org.jmolecules.ddd.integration.AggregateLookup;

import java.util.List;

public interface PdsProductFileLookup extends AggregateLookup<PdsProductFile, PdsProductFile.PdsProductFileID> {

    /**
     * 根据产品ID统计产品文件数量
     * @param productManagerId
     */
    PdsProductFile getNumberByProductManagerId(Integer productManagerId);

    /**
     * 查询所有的上传人员
     * @return
     */
    List<PdsProductFile> findByUserIdList();

    /**
     * 根据id查询数据
     * @param queryIdList
     * @return
     */
    List<PdsProductFile> getListById(List<Integer> queryIdList);

    /**
     * 根据id查询数据，包括未删除的
     *
     * @return
     */
    List<PdsProductFile> queryListByIdAndIsDeleted(List<Integer> queryIdList);
}
