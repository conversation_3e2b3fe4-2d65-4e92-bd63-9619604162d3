package com.renpho.erp.pds.domain.productFile;


import com.renpho.erp.pds.domain.common.FtmFileDetailResponse;
import com.renpho.erp.pds.domain.common.PdsSysTypes;
import com.renpho.erp.pds.domain.product.manager.authorize.PdsProductManagerSubmitBasicCmd;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.jmolecules.ddd.types.AggregateRoot;
import org.jmolecules.ddd.types.Identifiable;
import org.jmolecules.ddd.types.Identifier;

import java.time.LocalDateTime;

/**
 * @Author: Wyatt
 * @Date: 2024-12-02 20:13:19
 * @Description: 产品视觉传达集合根
 **/
@Data
public class PdsProductFile implements AggregateRoot<PdsProductFile, PdsProductFile.PdsProductFileID> {

    private PdsProductFileID id;

    /**
     * 产品管理主键ID
     */
    private Integer productManagerId;


    /**
     * 文件id
     */
    private String fileDetailId;

    /**
     * 文件名称
     */
    private String originalFilename;

    /**
     * 文件尺寸
     */
    private String fileDimension;


    /**
     * 文件类型
     */
    private Integer fileType;

    /**
     * 是否删除 0=未删除, 1=删除
     */
    private Integer isDeleted;

    /**
     * 创建人
     */
    private Integer createBy;

    /**
     * 创建的用户工号
     */
    private String  createByName;

    /**
     * 创建的用户工号
     */
    private String createByCode;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private Integer updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private PdsSysTypes pdsSysTypes;

    private ProductFileNumber productFileNumber;

    private FtmFileDetailResponse ftmFileDetailResponse;

    private PdsProductManagerSubmitBasicCmd pdsProductManagerSubmitBasicCmd;


    @RequiredArgsConstructor(staticName = "of")
    public static class PdsProductFileID implements Identifiable<Integer>, Identifier {
        private final Integer id;

        @Override
        public Integer getId() {
            return id;
        }
    }
}
