package com.renpho.erp.pds.domain.common;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 多语言.
 */
@Data
public class MultiLanguage implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 语言，cn,en
     */
    @JSONField(name = "en-US")
    private MultiLanguageBase enUs;

    /**
     * 名称
     */
    @JSONField(name = "zh-CN")
    private MultiLanguageBase zhCn;

}
