package com.renpho.erp.pds.domain.category;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.jmolecules.ddd.types.AggregateRoot;
import org.jmolecules.ddd.types.Identifiable;
import org.jmolecules.ddd.types.Identifier;

import java.util.List;

/**
 * 品类聚合根.
 *
 * <AUTHOR>
 * @since 2024.9.19
 */
@Data
public class PdsCategory implements AggregateRoot<PdsCategory, PdsCategory.PdsCategoryID> {

    private PdsCategory.PdsCategoryID id;

    /**
     * 品类代码
     */
    private String cateCode;

    /**
     * 父级品类主键ID
     */
    private Integer parentId;

    /**
     * SN码生成系数
     */
    private Integer snCoefficient;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;

    /**
     * 参数状态 1：Active，0：Disabled 单选框，必选，默认Active
     */
    private Integer status;

    /**
     * 多语言
     */
    private List<LanguageCategory> names;


    public PdsCategory() {

    }

    @RequiredArgsConstructor(staticName = "of")
    public static class PdsCategoryID implements Identifiable<Integer>, Identifier {

        private final Integer id;

        @Override
        public Integer getId() {
            return id;
        }

    }
}
