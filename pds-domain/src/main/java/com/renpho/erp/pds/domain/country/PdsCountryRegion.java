package com.renpho.erp.pds.domain.country;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.jmolecules.ddd.types.AggregateRoot;
import org.jmolecules.ddd.types.Identifiable;
import org.jmolecules.ddd.types.Identifier;

import java.util.List;

/**
 * 国家地区表.
 * <AUTHOR>
 * @since 2024.9.24
 */
@Data
public class PdsCountryRegion implements AggregateRoot<PdsCountryRegion, PdsCountryRegion.PdsCountryRegionID> {

    private PdsCountryRegionID id;

    /**
     * 国家代码
     */
    private String code;

    /**
     * 备注
     */
    private String remark;

    /**
     * 参数状态 1：Active，0：Disabled 单选框，必选，默认Active
     */
    private Integer status;

    /**
     * 多语言
     */
    private List<LanguageCountryRegion> names;

    @RequiredArgsConstructor(staticName = "of")
    public static class PdsCountryRegionID implements Identifiable<Integer>, Identifier {

        private final Integer id;

        @Override
        public Integer getId() {
            return id;
        }

    }

}