package com.renpho.erp.pds.domain.common;

import java.lang.annotation.*;

/**
 * Redisson锁注解.
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface LockRedisson {

    /**
     * 锁的键，支持 SpEL 表达式
     */
    String[] keys();

    /**
     * 锁的过期时间（毫秒）
     */
    long expire() default 30000;

    /**
     * 获取锁的最大等待时间（毫秒）
     */
    long acquireTimeout() default 1000;
}

