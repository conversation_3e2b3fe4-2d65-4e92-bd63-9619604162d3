package com.renpho.erp.pds.domain.common;

/**
 * 消息队列常量
 * <AUTHOR>
 * @since 2024/12/10
 */
public interface MQConstants {

	/**
	 * 推送供应商到NS的topic
	 */
	String SUPPLIER_PUSH_NS_TOPIC = "supplierSyncNSProducer-out-0";

	/**
     * 推送采购供应商信息到PDS的topic
	 */
	String BUSINESS_PUSH_PDS_TOPIC = "bmSyncPdsProducer-out-0";

	/**
	 * 推送PDS产品管理基础信息到产品商务管理的topic
	 */
	String PDS_PUSH_BUSINESS_TOPIC = "pdsSyncBmProducer-out-0";


	/**
	 * bpm审核回调topic
	 */
	String PDS_PSKU_BPM_AUDIT_TOPIC = "pds-product-bpm-audit-topic";
}
