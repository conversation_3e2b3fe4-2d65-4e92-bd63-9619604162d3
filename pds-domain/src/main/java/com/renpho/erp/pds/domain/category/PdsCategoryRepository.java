package com.renpho.erp.pds.domain.category;

/**
 * 品类操作接口.
 *
 * <AUTHOR>
 * @since 2024.9.19
 */
public interface PdsCategoryRepository {

	/**
	 * 添加品类数据
	 * @param pdsCategory 品类数据
	 * @return 主键id
	 */
	Integer savePdsCategory(PdsCategory pdsCategory);

	/**
	 * 更新品类数据
	 * @param pdsCategory 品类数据
	 * @return 受影响条数
	 */
	Integer updatePdsCategory(PdsCategory pdsCategory);

	/**
	 * 移动顺序
	 * @param categoryId 要移动的品类Id
	 * @param moveUp 是否上移
	 */
	void moveCategory(int categoryId, boolean moveUp);

}
