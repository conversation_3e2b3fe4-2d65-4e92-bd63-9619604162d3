package com.renpho.erp.pds.domain.common;

import lombok.Getter;

/**
 * 数据状态.
 * <AUTHOR>
 * @since 2024.9.23
 */
@Getter
public enum PdsCommonStatus {

	/**
	 * Active
	 */
	Active("Active", 1),

	/**
	 * Inactive
	 */
	Inactive("Inactive", 0);

	/**
	 * 名称
	 */
	private final String name;

	/**
	 * 值
	 */
	private final Integer value;

	PdsCommonStatus(String name, Integer value) {
		this.name = name;
		this.value = value;
	}

	/**
	 * 状态寻找
	 * @param value 下标
	 * @return 有效状态
	 */
	public static PdsCommonStatus getPdsCommonStatus(Integer value) {
		if (value == null) {
			return PdsCommonStatus.Active;
		}
        for (PdsCommonStatus pdsCommonStatus : PdsCommonStatus.values()) {
			if (pdsCommonStatus.getValue().equals(value)) {
				return pdsCommonStatus;
			}
		}
		return PdsCommonStatus.Inactive;
	}

}
