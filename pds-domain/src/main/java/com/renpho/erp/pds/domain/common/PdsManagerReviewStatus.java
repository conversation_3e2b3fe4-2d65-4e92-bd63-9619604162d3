package com.renpho.erp.pds.domain.common;

import lombok.Getter;

/**
 * 产品管理-审核状态.
 *
 * <AUTHOR>
 * @since 2024.11.04
 */
@Getter
public enum PdsManagerReviewStatus {

    /**
     * 草稿
     */
    DRAFT(0, "草稿", "Draft"),
    /**
     * 审核中
     */
    PENDING_REVIEW(1, "审核中", "Pending"),
    /**
     * 审核通过
     */
    APPROVED(2, "审核通过", "Actived"),
    /**
     * 审核不通过
     */
    REJECTED(3, "审核不通过", "Inactive");

    private final int code;
    private final String description;
    private final String descEn;

    PdsManagerReviewStatus(int code, String description, String descEn) {
        this.code = code;
        this.description = description;
        this.descEn = descEn;
    }

    @Override
    public String toString() {
        return code + " - " + description;
    }

    public static PdsManagerReviewStatus getEnum(int code) {
        for (PdsManagerReviewStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return null;
    }

    /**
     * 是否允许提交
     */
    public boolean canSubmit() {
        return this == DRAFT;
    }

    /**
     * 是否允许编辑
     */
    public boolean canEdit() {
        return this == DRAFT || this == REJECTED || this == APPROVED;
    }

}

