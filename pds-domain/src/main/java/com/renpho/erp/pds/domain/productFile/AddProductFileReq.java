package com.renpho.erp.pds.domain.productFile;


import com.renpho.erp.pds.domain.common.PdsSysTypes;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: Wyatt
 * @Date: 2024-12-03 12:21:51
 * @Description: 新增产品视觉传达请求参数
 **/
@Data
public class AddProductFileReq implements Serializable {

    private Integer productManagerId;

    @NotEmpty
    private List<ProductFileCmd> fileList;

    private PdsSysTypes pdsSysTypes;

    /**
     * 图片限制
     */
    private Integer pictureNumberLimit;

    /**
     * 图片限制
     */
    private Integer videoNumberLimit;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ProductFileCmd implements Serializable {
        private String fileDetailId;
        private String fileDimension;
        private Integer fileType;
        private String originalFilename;
    }

}
