package com.renpho.erp.pds.domain.product.manager.authorize;

import lombok.Getter;

/**
 * 授权角色类型.
 *
 * <AUTHOR>
 * @since 2024.10.24
 */
@Getter
public enum RoleTypeEnum {

    /**
     * 运营管理
     */
    MANAGER("运营管理", 0),
    /**
     * 运营人员
     */
    STAFF("运营人员", 1);

    /**
     * 名称
     */
    private final String name;

    /**
     * 值
     */
    private final Integer value;

    RoleTypeEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    /**
     * 根据编码获取对应的语言环境
     * @param value 编码
     * @return 编码获取对应的语言环境
     */
    public static RoleTypeEnum getEnum(Integer value) {
        for (RoleTypeEnum e : RoleTypeEnum.values()) {
            if (e.value.equals(value)) {
                return e;
            }
        }
        return RoleTypeEnum.MANAGER;
    }

}
