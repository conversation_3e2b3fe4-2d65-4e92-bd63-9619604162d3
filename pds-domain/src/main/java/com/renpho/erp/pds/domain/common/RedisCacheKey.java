package com.renpho.erp.pds.domain.common;

/**
 * redis 缓存key统一管理
 *
 * <AUTHOR>
 * @since 2024.11.28
 */
public class RedisCacheKey {

    /**
     * 认证资料的缓存key
     */
    private final static String PDS_PM_CERT = "PDS:PM:CERT";

    /**
     * 包材信息的缓存key
     */
    private final static String PDS_PM_PACKAGE = "PDS:PM:CERT";

    /**
     * 产品管理授权的缓存key
     */
    private final static String PDS_PM_AUTHORIZE = "PDS:PM:AUTHORIZE";


    /**
     * 获取产品管理-认证资料的缓存key
     * @param productManagerId 产品管理主键ID
     * @return 认证资料的缓存key
     */
    public static String getPdsPmCertKey(Integer productManagerId){
        return PDS_PM_CERT+":"+productManagerId;
    }

    /**
     * 获取产品管理-包材信息的缓存key
     * @param productManagerId 产品管理主键ID
     * @return 认证资料的缓存key
     */
    public static String getPdsPmPackageKey(Integer productManagerId){
        return PDS_PM_PACKAGE+":"+productManagerId;
    }

    /**
     * 获取产品管理-授权的缓存key
     * @param productManagerBasicId 产品管理主键ID
     * @param authorizedUserId 授权用户ID
     * @param roleType 授权类型
     * @return 产品管理-授权的缓存key
     */
    public static String getPdsPmAuthorizeKey(Integer productManagerBasicId, Integer authorizedUserId, Integer roleType) {
        return PDS_PM_AUTHORIZE + ":" + productManagerBasicId + ":" + authorizedUserId + ":" + roleType;
    }
}
