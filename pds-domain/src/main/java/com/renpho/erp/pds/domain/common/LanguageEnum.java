package com.renpho.erp.pds.domain.common;

import lombok.Getter;

import java.util.List;

/**
 * 语言环境.
 * <AUTHOR>
 * @since 2024.9.20
 */
@Getter
public enum LanguageEnum {

    /**
     * success
     */
    China("中文", 1),

    /**
     * error
     */
    English("英文", 0);

    /**
     * 名称
     */
    private final String name;

    /**
     * 值
     */
    private final Integer value;

    LanguageEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    /**
     * 根据编码获取对应的语言环境
     * @param value 编码
     * @return 编码获取对应的语言环境
     */
    public static LanguageEnum getEnum(Integer value) {
        if (value == null) {
            return LanguageEnum.China;
        }
        for (LanguageEnum e : LanguageEnum.values()) {
            if (e.value.equals(value)) {
                return e;
            }
        }
        return LanguageEnum.China;
    }

    /**
     * 根据语言环境获取语言信息
     *
     * @param language 语言
     * @param names 多语言
     * @return 语言信息
     */
    public static String getLanguage(List<? extends LanguageParent> names, LanguageEnum language) {
        if (LanguageEnum.China == language) {
            for (LanguageParent languageData : names) {
                if (languageData.getLanguage().equals("zh-CN")) {
                    return languageData.getName();
                }
            }
        } else {
            for (LanguageParent languageData : names) {
                if (languageData.getLanguage().equals("en-US")) {
                    return languageData.getName();
                }
            }
        }
        return null;
    }

}
