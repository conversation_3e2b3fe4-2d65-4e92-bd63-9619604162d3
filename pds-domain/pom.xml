<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.renpho.erp</groupId>
        <artifactId>pds-parent</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>pds-domain</artifactId>
    <name>${project.artifactId}</name>
    <description>领域层、模块</description>

    <dependencies>
        <!-- ==================================== -->
        <!-- the 3rd part -->
        <!-- ==================================== -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- ==================================== -->
        <!-- fastJson -->
        <!-- ==================================== -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>

        <!-- ==================================== -->
        <!-- 内部依赖 -->
        <!-- ==================================== -->
        <dependency>
            <groupId>com.renpho.soraka</groupId>
            <artifactId>soraka-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.renpho.erp</groupId>
            <artifactId>bpm-api</artifactId>
            <version>1.1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.renpho.karma</groupId>
            <artifactId>karma-dto</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
