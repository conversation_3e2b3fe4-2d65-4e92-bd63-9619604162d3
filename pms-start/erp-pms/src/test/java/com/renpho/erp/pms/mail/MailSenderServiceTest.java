package com.renpho.erp.pms.mail;

import com.renpho.erp.pms.Infrastructure.mail.MailAttachment;
import com.renpho.erp.pms.Infrastructure.mail.MailSenderService;
import com.renpho.erp.pms.Infrastructure.mail.PendShipmentPlanEmailService;
import com.renpho.erp.pms.Infrastructure.util.common.CustomBarcodePDFUtil;
import com.renpho.erp.pms.application.shipplan.service.ShipmentPlanOrderService;
import jakarta.mail.MessagingException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Set;

@SpringBootTest
@RunWith(SpringRunner.class)
public class MailSenderServiceTest {

    @Autowired
    private MailSenderService mailSenderService;

    @Autowired
    private ShipmentPlanOrderService shipmentPlanOrderService;

    @Test
    public void testSendMail() throws IOException {
        shipmentPlanOrderService.syncTrShipmentIdAndBoxMarkFile("PD2507290011", "123468", "1950480422734229505");
    }

    public MailAttachment generateExcelAttachment() {
        try (ByteArrayOutputStream baos = buildAttributes()) {
            String s = "FNSKU_100" + System.currentTimeMillis();
            return new MailAttachment(s, baos.toByteArray(), "application/pdf");
        } catch (IOException e) {
        }
        return null;
    }

    private ByteArrayOutputStream buildAttributes(){
        return CustomBarcodePDFUtil.generateFnskuPDF(70, 20, "1", "FNSKUAAAAA", 1);
    }
    @Test
    public void testSendSimpleMail() throws MessagingException, UnsupportedEncodingException {
        mailSenderService.sendWithAttachments(
                Set.of("<EMAIL>") ,
             Set.of("<EMAIL>") ,
            "单元测试：附件上传",
            "这是一封通过JUnit单元测试发出的邮件内容",
                List.of(generateExcelAttachment())
        );
    }

    @Autowired
    private PendShipmentPlanEmailService pipshipService;

    @Test
    public void testSendPdMail(){
        pipshipService.sendMail(60,List.of(114));
    }
}